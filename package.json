{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 5000", "build": "next build", "start": "next start --port 5000", "lint": "next lint", "postinstall": "npx puppeteer browsers install chrome"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.14.0", "@getbrevo/brevo": "^2.2.0", "@google/genai": "^0.8.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@mantine/core": "^6.0.21", "@mantine/dates": "^6.0.21", "@mantine/hooks": "^6.0.21", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@reduxjs/toolkit": "^2.8.1", "@sentry/nextjs": "^9.13.0", "@tabler/icons-react": "^3.33.0", "@tanstack/react-query": "^5.69.0", "@tanstack/react-table": "^8.21.2", "@types/file-saver": "^2.0.7", "@types/jszip": "^3.4.1", "@types/lodash": "^4.17.16", "@types/mime-types": "^2.1.4", "@types/node": "20.6.2", "@types/node-cron": "^3.0.11", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "@types/twilio": "^3.19.2", "@types/xlsx": "^0.0.35", "autoprefixer": "10.4.15", "bcryptjs": "^2.4.3", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "critters": "^0.0.25", "cross-env": "^7.0.3", "csv-writer": "^1.6.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dnd-multi-backend": "^7.0.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.3", "embla-carousel-react": "^8.3.0", "emoji-mart": "^5.6.0", "eslint": "8.49.0", "eslint-config-next": "15.2.3", "file-saver": "^2.0.5", "firebase-admin": "^13.4.0", "framer-motion": "^12.7.4", "input-otp": "^1.2.4", "jszip": "^3.10.1", "konva": "^9.3.20", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "lucide-react": "^0.446.0", "mantine-react-table": "^1.3.4", "mime-types": "^3.0.1", "mongodb": "^6.13.0", "mongoose": "^8.10.0", "next": "^15.2.3", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "node-cron": "^4.0.7", "pdf-lib": "^1.17.1", "phone": "^3.1.59", "postcss": "^8.5.3", "puppeteer": "^22.0.0", "rdndmb-html5-to-touch": "^9.0.0", "react": "18.2.0", "react-big-calendar": "^1.18.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-multi-backend": "^7.0.0", "react-dom": "18.2.0", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-json-view": "^1.21.3", "react-konva": "^19.0.3", "react-lib": "^0.1.3", "react-pdf": "^9.2.1", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.3", "react-to-print": "^3.1.0", "recharts": "^2.15.1", "shadcn-ui": "^0.9.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "swr": "^2.3.3", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.3", "twilio": "^5.5.1", "typescript": "5.2.2", "vaul": "^0.9.9", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/google.maps": "^3.58.1", "@types/lodash.isequal": "^4.5.8", "@types/next": "^8.0.7", "@types/puppeteer": "^7.0.4", "@types/uuid": "^10.0.0"}}