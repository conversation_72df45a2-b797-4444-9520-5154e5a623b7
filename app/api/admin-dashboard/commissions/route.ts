import { NextRequest } from 'next/server';
import Commission from '@/models/Commission';
import User from '@/models/User';
import Reservation from '@/models/Reservation';
import Branch from '@/models/Branch';
import { ObjectId } from 'mongodb';
import type { PipelineStage } from 'mongoose';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
await dbConnect();

  // Authenticate request (session or token)
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated) {
    return new Response(JSON.stringify({ error: auth.error }), {
      status: auth.error === 'Forbidden' ? 403 : 401
    });
  }

  const { userId, isSuperAdminUser: isSuperAdmin, isBranchesAdminUser: isBranchesAdmin } = auth;
  const { searchParams } = new URL(req.url);
  const timeframe = searchParams.get('timeframe') as 'today' | 'month' | 'year' || 'today';
  const branchId = searchParams.get('branchId');
  const branchIds = searchParams.get('branchIds')?.split(',').filter(Boolean);
  const allBranches = searchParams.get('allBranches') === 'true' || branchId === 'all' || (!branchId && !branchIds);

  // For debugging
  const debugEntries: any = {
    isSuperAdmin,
    isBranchesAdmin,
    userId,
    branchId,
    branchIds,
    allBranches,
    timeframe
  };

  // If branch admin and all branches selected, get accessible branches
  let accessibleBranchIds: string[] = [];
  if (allBranches && !isSuperAdmin && isBranchesAdmin && userId) {
    const branchesResponse = await Branch.find(
      { responsible: userId, deletedAt: null },
      '_id'
    ).lean();
    accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());

    debugEntries.accessibleBranchesCount = accessibleBranchIds.length;
    debugEntries.accessibleBranchIds = accessibleBranchIds;

    if (accessibleBranchIds.length === 0) {
      return new Response(
        JSON.stringify({
          global: {
            totalCommissions: 0,
            totalAmount: 0,
            topUser: null
          },
          branches: [],
          debug: process.env.NODE_ENV === 'development' ? {
            ...debugEntries,
            reason: 'No accessible branches found'
          } : undefined
        }),
        { status: 200 }
      );
    }
  }

  // Calculate date range
  const now = new Date();
  let start: Date, end: Date;
  if (timeframe === 'today') {
    start = new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate());
    end = new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999);
  } else if (timeframe === 'month') {
    start = new Date(now.getUTCFullYear(), now.getUTCMonth(), 1);
    end = new Date(now.getUTCFullYear(), now.getUTCMonth() + 1, 0, 23, 59, 59, 999);
  } else {
    start = new Date(now.getUTCFullYear(), 0, 1);
    end = new Date(now.getUTCFullYear(), 11, 31, 23, 59, 59, 999);
  }

  debugEntries.dateRange = { start, end };

  // Query for approved commissions in the date range
  // If approvedAt doesn't exist, fallback to createdAt (for legacy commissions)
  const match = {
    isApproved: true,
    $or: [
      { approvedAt: { $gte: start, $lte: end } },
      {
        approvedAt: { $exists: false },
        createdAt: { $gte: start, $lte: end }
      }
    ]
  };

  debugEntries.matchQuery = match;

  // Build branch filter based on role and branch selection
  let branchFilter = {};
  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    branchFilter = {
      'reservation.preferences.branchId': {
        $in: branchIds.map(id => {
          try {
            return new ObjectId(id);
          } catch {
            return id;
          }
        })
      }
    };
  } else if (!allBranches && branchId) {
    // Single branch selected (backward compatibility)
    branchFilter = { 'reservation.preferences.branchId': branchId };
  } else if (allBranches && !isSuperAdmin && accessibleBranchIds.length > 0) {
    // Branch admin with "all" selected - restrict to accessible branches
    branchFilter = {
      'reservation.preferences.branchId': {
        $in: accessibleBranchIds.map(id => {
          try {
            return new ObjectId(id);
          } catch {
            return id;
          }
        })
      }
    };
  }

  debugEntries.branchFilter = branchFilter;

  // Aggregate commissions joined to reservations for branch info
  const agg: PipelineStage[] = [
    { $match: match },
    {
      $lookup: {
        from: 'reservations',
        localField: 'reservationId',
        foreignField: '_id',
        as: 'reservation',
      },
    },
    { $unwind: '$reservation' },
    // Filter out deleted reservations
    { $match: { 'reservation.isDeleted': { $ne: true } } },
    { $match: branchFilter },
  ];

  debugEntries.aggregationPipeline = agg;

  // Global stats
  const globalAgg: PipelineStage[] = [
    ...agg,
    {
      $group: {
        _id: null,
        totalCommissions: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
      },
    },
  ];

  // Count total commissions in the database for debugging
  const totalCommissionsCount = await Commission.countDocuments();
  debugEntries.totalCommissionsInDB = totalCommissionsCount;

  // Count approved commissions for this date range
  const approvedCommissionsCount = await Commission.countDocuments(match);
  debugEntries.approvedCommissionsCount = approvedCommissionsCount;

  // For each accessible branch, check if there are any commissions
  if (accessibleBranchIds.length > 0) {
    const branchCommissionCounts = [];
    for (const branchId of accessibleBranchIds) {
      const count = await Reservation.countDocuments({
        'preferences.branchId': branchId,
        deletedAt: { $exists: false }
      });
      branchCommissionCounts.push({ branchId, reservationsCount: count });
    }
    debugEntries.branchReservationCounts = branchCommissionCounts;
  }

  const globalStats = await Commission.aggregate(globalAgg);
  debugEntries.globalStatsResult = globalStats;

  const global: any = {
    totalCommissions: globalStats[0]?.totalCommissions || 0,
    totalAmount: globalStats[0]?.totalAmount || 0,
  };

  // Top user globally
  const topUserAgg: PipelineStage[] = [
    ...agg,
    {
      $group: {
        _id: '$userId',
        total: { $sum: '$amount' },
      },
    },
    { $sort: { total: -1 as 1 | -1 } },
    { $limit: 1 },
  ];
  const topUserResult = await Commission.aggregate(topUserAgg);
  debugEntries.topUserResult = topUserResult;

  let topUser = null;
  if (topUserResult.length > 0) {
    const user: any = await User.findById(topUserResult[0]._id, '_id name').lean();
    if (user && typeof user === 'object' && !Array.isArray(user)) {
      topUser = { _id: user._id, name: user.name, total: topUserResult[0].total };
    }
  }
  global.topUser = topUser;

  // Per-branch stats (only if all branches)
  let branches = [];
  if (allBranches) {
    // For branch admin, only include accessible branches
    let branchQuery = {};
    if (!isSuperAdmin && accessibleBranchIds.length > 0) {
      branchQuery = { _id: { $in: accessibleBranchIds } };
    }

    // Group by branchId
    const branchAgg: PipelineStage[] = [
      ...agg,
      {
        $group: {
          _id: '$reservation.preferences.branchId',
          totalCommissions: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
        },
      },
    ];
    const branchStats = await Commission.aggregate(branchAgg);
    debugEntries.branchStatsResult = branchStats;

    // For each branch, get top user
    for (const b of branchStats) {
      // Skip if branch admin and not accessible
      if (!isSuperAdmin && accessibleBranchIds.length > 0) {
        const branchIdStr = b._id.toString();
        if (!accessibleBranchIds.includes(branchIdStr)) {
          continue; // Skip branches user doesn't have access to
        }
      }

      // Top user for this branch
      const branchUserAgg: PipelineStage[] = [
        ...agg,
        { $match: { 'reservation.preferences.branchId': b._id } },
        {
          $group: {
            _id: '$userId',
            total: { $sum: '$amount' },
          },
        },
        { $sort: { total: -1 as 1 | -1 } },
        { $limit: 1 },
      ];
      const branchTopUserResult = await Commission.aggregate(branchUserAgg);
      let branchTopUser = null;
      if (branchTopUserResult.length > 0) {
        const user: any = await User.findById(branchTopUserResult[0]._id, '_id name').lean();
        if (user && typeof user === 'object' && !Array.isArray(user)) {
          branchTopUser = { _id: user._id, name: user.name, total: branchTopUserResult[0].total };
        }
      }
      // Get branch name
      let branchName = String(b._id);
      const branchDoc = await Branch.findOne({ _id: b._id }, 'name').lean();
      if (branchDoc && typeof branchDoc === 'object' && !Array.isArray(branchDoc) && branchDoc.name) {
        branchName = branchDoc.name as string;
      }
      branches.push({
        branchId: b._id,
        branchName,
        totalCommissions: b.totalCommissions,
        totalAmount: b.totalAmount,
        topUser: branchTopUser,
      });
    }
  }

  // Debug info in development mode
  const debugInfo = {
    ...debugEntries,
    responseData: {
      global,
      branchesCount: branches.length
    }
  };

  return new Response(
    JSON.stringify({
      global,
      branches,
      debug: process.env.NODE_ENV === 'development' ? debugInfo : undefined
    }),
    { status: 200 }
  );
}