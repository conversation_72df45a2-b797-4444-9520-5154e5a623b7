import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import DashboardAccessToken from '@/models/DashboardAccessToken';
import { getUserRoles } from '../../utils/server-permission-utils';
import { isSuperAdmin } from '@/lib/utils/role-utils';
import { generateInvitationToken } from '@/app/invitations/utils/token-generator';
import dbConnect from '@/lib/db';

// Generate a unique dashboard access token
async function generateUniqueDashboardToken(): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    const token = generateInvitationToken(); // Reuse the same format
    
    // Check if token already exists
    const existingToken = await DashboardAccessToken.findOne({ token });
    if (!existingToken) {
      return token;
    }
    
    attempts++;
  }
  
  throw new Error('Failed to generate unique token after maximum attempts');
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }


    await dbConnect();
    // Check if user is superadmin
    const roles = await getUserRoles(session);
    if (!isSuperAdmin(roles)) {
      return NextResponse.json(
        { error: 'Only superadmin can generate dashboard access tokens' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { description, expiresAt } = body;

    // Generate unique token
    const token = await generateUniqueDashboardToken();

    // Create token document
    const dashboardToken = new DashboardAccessToken({
      token,
      createdBy: session.user.id,
      description: description || '',
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      isActive: true
    });

    await dashboardToken.save();

    return NextResponse.json({
      success: true,
      token: dashboardToken.token,
      id: dashboardToken._id,
      createdAt: dashboardToken.createdAt,
      expiresAt: dashboardToken.expiresAt,
      description: dashboardToken.description
    });

  } catch (error: any) {
    console.error('Error generating dashboard access token:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to generate token' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

  await dbConnect();
    
    // Check if user is superadmin
    const roles = await getUserRoles(session);
    if (!isSuperAdmin(roles)) {
      return NextResponse.json(
        { error: 'Only superadmin can view dashboard access tokens' },
        { status: 403 }
      );
    }

    // Get all tokens with creator info
    const tokens = await DashboardAccessToken.find({})
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 });

    return NextResponse.json({
      success: true,
      tokens: tokens.map(token => ({
        id: token._id,
        token: token.token,
        description: token.description,
        createdBy: token.createdBy,
        createdAt: token.createdAt,
        expiresAt: token.expiresAt,
        isActive: token.isActive,
        lastUsedAt: token.lastUsedAt
      }))
    });

  } catch (error: any) {
    console.error('Error fetching dashboard access tokens:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch tokens' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

  await dbConnect();
    
    // Check if user is superadmin
    const roles = await getUserRoles(session);
    if (!isSuperAdmin(roles)) {
      return NextResponse.json(
        { error: 'Only superadmin can revoke dashboard access tokens' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(req.url);
    const tokenId = searchParams.get('tokenId');

    if (!tokenId) {
      return NextResponse.json(
        { error: 'Token ID is required' },
        { status: 400 }
      );
    }

    // Deactivate the token instead of deleting it
    const result = await DashboardAccessToken.findByIdAndUpdate(
      tokenId,
      { isActive: false },
      { new: true }
    );

    if (!result) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Token revoked successfully'
    });

  } catch (error: any) {
    console.error('Error revoking dashboard access token:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to revoke token' },
      { status: 500 }
    );
  }
}
