import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { getDailyStatsEmailTemplate, processStatsEmailTemplate, StatsEmailData } from '@/lib/utils/stats-email-templates';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import { getBrevoSender } from '@/lib/brevo/brevoUtils';
import { StatsExportService } from '@/lib/services/stats-export-service';
import dbConnect from '@/lib/db';

/**
 * Debug API endpoint to send test emails with the stats email template
 * Only available in development environment for SuperAdmin users
 */
export async function POST(req: NextRequest) {
  // Environment check - only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints are only available in development environment' },
      { status: 403 }
    );
  }

  // Authentication check
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
  await dbConnect();
    const body = await req.json();
    const { email, templateData } = body;

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { error: 'Email address is required' },
        { status: 400 }
      );
    }

    if (!templateData) {
      return NextResponse.json(
        { error: 'Template data is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Generate email template
    const template = getDailyStatsEmailTemplate(templateData as StatsEmailData);
    const processedTemplate = processStatsEmailTemplate(template, templateData as StatsEmailData);

    // Send test email via Brevo
    const brevoSender = getBrevoSender();
    const success = await sendBrevoTemplatedEmail({
      to: [{ email }],
      subject: `[TEST] Statistiques quotidiennes - ${templateData.date}`,
      content: processedTemplate,
      sender: {
        email: brevoSender.email,
        name: 'AMQ Partners - Test Email'
      }
    });

    if (success) {
      return NextResponse.json({
        success: true,
        message: `Test email sent successfully to ${email}`,
        debug: {
          templateSize: processedTemplate.length,
          selectedStats: templateData.selectedStats,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      return NextResponse.json(
        { 
          error: 'Failed to send test email',
          message: 'Email service returned an error'
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to process test email request'
      },
      { status: 500 }
    );
  }
}

/**
 * Debug API endpoint to validate template generation without sending email
 */
export async function GET(req: NextRequest) {
  // Environment check - only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints are only available in development environment' },
      { status: 403 }
    );
  }

  // Authentication check
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
  await dbConnect();

    const testDate = new Date().toISOString().split('T')[0];
    const selectedStats = ['totalSales', 'totalReservationsScheduled', 'totalReservationsCreated', 'totalPresence'];

    // Generate sample data for template validation
    const sampleStatsData = {
      totalSales: { amount: 12345.67, count: 89 },
      totalReservations: 23,
      totalPresence: 156,
      salesByBranch: [
        { branchName: 'Test Branch 1', amount: 8000.00, count: 50 },
        { branchName: 'Test Branch 2', amount: 4345.67, count: 39 }
      ],
      reservationsByBranch: [
        { branchName: 'Test Branch 1', count: 15 },
        { branchName: 'Test Branch 2', count: 8 }
      ],
      presenceByBranch: [
        { branchName: 'Test Branch 1', count: 95 },
        { branchName: 'Test Branch 2', count: 61 }
      ],
      topSellers: [
        { name: 'Test Seller 1', amount: 3000.00, count: 20 },
        { name: 'Test Seller 2', amount: 2500.00, count: 18 }
      ],
      topPaps: [
        { name: 'Test PAP 1', count: 25 },
        { name: 'Test PAP 2', count: 20 }
      ]
    };

    // Generate export files and secure download URLs
    const exportService = StatsExportService.getInstance();

    // Generate CSV export
    const csvExportResult = await exportService.generateExport(sampleStatsData, {
      format: 'csv',
      date: testDate,
      selectedStats,
      emailSettingId: 'debug-template-test'
    });

    // Generate PDF export
    const pdfExportResult = await exportService.generateExport(sampleStatsData, {
      format: 'pdf',
      date: testDate,
      selectedStats,
      emailSettingId: 'debug-template-test'
    });

    const sampleData: StatsEmailData = {
      date: testDate,
      totalSales: sampleStatsData.totalSales,
      totalReservationsScheduled: sampleStatsData.totalReservationsScheduled,
      totalReservationsCreated: sampleStatsData.totalReservationsCreated,
      totalPresence: sampleStatsData.totalPresence,
      salesByBranch: sampleStatsData.salesByBranch,
      reservationsByBranch: sampleStatsData.reservationsByBranch,
      presenceByBranch: sampleStatsData.presenceByBranch,
      topSellers: sampleStatsData.topSellers,
      topPaps: sampleStatsData.topPaps,
      selectedStats,
      exportUrls: {
        csv: csvExportResult.success ? csvExportResult.downloadUrl! : '#csv-export-error',
        pdf: pdfExportResult.success ? pdfExportResult.downloadUrl! : '#pdf-export-error'
      }
    };

    // Generate template
    const template = getDailyStatsEmailTemplate(sampleData);
    const processedTemplate = processStatsEmailTemplate(template, sampleData);

    return NextResponse.json({
      success: true,
      templateSize: processedTemplate.length,
      sampleData,
      debug: {
        hasLogo: processedTemplate.includes('company-logo'),
        hasExportButtons: processedTemplate.includes('export-button'),
        sectionsIncluded: sampleData.selectedStats.length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error validating template:', error);
    return NextResponse.json(
      { 
        error: 'Template validation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
