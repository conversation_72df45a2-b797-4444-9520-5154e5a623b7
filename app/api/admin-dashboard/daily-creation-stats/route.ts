import { NextRequest } from 'next/server';
import Reservation from '@/models/Reservation';
import Branch from '@/models/Branch';
import { ObjectId } from 'mongodb';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { createQuebecDate, QUEBEC_TIMEZONE } from '@/app/admin-dashboard/utils/timezone-utils';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
await dbConnect();

  // Authenticate request (session or token)
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated) {
    return new Response(JSON.stringify({ error: auth.error }), {
      status: auth.error === 'Forbidden' ? 403 : 401
    });
  }

  const { searchParams } = new URL(req.url);
  const branchId = searchParams.get('branchId');
  const branchIds = searchParams.get('branchIds')?.split(',').filter(Boolean);
  const allBranches = searchParams.get('allBranches') === 'true' || branchId === 'all';
  const startDate = searchParams.get('startDate') || '';
  const endDate = searchParams.get('endDate') || '';
  const groupBy = searchParams.get('groupBy') || 'day'; // day, week, month

  const { userId, isSuperAdminUser, isBranchesAdminUser } = auth;

  // If branch admin and all branches selected, get accessible branches
  let accessibleBranchIds: string[] = [];
  if (allBranches && !isSuperAdminUser && isBranchesAdminUser && userId) {
    const branchesResponse = await Branch.find(
      { responsible: userId, deletedAt: null },
      '_id'
    ).lean();
    accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());
    
    if (accessibleBranchIds.length === 0) {
      return new Response(
        JSON.stringify({ 
          stats: [], 
          visitDateStats: [],
          totals: {
            totalReservations: 0,
            totalVisitReservations: 0
          },
          branchMap: {},
          debug: process.env.NODE_ENV === 'development' ? {
            isSuperAdmin: isSuperAdminUser,
            isBranchesAdmin: isBranchesAdminUser,
            accessibleBranchIds: [],
            branchId,
            branchIds,
            allBranches
          } : undefined
        }),
        { status: 200 }
      );
    }
  }

  // Build branch filter based on role and branch selection
  let branchFilter: any = {};
  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    branchFilter['preferences.branchId'] = { $in: branchIds };
  } else if (!allBranches && branchId && branchId !== 'all') {
    // Single branch selected (backward compatibility)
    branchFilter['preferences.branchId'] = branchId;
  } else if (allBranches && !isSuperAdminUser && accessibleBranchIds.length > 0) {
    // Branch admin with "all" selected - restrict to accessible branches
    branchFilter['preferences.branchId'] = { $in: accessibleBranchIds };
  }

  try {
    // Determine the group by format based on the groupBy parameter
    let dateFormat;
    
    switch (groupBy) {
      case 'week':
        // Group by week - using ISO week
        dateFormat = '%Y-W%U'; // Year-Week format
        break;
      case 'month':
        // Group by month
        dateFormat = '%Y-%m';
        break;
      default:
        // Default to daily grouping
        dateFormat = '%Y-%m-%d';
    }
    
    // Base match criteria for non-deleted reservations
    const baseMatch = {
      $match: {
        $or: [
          { deletedAt: { $exists: false } },
          { deletedAt: null }
        ],
        ...branchFilter
      }
    };

    // Date range filter - two approaches:
    // 1. For creation date stats, filter by createdAt
    // 2. For consistency with other stats, also filter by preferences.visitDate
    let creationDatePipeline: any[] = [];
    let visitDatePipeline: any[] = [];
    
    if (startDate && endDate) {
      // Creation date filter - use Quebec timezone for proper date filtering
      // This ensures reservations created on a specific Quebec date are correctly included
      const parsedStartDate = createQuebecDate(startDate, false);
      const parsedEndDate = createQuebecDate(endDate, true);

      creationDatePipeline = [
        {
          ...baseMatch,
          $match: {
            ...baseMatch.$match,
            createdAt: {
              $gte: parsedStartDate,
              $lte: parsedEndDate
            }
          }
        },
        {
          $addFields: {
            creationDateFormatted: {
              $dateToString: {
                format: dateFormat,
                date: "$createdAt",
                timezone: QUEBEC_TIMEZONE // Use Quebec timezone for consistent date grouping
              }
            }
          }
        }
      ];
      
      // Visit date filter - for consistency with other stats
      visitDatePipeline = [
        {
          ...baseMatch,
          $match: {
            ...baseMatch.$match,
            $expr: {
              $and: [
                {
                  $gte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    startDate
                  ]
                },
                {
                  $lte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    endDate
                  ]
                }
              ]
            }
          }
        },
        {
          $addFields: {
            visitDateFormatted: { 
              $substr: ['$preferences.visitDate', 0, 10]
            }
          }
        }
      ];
    } else {
      // No date filter, use all dates
      creationDatePipeline = [
        baseMatch,
        {
          $addFields: {
            creationDateFormatted: { 
              $dateToString: { 
                format: dateFormat, 
                date: "$createdAt",
                timezone: "UTC" 
              } 
            }
          }
        }
      ];
      
      visitDatePipeline = [
        baseMatch,
        {
          $addFields: {
            visitDateFormatted: { 
              $substr: ['$preferences.visitDate', 0, 10]
            }
          }
        }
      ];
    }
    
    // Optionally add branch tracking stage
    if (allBranches || branchId === 'all' || (branchIds && branchIds.length > 0)) {
      creationDatePipeline.push({
        $addFields: {
          branchIdField: "$preferences.branchId"
        }
      });
      
      visitDatePipeline.push({
        $addFields: {
          branchIdField: "$preferences.branchId"
        }
      });
    }
    
    // Group and sort for creation date pipeline
    creationDatePipeline.push(
      {
        $group: {
          _id: "$creationDateFormatted",
          count: { $sum: 1 }
        }
      },
      {
        $sort: { 
          _id: 1
        } 
      }
    );
    
    // Group and sort for visit date pipeline
    visitDatePipeline.push(
      {
        $group: {
          _id: "$visitDateFormatted",
          count: { $sum: 1 }
        }
      },
      {
        $sort: { 
          _id: 1
        } 
      }
    );
    
    // Execute both pipelines
    const creationStats = await Reservation.aggregate(creationDatePipeline);
    const visitDateStats = await Reservation.aggregate(visitDatePipeline);
    
    // Get total reservations for visit date range (consistent with summary cards)
    const totalVisitReservations = visitDateStats.reduce((sum, stat) => sum + stat.count, 0);
    
    // Get branch names if we're showing multiple branches
    let branchMap = {};
    if (allBranches || branchId === 'all' || (branchIds && branchIds.length > 0)) {
      // Use the already fetched branches if available, otherwise fetch all branches
      if (accessibleBranchIds.length > 0 && !isSuperAdminUser) {
        const branches = await Branch.find(
          { _id: { $in: accessibleBranchIds } }, 
          '_id name'
        ).lean();
        branchMap = Object.fromEntries(
          branches.map((b: any) => [b._id.toString(), b.name])
        );
      } else {
        // For superadmin, fetch all branches
        const branches = await Branch.find({}, '_id name').lean();
        branchMap = Object.fromEntries(
          branches.map((b: any) => [b._id.toString(), b.name])
        );
      }
    }

    // Debug info in development mode
    const debugInfo = process.env.NODE_ENV === 'development' ? {
      isSuperAdmin: isSuperAdminUser,
      isBranchesAdmin: isBranchesAdminUser,
      branchId,
      branchIds,
      allBranches,
      accessibleBranchIds: accessibleBranchIds.length > 0 ? accessibleBranchIds : 'N/A',
      branchFilter,
      startDate,
      endDate,
      groupBy
    } : undefined;

    return new Response(
      JSON.stringify({
        stats: creationStats,
        visitDateStats,
        totals: {
          totalReservations: creationStats.reduce((sum, stat) => sum + stat.count, 0),
          totalVisitReservations
        },
        branchMap,
        debug: debugInfo
      }),
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in daily creation stats API:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to fetch daily creation stats' }),
      { status: 500 }
    );
  }
} 