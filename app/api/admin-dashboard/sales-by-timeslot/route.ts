import { NextRequest } from 'next/server';
import Reservation from '@/models/Reservation';
import Branch from '@/models/Branch';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { getSalesStatusCodes } from '@/lib/utils/reservation-status-utils';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
await dbConnect();

  // Authenticate request (session or token)
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated) {
    return new Response(JSON.stringify({ error: auth.error }), {
      status: auth.error === 'Forbidden' ? 403 : 401
    });
  }

  const { searchParams } = new URL(req.url);
  const branchId = searchParams.get('branchId');
  const branchIds = searchParams.get('branchIds')?.split(',').filter(Boolean);
  const allBranches = searchParams.get('allBranches') === 'true' || branchId === 'all';
  const startDate = searchParams.get('startDate') || '';
  const endDate = searchParams.get('endDate') || '';

  const { userId, isSuperAdminUser, isBranchesAdminUser } = auth;

  try {
    // If branch admin and all branches selected, get accessible branches
    let accessibleBranchIds: string[] = [];
    if (allBranches && !isSuperAdminUser && isBranchesAdminUser && userId) {
      const branchesResponse = await Branch.find(
        { responsible: userId, deletedAt: null },
        '_id'
      ).lean();
      accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());
      
      if (accessibleBranchIds.length === 0) {
        // Return empty stats if branch admin has no branches
        const emptyStats = Array.from({ length: 7 }, (_, i) => ({
          dayIndex: i,
          dayName: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][i],
          dayNameFr: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'][i],
          timeslots: {
            '11:00': 0,
            '13:00': 0,
            '15:00': 0,
            '17:00': 0,
            '19:00': 0
          }
        }));
        
        return new Response(
          JSON.stringify({ 
            stats: emptyStats, 
            totals: {
              totalSales: 0,
              totalAmount: 0,
            },
            debug: process.env.NODE_ENV === 'development' ? { 
              accessibleBranchIds: [], 
              isSuperAdmin: isSuperAdminUser,
              isBranchesAdmin: isBranchesAdminUser,
              allBranches,
              branchId,
              branchIds
            } : undefined
          }),
          { status: 200 }
        );
      }
    }

    // Build branch filter
    let branchFilter: any = {};
    if (allBranches) {
      if (!isSuperAdminUser && isBranchesAdminUser && accessibleBranchIds.length > 0) {
        branchFilter = { $in: accessibleBranchIds.map(id => id) };
      }
      // For super admin with all branches, no filter needed
    } else if (branchIds && branchIds.length > 0) {
      branchFilter = { $in: branchIds };
    } else if (branchId && branchId !== 'all') {
      branchFilter = branchId;
    }

    // Build date filter
    let dateFilter: any = {};
    if (startDate && endDate) {
      dateFilter = {
        $gte: startDate,
        $lte: endDate
      };
    }

    // Get sales status codes and build match query
    const salesStatusCodes = await getSalesStatusCodes();
    const match: any = {
      deletedAt: null,
      status: { $in: salesStatusCodes }, // Only include reservations with sales status
    };

    if (Object.keys(branchFilter).length > 0) {
      match['preferences.branchId'] = branchFilter;
    }

    if (Object.keys(dateFilter).length > 0) {
      match['preferences.visitDate'] = dateFilter;
    }

    // Aggregate sales by day of week and time slot
    const salesStats = await Reservation.aggregate([
      { $match: match },
      {
        $addFields: {
          visitDateObj: { $dateFromString: { dateString: '$preferences.visitDate' } },
          visitTime: '$preferences.visitTime',
          // Extract the start time from "HH:MM-HH:MM" format
          startTime: {
            $arrayElemAt: [
              { $split: ['$preferences.visitTime', '-'] },
              0
            ]
          }
        }
      },
      {
        $addFields: {
          dayOfWeek: { $dayOfWeek: '$visitDateObj' }, // 1=Sunday, 2=Monday, etc.
          timeSlot: {
            $switch: {
              branches: [
                { case: { $eq: ['$startTime', '11:00'] }, then: '11:00' },
                { case: { $eq: ['$startTime', '13:00'] }, then: '13:00' },
                { case: { $eq: ['$startTime', '15:00'] }, then: '15:00' },
                { case: { $eq: ['$startTime', '17:00'] }, then: '17:00' },
                { case: { $eq: ['$startTime', '19:00'] }, then: '19:00' }
              ],
              default: 'Other'
            }
          }
        }
      },
      {
        $match: {
          timeSlot: { $ne: 'Other' } // Only include known time slots
        }
      },
      {
        $group: {
          _id: {
            dayOfWeek: '$dayOfWeek',
            timeSlot: '$timeSlot',
            branchId: allBranches ? '$preferences.branchId' : null
          },
          salesCount: { $sum: 1 },
          totalAmount: { $sum: '$sellingAmount' }
        }
      },
      { $sort: { '_id.dayOfWeek': 1, '_id.timeSlot': 1 } }
    ]);

    // Transform to format needed by the frontend
    const dayLabels = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayLabelsFr = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    const timeSlots = ['11:00', '13:00', '15:00', '17:00', '19:00'];
    
    // Initialize array with all days and time slots
    const stats = Array.from({ length: 7 }, (_, i) => ({
      dayIndex: i,
      dayName: dayLabels[i],
      dayNameFr: dayLabelsFr[i],
      timeslots: Object.fromEntries(timeSlots.map(slot => [slot, allBranches ? {} : 0]))
    }));

    // Fill in the actual data
    salesStats.forEach(stat => {
      const dayIndex = stat._id.dayOfWeek - 1; // Convert to 0-based index
      const timeSlot = stat._id.timeSlot;
      
      if (dayIndex >= 0 && dayIndex < 7 && timeSlots.includes(timeSlot)) {
        if (allBranches && stat._id.branchId) {
          // For all branches, store by branch ID
          if (!stats[dayIndex].timeslots[timeSlot]) {
            stats[dayIndex].timeslots[timeSlot] = {};
          }
          stats[dayIndex].timeslots[timeSlot][stat._id.branchId] = stat.salesCount;
        } else {
          // For single branch, store directly
          stats[dayIndex].timeslots[timeSlot] = stat.salesCount;
        }
      }
    });

    // Calculate totals
    const totals = {
      totalSales: salesStats.reduce((sum, stat) => sum + stat.salesCount, 0),
      totalAmount: salesStats.reduce((sum, stat) => sum + stat.totalAmount, 0)
    };

    // Debug info in development mode
    const debugInfo = process.env.NODE_ENV === 'development' ? {
      isSuperAdmin: isSuperAdminUser,
      isBranchesAdmin: isBranchesAdminUser,
      branchId,
      branchIds,
      allBranches,
      accessibleBranchIds: accessibleBranchIds.length > 0 ? accessibleBranchIds : 'N/A',
      branchFilter,
      startDate,
      endDate,
      matchQuery: match
    } : undefined;

    return new Response(JSON.stringify({ 
      stats, 
      totals,
      debug: debugInfo
    }), { status: 200 });
    
  } catch (error) {
    console.error('Error in sales by timeslot API:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to fetch sales by timeslot data' }),
      { status: 500 }
    );
  }
}
