import { NextRequest } from 'next/server';
import Reservation from '@/models/Reservation';
import { ObjectId } from 'mongodb';
import Branch from '@/models/Branch';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { getSalesStatusCodes, getPresenceStatusCodes } from '@/lib/utils/reservation-status-utils';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
await dbConnect();

  // Authenticate request (session or token)
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated) {
    return new Response(JSON.stringify({ error: auth.error }), {
      status: auth.error === 'Forbidden' ? 403 : 401
    });
  }

  const { searchParams } = new URL(req.url);
  const branchId = searchParams.get('branchId');
  const branchIds = searchParams.get('branchIds')?.split(',').filter(Boolean);
  const allBranches = searchParams.get('allBranches') === 'true' || branchId === 'all';
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');
  const type = searchParams.get('type'); // 'presence' or 'sales'

  if (!type || (type !== 'presence' && type !== 'sales')) {
    return new Response(JSON.stringify({ error: 'Missing or invalid type parameter' }), { status: 400 });
  }

  const { userId, isSuperAdminUser, isBranchesAdminUser } = auth;

  // If branch admin and all branches selected, get accessible branches
  let accessibleBranchIds: string[] = [];
  if (allBranches && !isSuperAdminUser && isBranchesAdminUser && userId) {
    const branchesResponse = await Branch.find(
      { responsible: userId, deletedAt: null },
      '_id'
    ).lean();
    accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());
    
    if (accessibleBranchIds.length === 0) {
      return new Response(
        JSON.stringify({ total: 0, count: 0, rate: 0 }),
        { status: 200 }
      );
    }
  }

  // Build branch filter based on role and branch selection
  let branchFilter = {};
  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    branchFilter = { 'preferences.branchId': { $in: branchIds } };
  } else if (!allBranches && branchId) {
    // Single branch selected (backward compatibility)
    branchFilter = { 'preferences.branchId': branchId };
  } else if (allBranches && !isSuperAdminUser && accessibleBranchIds.length > 0) {
    // Branch admin with "all" selected - restrict to accessible branches
    branchFilter = { 'preferences.branchId': { $in: accessibleBranchIds } };
  }

  // Build date filter
  const dateFilter = startDate && endDate
    ? {
        $expr: {
          $and: [
            {
              $gte: [
                { $substr: ['$preferences.visitDate', 0, 10] },
                startDate
              ]
            },
            {
              $lte: [
                { $substr: ['$preferences.visitDate', 0, 10] },
                endDate
              ]
            }
          ]
        }
      }
    : {};

  const baseAgg = [
    {
      $match: {
        $or: [
          { deletedAt: { $exists: false } },
          { deletedAt: null }
        ],
        ...branchFilter,
        ...dateFilter
      }
    }
  ];

  // Total reservations
  const total = await Reservation.aggregate([
    ...baseAgg,
    { $count: 'total' },
  ]).then(res => res[0]?.total || 0);

  // Presence or sales count
  let count = 0;
  if (type === 'presence') {
    const presenceStatusCodes = await getPresenceStatusCodes();
    count = await Reservation.aggregate([
      ...baseAgg,
      { $match: { status: { $in: presenceStatusCodes } } },
      { $count: 'count' },
    ]).then(res => res[0]?.count || 0);
  } else if (type === 'sales') {
    const salesStatusCodes = await getSalesStatusCodes();
    count = await Reservation.aggregate([
      ...baseAgg,
      { $match: { status: { $in: salesStatusCodes } } },
      { $count: 'count' },
    ]).then(res => res[0]?.count || 0);
  }

  // Calculate rate
  const rate = total > 0 ? Math.round((count / total) * 100) : 0;

  // Add debug info in development
  let debugInfo = {};
  if (process.env.NODE_ENV === 'development') {
    debugInfo = {
      isSuperAdmin: isSuperAdminUser,
      isBranchesAdmin: isBranchesAdminUser,
      branchId,
      branchIds,
      allBranches,
      accessibleBranchIds: accessibleBranchIds.length > 0 ? accessibleBranchIds : 'N/A',
      branchFilter,
      dateFilter,
      type
    };
  }

  return new Response(
    JSON.stringify({ 
      total, 
      count, 
      rate,
      debug: process.env.NODE_ENV === 'development' ? debugInfo : undefined
    }),
    { status: 200 }
  );
} 