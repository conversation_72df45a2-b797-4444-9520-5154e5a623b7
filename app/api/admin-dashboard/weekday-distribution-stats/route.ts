import { NextRequest } from 'next/server';
import Reservation from '@/models/Reservation';
import Branch from '@/models/Branch';
import { ObjectId } from 'mongodb';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
await dbConnect();

  // Authenticate request (session or token)
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated) {
    return new Response(JSON.stringify({ error: auth.error }), {
      status: auth.error === 'Forbidden' ? 403 : 401
    });
  }

  const { searchParams } = new URL(req.url);
  const branchId = searchParams.get('branchId');
  const branchIds = searchParams.get('branchIds')?.split(',').filter(Boolean);
  const allBranches = searchParams.get('allBranches') === 'true' || branchId === 'all';
  const startDate = searchParams.get('startDate') || '';
  const endDate = searchParams.get('endDate') || '';

  const { userId, isSuperAdminUser, isBranchesAdminUser } = auth;

  // If branch admin and all branches selected, get accessible branches
  let accessibleBranchIds: string[] = [];
  if (allBranches && !isSuperAdminUser && isBranchesAdminUser && userId) {
    const branchesResponse = await Branch.find(
      { responsible: userId, deletedAt: null },
      '_id'
    ).lean();
    accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());
    
    if (accessibleBranchIds.length === 0) {
      // Return empty stats if branch admin has no branches
      const emptyStats = Array.from({ length: 7 }, (_, i) => ({
        dayIndex: i,
        dayName: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][i],
        dayNameFr: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'][i],
        count: 0,
        sales: 0,
        presence: 0
      }));
      
      return new Response(
        JSON.stringify({ 
          stats: emptyStats, 
          totals: {
            totalReservations: 0,
            totalSales: 0,
            totalPresence: 0,
          },
          debug: process.env.NODE_ENV === 'development' ? { 
            accessibleBranchIds: [], 
            isSuperAdmin: isSuperAdminUser,
            isBranchesAdmin: isBranchesAdminUser,
            allBranches,
            branchId,
            branchIds
          } : undefined
        }),
        { status: 200 }
      );
    }
  }

  // Build branch filter based on role and branch selection
  let branchFilter: any = {};
  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    branchFilter['preferences.branchId'] = { $in: branchIds };
  } else if (!allBranches && branchId && branchId !== 'all') {
    // Single branch selected (backward compatibility)
    branchFilter['preferences.branchId'] = branchId;
  } else if (allBranches && !isSuperAdminUser && accessibleBranchIds.length > 0) {
    // Branch admin with "all" selected - restrict to accessible branches
    branchFilter['preferences.branchId'] = { $in: accessibleBranchIds };
  }

  try {
    // We need to use the consistent date filter approach used in reservation-stats 
    // which uses the preferences.visitDate field
    const basePipeline: any[] = [
      {
        $match: {
          $or: [
            { deletedAt: { $exists: false } },
            { deletedAt: null }
          ],
          ...branchFilter
        }
      }
    ];

    // Add date filtering if provided
    let datePipeline: any[] = [...basePipeline];
    if (startDate && endDate) {
      datePipeline = [
        {
          $match: {
            $or: [
              { deletedAt: { $exists: false } },
              { deletedAt: null }
            ],
            ...branchFilter
          }
        }
      ];
      
      // Add date range filter separately to avoid TypeScript linter error
      datePipeline[0].$match.$expr = {
        $and: [
          {
            $gte: [
              { $substr: ['$preferences.visitDate', 0, 10] },
              startDate
            ]
          },
          {
            $lte: [
              { $substr: ['$preferences.visitDate', 0, 10] },
              endDate
            ]
          }
        ]
      };
    }

    // Group reservations by day of week using preferences.visitDate
    const reservationsByDayOfWeek = await Reservation.aggregate([
      ...datePipeline,
      {
        $addFields: {
          // Extract date from preferences.visitDate and convert to Date
          visitDate: { 
            $dateFromString: { 
              dateString: { $substr: ['$preferences.visitDate', 0, 10] }
            } 
          }
        }
      },
      {
        $addFields: {
          // Get the day of the week (1=Sunday, 2=Monday, ...)
          dayOfWeek: { $dayOfWeek: '$visitDate' }
        }
      },
      {
        $group: {
          _id: '$dayOfWeek', // 1=Sunday, 2=Monday, ... (MongoDB's $dayOfWeek starts with 1=Sunday)
          count: { $sum: 1 },
          sales: {
            $sum: {
              $cond: [
                { $gt: ['$sellingAmount', 0] },
                1,
                0
              ]
            }
          },
          presence: {
            $sum: {
              $cond: [
                { $and: [
                  { $ne: ['$presentAt', null] },
                  { $in: ['$status', ['confirmed', 'en-suivi', 'non-vendu', 'sales']] }
                ]},
                1,
                0
              ]
            }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Transform to format needed by the frontend (with name labels)
    const dayLabels = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayLabelsFr = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    
    // Initialize array with all days, value 0 if no reservations
    const stats = Array.from({ length: 7 }, (_, i) => ({
      dayIndex: i,
      dayName: dayLabels[i],
      dayNameFr: dayLabelsFr[i],
      count: 0,
      sales: 0,
      presence: 0
    }));
    
    // Fill in actual values where we have data 
    // MongoDB's dayOfWeek is 1-indexed (1=Sunday), but our stats array is 0-indexed (0=Sunday)
    reservationsByDayOfWeek.forEach(item => {
      const index = item._id - 1; // Adjust for MongoDB's 1-indexed day of week
      if (index >= 0 && index < 7) { // Validate array bounds just in case
        stats[index].count = item.count;
        stats[index].sales = item.sales;
        stats[index].presence = item.presence;
      }
    });

    // Calculate totals
    const totals = {
      totalReservations: stats.reduce((sum, day) => sum + day.count, 0),
      totalSales: stats.reduce((sum, day) => sum + day.sales, 0),
      totalPresence: stats.reduce((sum, day) => sum + day.presence, 0),
    };

    // Debug info in development mode
    const debugInfo = process.env.NODE_ENV === 'development' ? {
      isSuperAdmin: isSuperAdminUser,
      isBranchesAdmin: isBranchesAdminUser,
      branchId,
      branchIds,
      allBranches,
      accessibleBranchIds: accessibleBranchIds.length > 0 ? accessibleBranchIds : 'N/A',
      branchFilter,
      startDate,
      endDate
    } : undefined;

    // Return the data with day labels for easier frontend rendering
    return new Response(JSON.stringify({ 
      stats, 
      totals,
      debug: debugInfo
    }), { status: 200 });
    
  } catch (error) {
    console.error('Error in weekday distribution API:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to fetch weekday distribution data' }),
      { status: 500 }
    );
  }
} 