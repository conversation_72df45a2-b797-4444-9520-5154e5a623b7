import { NextRequest } from 'next/server';
import Reservation from '@/models/Reservation';
import Branch from '@/models/Branch';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { getSalesStatusCodes, getPresenceStatusCodes, getCancelledSalesStatusCodes } from '@/lib/utils/reservation-status-utils';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
await dbConnect();

  // Authenticate request (session or token)
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated) {
    return new Response(JSON.stringify({ error: auth.error }), {
      status: auth.error === 'Forbidden' ? 403 : 401
    });
  }

  // Get status codes for sales, presence, and cancelled calculations (used throughout the function)
  const salesStatusCodes = await getSalesStatusCodes();
  const presenceStatusCodes = await getPresenceStatusCodes();
  const cancelledStatusCodes = await getCancelledSalesStatusCodes();

  const { searchParams } = new URL(req.url);
  const branchId = searchParams.get('branchId');
  const branchIds = searchParams.get('branchIds')?.split(',').filter(Boolean);
  const allBranches = searchParams.get('allBranches') === 'true' || branchId === 'all';
  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);

  const { userId, isSuperAdminUser, isBranchesAdminUser } = auth;

  // If branch admin and all branches selected, get accessible branches
  let accessibleBranchIds: string[] = [];
  if (allBranches && !isSuperAdminUser && isBranchesAdminUser && userId) {
    const branchesResponse = await Branch.find(
      { responsible: userId, deletedAt: null },
      '_id'
    ).lean();
    accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());

    if (accessibleBranchIds.length === 0) {
      return new Response(
        JSON.stringify({
          stats: [],
          totalCount: 0,
          totalPages: 0,
          page,
          totalReservations: 0,
          totalSales: 0,
          averageSale: 0,
        }),
        { status: 200 }
      );
    }
  }

  // Build match condition based on role and branch selection
  let match: any = { $or: [{ deletedAt: { $exists: false } }, { deletedAt: null }] };

  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    match['preferences.branchId'] = { $in: branchIds };
  } else if (branchId && branchId !== 'all') {
    // Single branch (backward compatibility)
    match['preferences.branchId'] = branchId;
  } else if (allBranches && !isSuperAdminUser && accessibleBranchIds.length > 0) {
    // Branch admin with "all" selected - restrict to accessible branches
    match['preferences.branchId'] = { $in: accessibleBranchIds };
  }

  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');

  // If startDate and endDate are provided, group by visitDate (and branch if all branches)
  if (startDate && endDate) {
    const dateMatch = {
      ...match,
      $expr: {
        $and: [
          {
            $gte: [
              { $substr: ['$preferences.visitDate', 0, 10] },
              startDate
            ]
          },
          {
            $lte: [
              { $substr: ['$preferences.visitDate', 0, 10] },
              endDate
            ]
          }
        ]
      }
    };
    let dailyStats;
    dailyStats = await Reservation.aggregate([
      { $match: dateMatch },
      {
        $group: {
          _id: {
            date: { $substr: ['$preferences.visitDate', 0, 10] },
            time: '$preferences.visitTime',
            branchId: '$preferences.branchId',
          },
          count: { $sum: 1 },
          salesCount: {
            $sum: {
              $cond: [
                { $in: ['$status', salesStatusCodes] },
                1,
                0
              ]
            }
          },
          presenceCount: {
            $sum: {
              $cond: [
                { $in: ['$status', presenceStatusCodes] },
                1,
                0
              ]
            }
          },
        },
      },
      { $sort: { '_id.date': 1, '_id.time': 1, '_id.branchId': 1 } },
    ]);
    // --- Add summary aggregation for the date range ---
    const summaryAgg = await Reservation.aggregate([
      { $match: dateMatch },
      {
        $group: {
          _id: null,
          totalReservations: { $sum: 1 },
          totalSales: {
            $sum: {
              $cond: [
                { $in: ['$status', salesStatusCodes] },
                { $ifNull: ['$sellingAmount', 0] },
                0
              ]
            }
          },
          cancelledSales: {
            $sum: {
              $cond: [
                { $in: ['$status', cancelledStatusCodes] },
                { $ifNull: ['$sellingAmount', 0] },
                0
              ]
            }
          },
          salesCount: {
            $sum: {
              $cond: [
                { $in: ['$status', salesStatusCodes] },
                1,
                0
              ]
            }
          }
        }
      }
    ]);
    const statsAgg = summaryAgg[0] || { totalReservations: 0, totalSales: 0, cancelledSales: 0, salesCount: 0 };
    const averageSale = statsAgg.salesCount > 0 ? statsAgg.totalSales / statsAgg.salesCount : 0;
    
    // Add debug info in development
    let debugInfo = {};
    if (process.env.NODE_ENV === 'development') {
      debugInfo = {
        isSuperAdmin: isSuperAdminUser,
        isBranchesAdmin: isBranchesAdminUser,
        branchId,
        accessibleBranchIds: accessibleBranchIds.length > 0 ? accessibleBranchIds : 'N/A',
      };
    }
    
    return new Response(
      JSON.stringify({
        stats: dailyStats,
        totalReservations: statsAgg.totalReservations,
        totalSales: statsAgg.totalSales,
        cancelledSales: statsAgg.cancelledSales,
        averageSale,
        debug: process.env.NODE_ENV === 'development' ? debugInfo : undefined
      }),
      { status: 200 }
    );
  }

  // Get all grouped stats for total count
  const allStats = await Reservation.aggregate([
    { $match: match },
    {
      $group: {
        _id: {
          date: { $substr: ['$preferences.visitDate', 0, 10] },
          time: '$preferences.visitTime',
          branchId: '$preferences.branchId',
        },
        count: { $sum: 1 },
        salesCount: {
          $sum: {
            $cond: [
              { $in: ['$status', salesStatusCodes] },
              1,
              0
            ]
          }
        },
        presenceCount: {
          $sum: {
            $cond: [
              { $in: ['$status', presenceStatusCodes] },
              1,
              0
            ]
          }
        },
      },
    },
    { $sort: { '_id.date': -1, '_id.time': -1 } },
  ]);

  const totalCount = allStats.length;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Paginate the grouped stats
  const paginatedStats = allStats.slice((page - 1) * pageSize, page * pageSize);

  // --- New: Compute total reservations, total sales, cancelled sales, average sale ---
  const salesAgg = await Reservation.aggregate([
    { $match: match },
    {
      $group: {
        _id: null,
        totalReservations: { $sum: 1 },
        totalSales: {
          $sum: {
            $cond: [
              { $in: ['$status', salesStatusCodes] },
              { $ifNull: ['$sellingAmount', 0] },
              0
            ]
          }
        },
        cancelledSales: {
          $sum: {
            $cond: [
              { $in: ['$status', cancelledStatusCodes] },
              { $ifNull: ['$sellingAmount', 0] },
              0
            ]
          }
        },
        salesCount: {
          $sum: {
            $cond: [
              { $in: ['$status', salesStatusCodes] },
              1,
              0
            ]
          }
        }
      }
    }
  ]);
  const statsAgg = salesAgg[0] || { totalReservations: 0, totalSales: 0, cancelledSales: 0, salesCount: 0 };
  const averageSale = statsAgg.salesCount > 0 ? statsAgg.totalSales / statsAgg.salesCount : 0;

  // Add debug info in development
  let debugInfo = {};
  if (process.env.NODE_ENV === 'development') {
    debugInfo = {
      isSuperAdmin: isSuperAdminUser,
      isBranchesAdmin: isBranchesAdminUser,
      branchId,
      branchIds,
      allBranches,
      accessibleBranchIds: accessibleBranchIds.length > 0 ? accessibleBranchIds : 'N/A',
    };
  }

  return new Response(JSON.stringify({
    stats: paginatedStats,
    totalCount,
    totalPages,
    page,
    totalReservations: statsAgg.totalReservations,
    totalSales: statsAgg.totalSales,
    cancelledSales: statsAgg.cancelledSales,
    averageSale,
    debug: process.env.NODE_ENV === 'development' ? debugInfo : undefined
  }), { status: 200 });
}