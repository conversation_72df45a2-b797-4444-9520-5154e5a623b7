import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
interface PostalCodeData {
  POSTAL_CODE: string;
  CITY: string;
  PROVINCE_ABBR: string;
  TIME_ZONE: number;
  LATITUDE: number;
  LONGITUDE: number;
  LOCATION?: {
    type: string;
    coordinates: [number, number];
  };
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const rawPostalCode = searchParams.get('code');

    if (!rawPostalCode) {
      return NextResponse.json(
        { error: 'Postal code is required' },
        { status: 400 }
      );
    }

    // Clean up and format the postal code
    const cleanPostalCode = rawPostalCode.replace(/\s+/g, '').toUpperCase();
    const formattedPostalCode = `${cleanPostalCode.slice(0, 3)} ${cleanPostalCode.slice(3)}`;

    console.log('Searching for postal code:', formattedPostalCode);

    const db= (await dbConnect()).connection;

    const postalData = await db.collection('Postal').findOne(
      { POSTAL_CODE: formattedPostalCode },
      {
        projection: {
          POSTAL_CODE: 1,
          CITY: 1,
          PROVINCE_ABBR: 1,
          TIME_ZONE: 1,
          LATITUDE: 1,
          LONGITUDE: 1,
          _id: 0
        }
      }
    );

    if (!postalData) {
      return NextResponse.json({
        error: 'Postal code not found',
        searched: formattedPostalCode
      }, { status: 404 });
    }

    // Add LOCATION field to match the expected format
    const responseData = {
      ...postalData,
      LOCATION: {
        type: 'Point',
        coordinates: [postalData.LONGITUDE, postalData.LATITUDE]
      }
    };

    // Format the response to match the frontend expectations
    return NextResponse.json({
      code: responseData.POSTAL_CODE,
      city: responseData.CITY,
      province: responseData.PROVINCE_ABBR,
      timezone: responseData.TIME_ZONE,
      location: responseData.LOCATION
    });
  } catch (error) {
    console.error('Error fetching postal code data:', error);
    return NextResponse.json({
      error: 'Failed to fetch postal code data',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}