import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';

export async function GET(
  request: Request,
  { params }: { params: { code: string } }
) {
  try {
    const db= (await dbConnect()).connection;
    
    if (!db) {
      throw new Error('Database connection not established');
    }
    
    // Parse the code - ensure it's correctly formatted for the database search
    const rawCode = params.code;
    // Format code from "M1M1M1" to "M1M 1M1"
    const formattedCode = rawCode.length === 6 
      ? `${rawCode.substring(0, 3)} ${rawCode.substring(3)}`
      : rawCode;
    
    console.log('Looking for postal code:', formattedCode);
    
    
    // Find exactly what you confirmed works in Compass
    const postalData = await db.collection('Postal').findOne({ 
      "POSTAL_CODE": formattedCode 
    });
    
    console.log('Query result:', postalData);
    
    if (!postalData) {
      // For debugging - show a few postal codes that DO exist
      const sampleCodes = await db.collection('Postal')
        .find({})
        .limit(5)
        .toArray();
      
      console.log('Sample postal codes in database:', 
        sampleCodes.map(p => p.POSTAL_CODE));
      
      return NextResponse.json({ 
        error: 'Postal code not found',
        searched: formattedCode,
      }, { status: 404 });
    }
    
    return NextResponse.json(postalData);
  } catch (error) {
    console.error('Error fetching postal data:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch postal data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 