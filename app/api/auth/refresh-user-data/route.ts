import { NextResponse } from 'next/server';
import UserModel from '@/models/User';
import RoleModel from '@/models/Role';
import PermissionModel from '@/models/Permission';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';

interface MongoRole {
  _id: Types.ObjectId;
  name: string;
  description?: string;
  permissions: Types.ObjectId[];
}

interface MongoPermission {
  _id: Types.ObjectId;
  code: string;
  name: string;
  description: string;
}

interface UserWithPopulatedFields {
  _id: Types.ObjectId;
  name: string;
  email: string;
  roles: MongoRole[];
  directPermissions: MongoPermission[];
  [key: string]: any;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const db= (await dbConnect()).connection;
    const user = await db.collection("users").findOne({ _id: new Types.ObjectId(userId) });
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    // Fetch roles
    const rolesDocs = await db.collection("roles").find({
      _id: { $in: user.roles }
    }).toArray();

    const roles = (rolesDocs as unknown[]).map(role => ({
      _id: (role as any)._id as Types.ObjectId,
      name: (role as any).name as string,
      description: (role as any).description as string,
      permissions: ((role as any).permissions || []) as Types.ObjectId[]
    }));

    // Get all permission IDs from roles
    const rolePermissionIds = roles.reduce<Types.ObjectId[]>((acc, role) => {
      return acc.concat(role.permissions || []);
    }, []);

    // Fetch permissions
    const permissionsDocs = await db.collection("permissions").find({
      _id: { 
        $in: [...rolePermissionIds, ...(user.directPermissions || [])]
      }
    }).toArray();

    const permissions = (permissionsDocs as unknown[]).map(perm => ({
      _id: (perm as any)._id as Types.ObjectId,
      code: (perm as any).code as string,
      name: (perm as any).name as string,
      description: (perm as any).description as string
    }));

    // Map roles to include only necessary data
    const processedRoles = roles.map(role => ({
      _id: role._id.toString(),
      name: role.name,
      description: role.description || '',
      permissions: (role.permissions || []).map(p => p.toString())
    }));

    // Get all permission codes from roles
    const rolePermissionCodes = permissions
      .filter(p => rolePermissionIds.map(id => id.toString()).includes(p._id.toString()))
      .map(p => p.code);

    // Map direct permissions, excluding those already present in role permissions
    const directPermissionIds = (user.directPermissions || []).map((id: Types.ObjectId) =>
      id.toString()
    );
    const processedDirectPermissions = permissions
      .filter(p =>
        directPermissionIds.includes(p._id.toString()) &&
        !rolePermissionCodes.includes(p.code)
      )
      .map(p => ({
        _id: p._id.toString(),
        code: p.code,
        name: p.name,
        description: p.description || ''
      }));

    // Get all permission codes
    const permissionCodes = Array.from(new Set(permissions.map(p => p.code)));

    // Return the updated session data
    return NextResponse.json({
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        roles: processedRoles,
        permissions: permissionCodes,
        directPermissions: processedDirectPermissions,
        isActive: user.isActive,
        image: user.image
      }
    });
  } catch (error) {
    console.error('Session refresh error:', error);
    return NextResponse.json({ error: 'Refresh failed' }, { status: 500 });
  }
}