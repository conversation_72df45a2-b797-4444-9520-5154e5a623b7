import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import User from '@/models/User';
import { Partner } from '@/models/Partner';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to access this resource' },
        { status: 401 }
      );
    }

  await dbConnect();

    // Fetch partners (users with Partner role)
    // The Partner role _id is "67cec8568f77ab38a0c94e61"
    const partnerRoleId = "67cec8568f77ab38a0c94e61";
    const partnerUsers = await User.find({ 
      roles: { $in: [partnerRoleId] }
    })
    .select('_id name email')
    .sort({ name: 1 });
    
    // Get partner company information for each user
    const partners = await Promise.all(partnerUsers.map(async (user) => {
      // Find partner where this user is an owner
      const partnerCompany = await Partner.findOne({
        owners: { $in: [new mongoose.Types.ObjectId(user._id)] }
      }).select('name');
      
      // Return user data with partner company name
      return {
        ...user.toObject(),
        partnerCompany: partnerCompany ? partnerCompany.name : null
      };
    }));
    
    return NextResponse.json({ partners });
    
  } catch (error: any) {
    console.error('Error fetching partners:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch partners' },
      { status: 500 }
    );
  }
} 