import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { Partner } from '@/models/Partner';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: Params) {
  await Promise.resolve(); // Ensure params are handled asynchronously
  const { id } = params;

  try {
    if (!mongoose.isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid partner ID' },
        { status: 400 }
      );
    }
    await dbConnect();
    const partner = await Partner.findById(id).populate('branchId', 'name');
    
    if (!partner) {
      return NextResponse.json(
        { error: 'Partner not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(partner);
  } catch (error) {
    console.error('Failed to fetch partner:', error);
    return NextResponse.json(
      { error: 'Failed to fetch partner' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, { params }: Params) {
  await Promise.resolve(); // Ensure params are handled asynchronously
  const { id } = params;

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!mongoose.isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid partner ID' },
        { status: 400 }
      );
    }

    const data = await request.json();
    await dbConnect();
    const updatedPartner = await Partner.findByIdAndUpdate(
      id,
      {
        name: data.name,
        abbreviatedName: data.abbreviatedName,
        branchId: data.branchId,
        email: data.email,
        phone: data.phone,
        fax: data.fax,
        internalPhone: data.internalPhone,
        internationalFax: data.internationalFax,
        website: data.website,
        address: data.address,
        local: data.local,
        city: data.city,
        province: data.province,
        country: data.country,
        postalCode: data.postalCode,
        divisionId: data.divisionId,
        language: data.language,
        federalTax: data.federalTax,
        provincialTax: data.provincialTax,
        companyLogo: data.companyLogo,
        urlSlug: data.urlSlug,
        loginHtml: data.loginHtml,
        owners: data.owners,
        agents: data.agents,
        lastModified: new Date(),
      },
      { new: true }
    ).populate('branchId', 'name');

    if (!updatedPartner) {
      return NextResponse.json(
        { error: 'Partner not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedPartner);
  } catch (error: any) {
    console.error('Failed to update partner:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update partner' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request, { params }: Params) {
  await Promise.resolve(); // Ensure params are handled asynchronously
  const { id } = params;

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!mongoose.isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid partner ID' },
        { status: 400 }
      );
    }
    await dbConnect();
    const deletedPartner = await Partner.findByIdAndDelete(id);

    if (!deletedPartner) {
      return NextResponse.json(
        { error: 'Partner not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Partner deleted successfully' });
  } catch (error) {
    console.error('Failed to delete partner:', error);
    return NextResponse.json(
      { error: 'Failed to delete partner' },
      { status: 500 }
    );
  }
}