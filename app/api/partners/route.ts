import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { Partner } from '@/models/Partner';
import Branch from '@/models/Branch';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { Event } from '@/models/Event';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const search = searchParams.get('search') || '';
    const branchId = searchParams.get('branchId') || '';
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortDirection = searchParams.get('sortDirection') || 'desc';
    const skip = (page - 1) * limit;
    await dbConnect();

    // Build the filter object
    const filter: any = { status: 'active' };
    
    // Add branch filter if specified
    if (branchId && branchId !== 'all') {
      filter.branchId = new mongoose.Types.ObjectId(branchId);
    }
    
    // Add search filter if specified
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      filter.$or = [
        { name: searchRegex },
        { email: searchRegex },
        { phone: searchRegex },
        { address: searchRegex },
        { city: searchRegex }
      ];
    }

    // Build sort object
    const sortObj: any = {};
    
    // Special handling for branchId to sort by branch name
    if (sortField === 'branchId') {
      // We'll need to sort after population
      sortObj['branchId.name'] = sortDirection === 'asc' ? 1 : -1;
    } else if (sortField === 'eventsCount') {
      // We'll handle eventsCount sorting after we have the data
      // because it's calculated after the query
      sortObj['createdAt'] = sortDirection === 'asc' ? 1 : -1; // Fallback sort
    } else {
      // Normal field sorting
      sortObj[sortField] = sortDirection === 'asc' ? 1 : -1;
    }

    console.log('Sorting by:', sortObj);

    // Set timeout for the query
    const queryTimeoutMs = 30000; // 30 seconds
    const query = Partner.find(filter)
      .populate('branchId', 'name')
      .populate('owners', 'name email')
      .populate('agents', 'name email')
      .sort(sortObj)
      .lean()
      .maxTimeMS(queryTimeoutMs)
      .skip(skip)
      .limit(limit);

    const [partners, total] = await Promise.all([
      query,
      Partner.countDocuments(filter)
    ]);

    // Get event counts for all partners in one aggregation
    const partnerIds = partners.map((p: any) => p._id);
    const eventCounts = await Event.aggregate([
      { $match: { partnerId: { $in: partnerIds } } },
      { $group: { _id: '$partnerId', count: { $sum: 1 } } }
    ]);
    const eventCountMap = Object.fromEntries(eventCounts.map(ec => [ec._id.toString(), ec.count]));

    // Attach eventsCount to each partner
    let partnersWithEventCount = partners.map((partner: any) => ({
      ...partner,
      eventsCount: eventCountMap[partner._id.toString()] || 0
    }));

    // Handle special sorting cases after data is processed
    if (sortField === 'eventsCount') {
      partnersWithEventCount = partnersWithEventCount.sort((a: any, b: any) => {
        const aCount = a.eventsCount || 0;
        const bCount = b.eventsCount || 0;
        return sortDirection === 'asc' 
          ? aCount - bCount 
          : bCount - aCount;
      });
    }

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: partnersWithEventCount,
      total,
      page,
      totalPages,
      limit
    });

  } catch (error: any) {
    console.error('GET partners error:', error);
    
    // Handle specific error types
    if (error.name === 'MongooseError' && error.message.includes('buffering timed out')) {
      return NextResponse.json(
        { error: 'Database connection timed out. Please try again.' },
        { status: 503 }
      );
    }

    if (error.name === 'MongoServerError') {
      return NextResponse.json(
        { error: 'Database server error. Please try again.' },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch partners' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email || !body.phone || !body.address || !body.city || !body.branchId) {
      return NextResponse.json(
        { error: 'Required fields are missing' },
        { status: 400 }
      );
    }
    await dbConnect();
    // Validate that branch exists
    const branch = await Branch.findById(body.branchId);
    if (!branch) {
      return NextResponse.json(
        { error: 'Invalid branch ID' },
        { status: 400 }
      );
    }

    // Create new partner
    const partner = await Partner.create({
      name: body.name,
      abbreviatedName: body.abbreviatedName,
      branchId: new mongoose.Types.ObjectId(body.branchId),
      email: body.email,
      phone: body.phone,
      address: body.address,
      local: body.local,
      city: body.city,
      postalCode: body.postalCode,
      language: body.language,
      companyLogo: body.companyLogo,
      urlSlug: body.urlSlug,
      loginHtml: body.loginHtml,
      owners: body.owners?.map((id: string) => new mongoose.Types.ObjectId(id)) || [],
      agents: body.agents?.map((id: string) => new mongoose.Types.ObjectId(id)) || [],
      status: 'active',
      lastModified: new Date()
    });

    // Populate related information
    await partner.populate([
      { path: 'branchId', select: 'name' },
      { path: 'owners', select: 'name email' },
      { path: 'agents', select: 'name email' }
    ]);

    return NextResponse.json(partner);
  } catch (error: any) {
    console.error('Create partner error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A partner with this name already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to create partner' },
      { status: 500 }
    );
  }
}