import { NextResponse } from 'next/server';
import Branch from '@/models/Branch';
import dbConnect from '@/lib/db';

export async function GET() {
  try {
  await dbConnect();

    // Fetch all branches and sort by name
    const branches = await Branch.find({})
      .select('name address city province postalCode availabilityLimit')
      .sort({ name: 1 })
      .lean();

    // Calculate distances if needed (this would be based on user's postal code)
    // For now, returning without distances
    const formattedBranches = branches.map(branch => ({
      _id: String(branch._id),
      name: branch.name,
      address: branch.address,
      city: branch.city,
      province: branch.province,
      postalCode: branch.postalCode,
      availabilityLimit: branch.availabilityLimit,
      distance: null // This would be calculated based on user's location if needed
    }));

    return NextResponse.json(formattedBranches);
  } catch (error) {
    console.error('Error fetching branches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch branches' },
      { status: 500 }
    );
  }
} 