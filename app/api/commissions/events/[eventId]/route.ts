import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import Commission from '@/models/Commission';
import Event from '@/models/Event';
import { EventReport } from '@/models/EventReport';
import mongoose from 'mongoose';
import { canUserViewEventCommissions } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '../../../utils/server-permission-utils';
import dbConnect from '@/lib/db';

interface Params {
  params: {
    eventId: string;
  };
}

// GET - Retrieve commissions for a specific event
export async function GET(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p = await params;

  try {
  await dbConnect();

    if (!p.eventId || !mongoose.Types.ObjectId.isValid(p.eventId)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      );
    }

    // Verify event exists and get branch information
    const event = await Event.findById(p.eventId).lean();
    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Check branch-specific permissions
    const canView = await canUserViewEventCommissions(session.user, event.branchId.toString());
    if (!canView) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view event commissions for this branch' },
        { status: 403 }
      );
    }

    // Get event report for additional context
    const eventReport = await EventReport.findOne({ eventId: p.eventId })
      .select('status calculatedCommissions commissionCalculatedAt linkedReservationIds')
      .lean();

    // Get all commissions for this event
    const commissions = await Commission.find({ eventId: p.eventId })
      .populate('userId', 'name email')
      .populate('commissionTypeId', 'name code amount')
      .populate('approvedBy', 'name email')
      .sort({ createdAt: -1 })
      .lean();

    // Calculate summary statistics
    const summary = {
      totalCommissions: commissions.length,
      totalAmount: commissions.reduce((sum, c) => sum + c.amount, 0),
      approvedAmount: commissions
        .filter(c => c.isApproved)
        .reduce((sum, c) => sum + c.amount, 0),
      pendingAmount: commissions
        .filter(c => !c.isApproved)
        .reduce((sum, c) => sum + c.amount, 0),
      approvedCount: commissions.filter(c => c.isApproved).length,
      pendingCount: commissions.filter(c => !c.isApproved).length,
      
      // Group by commission type
      byType: commissions.reduce((acc, c) => {
        const typeCode = c.commissionTypeId?.code || 'UNKNOWN';
        if (!acc[typeCode]) {
          acc[typeCode] = {
            count: 0,
            totalAmount: 0,
            approvedAmount: 0,
            pendingAmount: 0
          };
        }
        acc[typeCode].count += 1;
        acc[typeCode].totalAmount += c.amount;
        if (c.isApproved) {
          acc[typeCode].approvedAmount += c.amount;
        } else {
          acc[typeCode].pendingAmount += c.amount;
        }
        return acc;
      }, {} as any),
      
      // Group by user
      byUser: commissions.reduce((acc, c) => {
        const userId = c.userId._id.toString();
        if (!acc[userId]) {
          acc[userId] = {
            user: c.userId,
            totalAmount: 0,
            approvedAmount: 0,
            pendingAmount: 0,
            commissionCount: 0,
            commissions: []
          };
        }
        acc[userId].totalAmount += c.amount;
        acc[userId].commissionCount += 1;
        acc[userId].commissions.push({
          type: c.commissionTypeId?.code,
          amount: c.amount,
          isApproved: c.isApproved,
          metadata: c.metadata
        });
        
        if (c.isApproved) {
          acc[userId].approvedAmount += c.amount;
        } else {
          acc[userId].pendingAmount += c.amount;
        }
        return acc;
      }, {} as any)
    };

    return NextResponse.json({
      event: {
        _id: event._id,
        name: event.name,
        location: event.location,
        startDate: event.startDate,
        endDate: event.endDate,
        status: event.status
      },
      eventReport: eventReport ? {
        status: eventReport.status,
        commissionCalculatedAt: eventReport.commissionCalculatedAt,
        linkedReservationsCount: eventReport.linkedReservationIds?.length || 0,
        calculatedCommissions: eventReport.calculatedCommissions
      } : null,
      commissions,
      summary
    });

  } catch (error) {
    console.error('Error fetching event commissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event commissions' },
      { status: 500 }
    );
  }
}

// POST - Recalculate commissions for an event (admin only)
export async function POST(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p = await params;

  try {
  await dbConnect();

    if (!p.eventId || !mongoose.Types.ObjectId.isValid(p.eventId)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      );
    }

    // Get event to check branch permissions
    const event = await Event.findById(p.eventId).lean();
    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Check branch-specific permissions for recalculation
    const canCalculate = await canUserViewEventCommissions(session.user, event.branchId.toString());
    if (!canCalculate) {
      return NextResponse.json(
        { error: 'Insufficient permissions to recalculate event commissions for this branch' },
        { status: 403 }
      );
    }

    // Find the event report
    const eventReport = await EventReport.findOne({ eventId: p.eventId });
    if (!eventReport) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    if (eventReport.status !== 'validated') {
      return NextResponse.json(
        { error: 'Can only recalculate commissions for validated reports' },
        { status: 400 }
      );
    }

    // Import and use the commission calculation service
    const { EventCommissionCalculationService } = await import('@/lib/services/event-commission-calculation');
    const commissionService = new EventCommissionCalculationService();
    
    const result = await commissionService.recalculateEventCommissions(
      eventReport._id.toString(),
      session.user.id
    );

    return NextResponse.json({
      message: 'Commissions recalculated successfully',
      result: {
        totalCommissions: result.commissions.length,
        totalAmount: result.summary.totalAmount,
        errors: result.errors,
        summary: result.summary
      }
    });

  } catch (error) {
    console.error('Error recalculating event commissions:', error);
    return NextResponse.json(
      { error: 'Failed to recalculate event commissions' },
      { status: 500 }
    );
  }
}
