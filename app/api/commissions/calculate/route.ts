import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { EventReport } from '@/models/EventReport';
import mongoose from 'mongoose';
import { canUserCalculateEventCommissions } from '@/lib/utils/permissions-utils';
import Event from '@/models/Event';
import { getUserPermissions } from '../../utils/server-permission-utils';
import { EventCommissionCalculationService } from '@/lib/services/event-commission-calculation';
import dbConnect from '@/lib/db';

// POST - Calculate commissions for an event report
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
  await dbConnect();

    const { eventReportId, dryRun = false } = await request.json();

    if (!eventReportId || !mongoose.Types.ObjectId.isValid(eventReportId)) {
      return NextResponse.json(
        { error: 'Valid event report ID is required' },
        { status: 400 }
      );
    }

    // Verify event report exists and get event information
    const eventReport = await EventReport.findById(eventReportId)
      .populate('eventId', 'name location startDate endDate branchId')
      .lean();

    if (!eventReport) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Check branch-specific permissions
    const event = eventReport.eventId as any;
    const canCalculate = await canUserCalculateEventCommissions(session.user, event.branchId.toString());
    if (!canCalculate) {
      return NextResponse.json(
        { error: 'Insufficient permissions to calculate event commissions for this branch' },
        { status: 403 }
      );
    }

    if (eventReport.status !== 'validated') {
      return NextResponse.json(
        { error: 'Can only calculate commissions for validated reports' },
        { status: 400 }
      );
    }

    const commissionService = new EventCommissionCalculationService();
    
    if (dryRun) {
      // For dry run, we'll simulate the calculation without saving
      // This is useful for testing and previewing commission amounts
      const result = await commissionService.calculateEventCommissions(eventReportId);
      
      // Don't actually save the commissions in dry run mode
      // The service already saves them, so we'd need to delete them
      // For now, we'll just return the calculation results
      
      return NextResponse.json({
        dryRun: true,
        eventReport: {
          _id: eventReport._id,
          eventId: eventReport.eventId,
          status: eventReport.status
        },
        calculation: {
          totalReservations: result.totalReservations,
          summary: result.summary,
          commissionBreakdown: result.commissions.map(c => ({
            userId: c.userId,
            amount: c.amount,
            type: c.metadata?.calculationMethod,
            metadata: c.metadata
          })),
          errors: result.errors
        },
        message: 'Commission calculation preview completed (dry run)'
      });
      
    } else {
      // Actual calculation and saving
      const result = await commissionService.calculateEventCommissions(eventReportId);
      
      return NextResponse.json({
        dryRun: false,
        eventReport: {
          _id: eventReport._id,
          eventId: eventReport.eventId,
          status: eventReport.status
        },
        calculation: {
          totalReservations: result.totalReservations,
          summary: result.summary,
          commissionsCreated: result.commissions.length,
          errors: result.errors
        },
        message: 'Commission calculation completed successfully'
      });
    }

  } catch (error) {
    console.error('Error calculating commissions:', error);
    return NextResponse.json(
      { error: 'Failed to calculate commissions' },
      { status: 500 }
    );
  }
}

// GET - Get commission calculation preview for an event report
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
  await dbConnect();

    const url = new URL(request.url);
    const eventReportId = url.searchParams.get('eventReportId');

    if (!eventReportId || !mongoose.Types.ObjectId.isValid(eventReportId)) {
      return NextResponse.json(
        { error: 'Valid event report ID is required' },
        { status: 400 }
      );
    }

    // Get event report with populated data
    const eventReport = await EventReport.findById(eventReportId)
      .populate('eventId', 'name location startDate endDate branchId')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('supervisors', 'name email')
      .lean();

    if (!eventReport) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Check branch-specific permissions
    const event = eventReport.eventId as any;
    const canCalculate = await canUserCalculateEventCommissions(session.user, event.branchId.toString());
    if (!canCalculate) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view commission calculations for this branch' },
        { status: 403 }
      );
    }

    // Get commission configuration
    const CommissionConfiguration = (await import('@/models/CommissionConfiguration')).default;
    const config = await CommissionConfiguration.findOne({ isActive: true }).lean();
    
    if (!config) {
      return NextResponse.json(
        { error: 'No active commission configuration found' },
        { status: 400 }
      );
    }

    // Get linked reservations count
    const linkedReservationsCount = eventReport.linkedReservationIds?.length || 0;

    // Calculate preview amounts without creating actual commission records
    const preview = {
      eventReport: {
        _id: eventReport._id,
        eventId: eventReport.eventId,
        status: eventReport.status,
        linkedReservationsCount
      },
      configuration: {
        papCommissionAmount: config.papCommissionAmount,
        cookCommissionPerReservation: config.cookCommissionPerReservation,
        supervisorCommissionPerReservation: config.supervisorCommissionPerReservation,
        papSchedulingThresholdWeeks: config.papSchedulingThresholdWeeks
      },
      estimatedCommissions: {
        paps: eventReport.paps.map(pap => ({
          userId: pap.userId,
          estimatedAmount: linkedReservationsCount * config.papCommissionAmount, // Simplified estimate
          note: 'Actual amount depends on reservation timing (3-week rule)'
        })),
        cooks: eventReport.cooks.map(cook => ({
          userId: cook.userId,
          estimatedAmount: (linkedReservationsCount * config.cookCommissionPerReservation) / eventReport.cooks.length, // Simplified equal split
          note: 'Actual amount based on time proportion'
        })),
        supervisors: eventReport.supervisors.map(supervisorId => ({
          userId: supervisorId,
          estimatedAmount: linkedReservationsCount * config.supervisorCommissionPerReservation,
          note: 'Fixed amount per reservation'
        }))
      },
      totals: {
        estimatedPapTotal: eventReport.paps.length * linkedReservationsCount * config.papCommissionAmount,
        estimatedCookTotal: linkedReservationsCount * config.cookCommissionPerReservation,
        estimatedSupervisorTotal: eventReport.supervisors.length * linkedReservationsCount * config.supervisorCommissionPerReservation,
        estimatedGrandTotal: (eventReport.paps.length * linkedReservationsCount * config.papCommissionAmount) +
                           (linkedReservationsCount * config.cookCommissionPerReservation) +
                           (eventReport.supervisors.length * linkedReservationsCount * config.supervisorCommissionPerReservation)
      }
    };

    return NextResponse.json(preview);

  } catch (error) {
    console.error('Error getting commission preview:', error);
    return NextResponse.json(
      { error: 'Failed to get commission preview' },
      { status: 500 }
    );
  }
}
