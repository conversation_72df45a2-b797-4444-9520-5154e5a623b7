import { NextRequest, NextResponse } from 'next/server';
import { validateDashboardToken } from '@/lib/utils/dashboard-token-utils';
import dbConnect from '@/lib/db';

export async function POST(req: NextRequest) {
  try {
  await dbConnect();
    
    const body = await req.json();
    const { token } = body;

    if (!token) {
      return NextResponse.json(
        { isValid: false, error: 'Token is required' },
        { status: 400 }
      );
    }

    const validation = await validateDashboardToken(token);
    
    return NextResponse.json({
      isValid: validation.isValid,
      error: validation.error
    });

  } catch (error: any) {
    console.error('Error validating dashboard token:', error);
    return NextResponse.json(
      { isValid: false, error: 'Token validation failed' },
      { status: 500 }
    );
  }
}
