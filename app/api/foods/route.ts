import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import Food from '@/models/Food';
import { hasPermission } from '@/lib/auth/check-permission';
import { FOOD_PERMISSIONS } from '@/types/permission-codes';

interface FoodData {
  name: string;
  name_en: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'fr';

    const db= (await dbConnect()).connection;
    
    const foods = await db.collection<FoodData>('Foods')
      .find({})
      .sort({ name: 1 })
      .toArray();

    // Format response based on language
    const formattedFoods = foods.map(food => ({
      _id: food._id,
      name: lang === 'en' ? food.name_en : food.name,
      name_en: food.name_en,
      name_fr: food.name
    }));

    return NextResponse.json(formattedFoods);
  } catch (error) {
    console.error('Error fetching foods:', error);
    return NextResponse.json(
      { error: 'Failed to fetch foods' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    await dbConnect();
    
    if (!await hasPermission(FOOD_PERMISSIONS.CREATE_FOODS)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();

    if (!body.name || !body.name_en) {
      return NextResponse.json(
        { error: 'Name and English name are required' },
        { status: 400 }
      );
    }

    const food = await Food.create({
      name: body.name,
      name_en: body.name_en,
    });

    return NextResponse.json(food);
  } catch (error: any) {
    console.error('Create food error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A food with this name already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to create food' },
      { status: 500 }
    );
  }
} 