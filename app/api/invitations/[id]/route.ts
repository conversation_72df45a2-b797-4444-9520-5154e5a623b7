import { NextRequest, NextResponse } from 'next/server';
import Invitation from '@/models/Invitation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { Partner } from '@/models/Partner';
import dbConnect from '@/lib/db';
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to view invitations' },
        { status: 401 }
      );
    }

    const db= (await dbConnect()).connection;
    const { id } = params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid invitation ID format' },
        { status: 400 }
      );
    }

    // Find invitation by ID and populate referenced fields
    const invitation = await Invitation.findById(id)
      .populate('partner_id', 'name email')
      .populate('reservation_id', 'title date');

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Get partner company information if partner_id exists
    if (invitation.partner_id) {
      // Find partner where this user is an owner
      const partnerCompany = await Partner.findOne({
        owners: { $in: [invitation.partner_id._id] }
      }).select('name');

      // Add partner company name to the response
      const invitationObj = invitation.toObject();
      invitationObj.partner_id = {
        ...invitationObj.partner_id,
        partnerCompany: partnerCompany ? partnerCompany.name : null
      };

      return NextResponse.json({ invitation: invitationObj });
    }

    return NextResponse.json({ invitation });
  } catch (error: any) {
    console.error('Error fetching invitation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch invitation' },
      { status: 500 }
    );
  }
}


export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  // Delete functionality has been disabled
  return NextResponse.json(
    { error: 'Delete operation is not allowed for invitations' },
    { status: 405 }
  );
}