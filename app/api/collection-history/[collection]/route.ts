import { NextRequest, NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';



const uri = process.env.MONGODB_URI || "mongodb+srv://knockprocom:<EMAIL>/amqpartners_dev";
if (!uri) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const options = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000,
  family: 4, // Force IPv4
  retryWrites: true,
  retryReads: true,
};

declare global {
  var _mongoClientPromise: Promise<MongoClient>;
}

let client: MongoClient;
let clientPromise: Promise<MongoClient>;

// In development mode, use a global variable so that the value
// is preserved across module reloads caused by HMR (Hot Module Replacement).
if (!global._mongoClientPromise) {
  client = new MongoClient(uri, options);
  global._mongoClientPromise = client.connect();
}
clientPromise = global._mongoClientPromise;


export async function connectToDatabase() {
  try {
    const client = await clientPromise;
    const db = client.db();

    // Test the connection by pinging the database
    await db.admin().ping();

    return db;
  } catch (error) {
    console.error('MongoDB connection error:', error);

    // Reset the global promise so next attempt will create a new connection
    if (global._mongoClientPromise) {
      try {
        const client = await global._mongoClientPromise;
        await client.close();
      } catch (closeError) {
        console.error('Error closing MongoDB client:', closeError);
      }
      delete global._mongoClientPromise;
    }

    throw error;
  }
}
// Connect to the history database
async function connectToHistoryDatabase() {
  const historyDbUri = process.env.MONGODB_URI;
  const historyDbName = process.env.MONGODB_HISTORY_DB_NAME;

  // If history database is configured separately, use it
  if (historyDbUri && historyDbName) {
    const { MongoClient } = await import('mongodb');
    const client = new MongoClient(historyDbUri);
    await client.connect();
    const db = client.db(historyDbName);
    return { client, db };
  }

  // Otherwise, use the main database connection
  const db = await connectToDatabase();
  const historyCollectionName = historyDbName || 'collection_history';

  return {
    client: null, // No separate client to close
    db,
    collectionName: historyCollectionName
  };
}

export async function GET(request: NextRequest, { params }: { params: { collection: string } }) {
  let client: any = null;

  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = parseInt(searchParams.get('skip') || '0');
    const collection = params.collection;

    if (!collection) {
      return NextResponse.json(
        { success: false, error: 'Collection name is required' },
        { status: 400 }
      );
    }

    // Connect to the history database
    const { client: historyClient, db } = await connectToHistoryDatabase();
    client = historyClient;

    // Create history collection name - always append _history to the collection name
    const historyCollectionName = `${collection}_history`;
    const historyCollection = db.collection(historyCollectionName);

    console.log(`Looking for history in collection: ${historyCollectionName}`);

    let query: any = {};

    // If search term is provided, we need to search within the history entries
    if (search) {
      // Create a regex for case-insensitive search
      const searchRegex = new RegExp(search, 'i');

      query = {
        $or: [
          // Search by document ID
          { _id: searchRegex },
          // Search within history entries for common fields
          { 'history.name': searchRegex },
          { 'history.title': searchRegex },
          { 'history.email': searchRegex },
          { 'history.phone': searchRegex },
          { 'history.status': searchRegex },
          // For reservations specifically
          { 'history.customerInfo.client1Name': searchRegex },
          { 'history.customerInfo.client2Name': searchRegex },
          { 'history.customerInfo.phone': searchRegex },
          { 'history.customerInfo.email': searchRegex },
        ]
      };
    }

    // Check if collection exists and has documents
    const collectionExists = await db.listCollections({ name: historyCollectionName }).hasNext();
    console.log(`Collection ${historyCollectionName} exists: ${collectionExists}`);

    if (!collectionExists) {
      console.log(`Collection ${historyCollectionName} does not exist`);
      return NextResponse.json({
        success: true,
        collection,
        histories: [],
        pagination: {
          total: 0,
          limit,
          skip,
          hasMore: false
        },
        message: `No history collection found for ${collection}. Make sure the trigger is deployed and some changes have been made.`
      });
    }

    // Fetch collection histories with pagination
    const histories = await historyCollection
      .find(query)
      .sort({ 'history.historyTimestamp': -1 }) // Sort by most recent history entry
      .limit(limit)
      .skip(skip)
      .toArray();

    console.log(`Found ${histories.length} documents in ${historyCollectionName}`);

    // Get total count for pagination
    const total = await historyCollection.countDocuments(query);

    // Transform the data to ensure proper structure (keep original order)
    const transformedHistories = histories.map(history => ({
      _id: history._id,
      history: (history.history || []).map((entry: any) => ({
        ...entry,
        historyTimestamp: entry.historyTimestamp || new Date(),
        historyOperation: entry.historyOperation || 'unknown'
      }))
    }));

    return NextResponse.json({
      success: true,
      collection,
      histories: transformedHistories,
      pagination: {
        total,
        limit,
        skip,
        hasMore: skip + limit < total
      }
    });

  } catch (error) {
    console.error(`Error fetching ${params.collection} histories:`, error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to fetch ${params.collection} histories`,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  } finally {
    if (client && typeof client.close === 'function') {
      await client.close();
    }
  }
}

// GET specific document history
export async function POST(request: NextRequest, { params }: { params: { collection: string } }) {
  let client: any = null;

  try {
    const body = await request.json();
    const { documentId } = body;
    const collection = params.collection;

    if (!collection) {
      return NextResponse.json(
        { success: false, error: 'Collection name is required' },
        { status: 400 }
      );
    }

    if (!documentId) {
      return NextResponse.json(
        { success: false, error: 'Document ID is required' },
        { status: 400 }
      );
    }

    // Connect to the history database
    const { client: historyClient, db } = await connectToHistoryDatabase();
    client = historyClient;

    // Create history collection name - always append _history to the collection name
    const historyCollectionName = `${collection}_history`;
    const historyCollection = db.collection(historyCollectionName);

    // Fetch specific document history
    const history = await historyCollection.findOne({ _id: documentId });

    if (!history) {
      return NextResponse.json(
        { success: false, error: `${collection} history not found` },
        { status: 404 }
      );
    }

    // Transform the data to ensure proper structure (keep original order)
    const transformedHistory = {
      _id: history._id,
      history: (history.history || []).map((entry: any) => ({
        ...entry,
        historyTimestamp: entry.historyTimestamp || new Date(),
        historyOperation: entry.historyOperation || 'unknown'
      }))
    };

    return NextResponse.json({
      success: true,
      collection,
      history: transformedHistory
    });

  } catch (error) {
    console.error(`Error fetching specific ${params.collection} history:`, error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to fetch ${params.collection} history`,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  } finally {
    if (client && typeof client.close === 'function') {
      await client.close();
    }
  }
}
