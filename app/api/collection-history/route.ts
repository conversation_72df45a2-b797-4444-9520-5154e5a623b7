import { NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';



const uri = process.env.MONGODB_URI || "mongodb+srv://knockprocom:<EMAIL>/amqpartners_dev";
if (!uri) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const options = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000,
  family: 4, // Force IPv4
  retryWrites: true,
  retryReads: true,
};

declare global {
  var _mongoClientPromise: Promise<MongoClient>;
}

let client: MongoClient;
let clientPromise: Promise<MongoClient>;

// In development mode, use a global variable so that the value
// is preserved across module reloads caused by HMR (Hot Module Replacement).
if (!global._mongoClientPromise) {
  client = new MongoClient(uri, options);
  global._mongoClientPromise = client.connect();
}
clientPromise = global._mongoClientPromise;


export async function connectToDatabase() {
  try {
    const client = await clientPromise;
    const db = client.db();

    // Test the connection by pinging the database
    await db.admin().ping();

    return db;
  } catch (error) {
    console.error('MongoDB connection error:', error);

    // Reset the global promise so next attempt will create a new connection
    if (global._mongoClientPromise) {
      try {
        const client = await global._mongoClientPromise;
        await client.close();
      } catch (closeError) {
        console.error('Error closing MongoDB client:', closeError);
      }
      delete global._mongoClientPromise;
    }

    throw error;
  }
}
// Connect to the history database
async function connectToHistoryDatabase() {
  const historyDbUri = process.env.MONGODB_URI;
  const historyDbName = process.env.MONGODB_HISTORY_DB_NAME;

  // If history database is configured separately, use it
  if (historyDbUri && historyDbName) {
    const { MongoClient } = await import('mongodb');
    const client = new MongoClient(historyDbUri);
    await client.connect();
    const db = client.db(historyDbName);
    return { client, db };
  }

  // Otherwise, use the main database connection
  const db = await connectToDatabase();

  return {
    client: null, // No separate client to close
    db
  };
}

export async function GET() {
  let client: any = null;

  try {
    // Connect to the history database
    const { client: historyClient, db } = await connectToHistoryDatabase();
    client = historyClient;

    // Get all collections in the database
    const collections = await db.listCollections().toArray();
    console.log('All collections found:', collections.map(c => c.name));

    // Filter collections that end with '_history'
    const historyCollections = collections
      .filter(collection => collection.name.endsWith('_history'))
      .map(collection => {
        const originalCollectionName = collection.name.replace('_history', '');
        return {
          name: originalCollectionName,
          historyCollection: collection.name,
          displayName: originalCollectionName.charAt(0).toUpperCase() + originalCollectionName.slice(1)
        };
      });

    console.log('History collections found:', historyCollections.map(c => c.historyCollection));

    // Get document counts for each history collection
    const collectionsWithCounts = await Promise.all(
      historyCollections.map(async (collection) => {
        try {
          const count = await db.collection(collection.historyCollection).countDocuments();
          const latestEntry = await db.collection(collection.historyCollection)
            .findOne({}, { sort: { 'history.historyTimestamp': -1 } });

          return {
            ...collection,
            documentCount: count,
            lastActivity: latestEntry?.history?.[latestEntry.history.length - 1]?.historyTimestamp || null
          };
        } catch (error) {
          console.error(`Error getting count for ${collection.historyCollection}:`, error);
          return {
            ...collection,
            documentCount: 0,
            lastActivity: null
          };
        }
      })
    );

    // Sort by document count (most active first)
    collectionsWithCounts.sort((a, b) => b.documentCount - a.documentCount);

    return NextResponse.json({
      success: true,
      collections: collectionsWithCounts,
      total: collectionsWithCounts.length
    });

  } catch (error) {
    console.error('Error fetching collection history list:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch collection history list',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  } finally {
    if (client && typeof client.close === 'function') {
      await client.close();
    }
  }
}
