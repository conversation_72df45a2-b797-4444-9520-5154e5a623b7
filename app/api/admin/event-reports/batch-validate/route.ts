import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import { EventReport } from '@/models/EventReport';
import { Event } from '@/models/Event';
import mongoose from 'mongoose';
import { canUserValidateEventReports } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { EventCommissionCalculationService } from '@/lib/services/event-commission-calculation';
import dbConnect from '@/lib/db';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!canUserValidateEventReports(session.user)) {
    return NextResponse.json(
      { error: 'Insufficient permissions to validate event reports' },
      { status: 403 }
    );
  }

  try {
  await dbConnect();
    
    const { reportIds, action, notes } = await request.json();
    
    if (!reportIds || !Array.isArray(reportIds) || reportIds.length === 0) {
      return NextResponse.json(
        { error: 'Report IDs array is required' },
        { status: 400 }
      );
    }

    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be either "approve" or "reject"' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    // Process each report
    for (const reportId of reportIds) {
      try {
        if (!mongoose.Types.ObjectId.isValid(reportId)) {
          errors.push({
            reportId,
            error: 'Invalid report ID format'
          });
          continue;
        }

        // Get the report
        const report = await EventReport.findById(reportId).populate('eventId');
        if (!report) {
          errors.push({
            reportId,
            error: 'Report not found'
          });
          continue;
        }

        // Check if report is in validatable state
        if (report.status !== 'submitted') {
          errors.push({
            reportId,
            error: `Cannot validate report with status: ${report.status}`
          });
          continue;
        }

        if (action === 'approve') {
          // Calculate commissions if not already done
          let commissionData = null;
          if (!report.commissions) {
            try {
              const commissionService = new EventCommissionCalculationService();
              const calculationResult = await commissionService.calculateEventCommissions(report);
              
              if (calculationResult.errors.length > 0) {
                console.warn('Commission calculation warnings:', calculationResult.errors);
              }

              commissionData = {
                calculatedAt: new Date(),
                calculatedBy: new mongoose.Types.ObjectId(session.user.id),
                paps: calculationResult.commissions
                  .filter(c => c.type === 'EVENT_PAP_COMMISSION')
                  .map(c => ({
                    userId: c.userId,
                    amount: c.amount,
                    reservationCount: c.metadata?.reservationCount || 0
                  })),
                cooks: calculationResult.commissions
                  .filter(c => c.type === 'EVENT_COOK_COMMISSION')
                  .map(c => ({
                    userId: c.userId,
                    amount: c.amount,
                    percentage: c.metadata?.percentage || 0
                  })),
                total: calculationResult.summary.totalAmount
              };
            } catch (commissionError) {
              console.error('Commission calculation failed:', commissionError);
              errors.push({
                reportId,
                error: 'Failed to calculate commissions'
              });
              continue;
            }
          }

          // Update report to validated status
          const updateData: any = {
            status: 'validated',
            validatedAt: new Date(),
            validatedBy: new mongoose.Types.ObjectId(session.user.id),
            validationComments: notes || 'Bulk approval',
            $push: {
              history: {
                action: 'Report validated (bulk approval)',
                changedBy: new mongoose.Types.ObjectId(session.user.id),
                changedAt: new Date(),
                newValue: { status: 'validated', notes: notes || 'Bulk approval' }
              }
            }
          };

          if (commissionData) {
            updateData.commissions = commissionData;
          }

          await EventReport.findByIdAndUpdate(reportId, updateData);

          // Update event status to done
          await Event.findByIdAndUpdate(report.eventId._id, {
            status: 'done',
            $push: {
              history: {
                action: 'Event completed (report validated)',
                changedBy: new mongoose.Types.ObjectId(session.user.id),
                changedAt: new Date()
              }
            }
          });

          results.push({
            reportId,
            status: 'approved',
            commissionCalculated: !!commissionData
          });

        } else if (action === 'reject') {
          // Update report back to processing status for revision
          await EventReport.findByIdAndUpdate(reportId, {
            status: 'processing',
            validationComments: notes || 'Bulk rejection - requires revision',
            $push: {
              history: {
                action: 'Report rejected (bulk rejection)',
                changedBy: new mongoose.Types.ObjectId(session.user.id),
                changedAt: new Date(),
                newValue: { status: 'processing', notes: notes || 'Bulk rejection - requires revision' }
              }
            }
          });

          // Update event status back to processing_report
          await Event.findByIdAndUpdate(report.eventId._id, {
            status: 'processing_report',
            $push: {
              history: {
                action: 'Event status reverted (report rejected)',
                changedBy: new mongoose.Types.ObjectId(session.user.id),
                changedAt: new Date()
              }
            }
          });

          results.push({
            reportId,
            status: 'rejected'
          });
        }

      } catch (error) {
        console.error(`Error processing report ${reportId}:`, error);
        errors.push({
          reportId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Send notifications for processed reports
    try {
      if (process.env.NODE_ENV !== 'test') {
        const { EventNotificationService } = await import('@/lib/services/event-notification-service');
        const notificationService = new EventNotificationService();

        for (const result of results) {
          if (result.status === 'approved') {
            await notificationService.sendReportValidatedNotification(result.reportId);
          } else if (result.status === 'rejected') {
            await notificationService.sendReportRejectedNotification(result.reportId);
          }
        }
      }
    } catch (notificationError) {
      console.error('Failed to send notifications:', notificationError);
      // Don't fail the entire operation for notification errors
    }

    return NextResponse.json({
      success: true,
      action,
      processed: results.length,
      errors: errors.length,
      results,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('Error in batch validation:', error);
    return NextResponse.json(
      { error: 'Failed to process batch validation' },
      { status: 500 }
    );
  }
}

// GET endpoint to check batch validation status
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!canUserValidateEventReports(session.user)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    );
  }

  try {
  await dbConnect();

    const { searchParams } = new URL(request.url);
    const reportIds = searchParams.get('reportIds')?.split(',') || [];

    if (reportIds.length === 0) {
      return NextResponse.json(
        { error: 'Report IDs are required' },
        { status: 400 }
      );
    }

    const reports = await EventReport.find({
      _id: { $in: reportIds.map(id => new mongoose.Types.ObjectId(id)) }
    }).select('_id status validatedAt validatedBy validationComments').lean();

    const statusSummary = {
      total: reportIds.length,
      found: reports.length,
      validated: reports.filter(r => r.status === 'validated').length,
      pending: reports.filter(r => r.status === 'submitted').length,
      processing: reports.filter(r => r.status === 'processing').length
    };

    return NextResponse.json({
      summary: statusSummary,
      reports
    });

  } catch (error) {
    console.error('Error checking batch validation status:', error);
    return NextResponse.json(
      { error: 'Failed to check validation status' },
      { status: 500 }
    );
  }
}
