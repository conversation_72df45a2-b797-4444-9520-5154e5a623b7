import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { Partner } from '@/models/Partner';
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
  await dbConnect();
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const status = searchParams.get('status') || 'active';
    
    // Build filter object
    const filter: any = { status };
    
    if (branchId && branchId !== 'all') {
      filter.branchId = branchId;
    }
    
    // Get partners with basic fields
    const partners = await Partner.find(filter)
      .select('_id name abbreviatedName branchId')
      .populate('branchId', 'name')
      .sort({ name: 1 })
      .lean();
    
    return NextResponse.json(partners);
  } catch (error) {
    console.error('Error fetching partners:', error);
    return NextResponse.json(
      { error: 'Failed to fetch partners' },
      { status: 500 }
    );
  }
} 