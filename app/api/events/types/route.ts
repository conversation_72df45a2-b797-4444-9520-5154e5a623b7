import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import * as RoleUtils from '@/lib/utils/role-utils';
import { EventType } from '@/models/EventType';
import dbConnect from '@/lib/db';

export async function GET() {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
  await dbConnect();
    
    // Get all event types
    const eventTypes = await EventType.find().sort({ name: 1 }).lean();
    
    return NextResponse.json(eventTypes);
  } catch (error) {
    console.error('Error fetching event types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event types' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Only super admins can create event types
  const isSuperAdmin = RoleUtils.isSuperAdmin(session);
  if (!isSuperAdmin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
  await dbConnect();
    const data = await request.json();
    
    if (!data.name || !data.code) {
      return NextResponse.json(
        { error: 'Name and code are required' },
        { status: 400 }
      );
    }
    
    // Check if event type with same code already exists
    const exists = await EventType.findOne({
      $or: [
        { code: data.code.toLowerCase() },
        { name: data.name.toLowerCase() }
      ]
    });
    
    if (exists) {
      return NextResponse.json(
        { error: 'Event type with this code or name already exists' },
        { status: 400 }
      );
    }
    
    // Create new event type
    const newEventType = new EventType({
      code: data.code.toLowerCase(),
      name: data.name
    });
    
    await newEventType.save();
    
    return NextResponse.json(newEventType, { status: 201 });
  } catch (error) {
    console.error('Error creating event type:', error);
    return NextResponse.json(
      { error: 'Failed to create event type' },
      { status: 500 }
    );
  }
}

// Add a PUT endpoint to update event types
export async function PUT(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Only super admins can update event types
  const isSuperAdmin = RoleUtils.isSuperAdmin(session);
  if (!isSuperAdmin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
  await dbConnect();
    const data = await request.json();
    
    if (!data._id) {
      return NextResponse.json(
        { error: 'Event type ID is required' },
        { status: 400 }
      );
    }
    
    // Check if updating code or name, make sure it's not duplicated
    if (data.code || data.name) {
      const query: any = { _id: { $ne: data._id } };
      const orConditions = [];
      
      if (data.code) {
        orConditions.push({ code: data.code.toLowerCase() });
      }
      
      if (data.name) {
        orConditions.push({ name: data.name.toLowerCase() });
      }
      
      if (orConditions.length > 0) {
        query.$or = orConditions;
        const duplicate = await EventType.findOne(query);
        
        if (duplicate) {
          return NextResponse.json(
            { error: 'Event type with this code or name already exists' },
            { status: 400 }
          );
        }
      }
    }
    
    // Update the event type
    const updateData: any = { ...data };
    delete updateData._id;
    
    if (updateData.code) {
      updateData.code = updateData.code.toLowerCase();
    }
    
    const updatedEventType = await EventType.findByIdAndUpdate(
      data._id,
      { $set: updateData },
      { new: true }
    ).lean();
    
    if (!updatedEventType) {
      return NextResponse.json(
        { error: 'Event type not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedEventType);
  } catch (error) {
    console.error('Error updating event type:', error);
    return NextResponse.json(
      { error: 'Failed to update event type' },
      { status: 500 }
    );
  }
}

// Add a DELETE endpoint to remove event types
export async function DELETE(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Only super admins can delete event types
  const isSuperAdmin = RoleUtils.isSuperAdmin(session);
  if (!isSuperAdmin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
  await dbConnect();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Event type ID is required' },
        { status: 400 }
      );
    }
    
    // Find and delete the event type
    const deletedEventType = await EventType.findByIdAndDelete(id).lean();
    
    if (!deletedEventType) {
      return NextResponse.json(
        { error: 'Event type not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(deletedEventType);
  } catch (error) {
    console.error('Error deleting event type:', error);
    return NextResponse.json(
      { error: 'Failed to delete event type' },
      { status: 500 }
    );
  }
} 