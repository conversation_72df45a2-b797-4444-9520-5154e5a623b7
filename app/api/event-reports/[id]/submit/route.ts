import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import { EventReport } from '@/models/EventReport';
import { Event } from '@/models/Event';
import mongoose from 'mongoose';
import { validateEventReportForSubmission } from '@/app/api/utils/event-report-utils';
import { canUserSubmitEventReports } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { EventReservationLinkingService } from '@/lib/services/event-reservation-linking';
import { getUserFromToken } from '@/app/api/affectations/utils/mobile_auth_utils';
import dbConnect from '@/lib/db';

interface Params {
  params: {
    id: string;
  };
}

export async function POST(request: Request, { params }: Params) {
  const requestBody = await request.json().catch(() => ({}));
  const requestHeaders = request.headers;
  let session = await getUserFromToken(requestBody, requestHeaders);

  if (!session) {
    session = await getServerSession(authOptions);
  }

  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p = await params;
  
  try {
  await dbConnect();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid report ID' },
        { status: 400 }
      );
    }

    // Get current report
    const report = await EventReport.findById(p.id).populate('eventId');
    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Get user ID (handle both web and mobile sessions)
    const userId = session.user.id || session.user._id?.toString();

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid user session' },
        { status: 401 }
      );
    }

    // Check permissions
    const isSupervisor = report.supervisors.some((supervisorId: any) =>
      supervisorId.toString() === userId
    );

    if (!canUserSubmitEventReports(session.user) && !isSupervisor) {
      return NextResponse.json(
        { error: 'Insufficient permissions to submit this report' },
        { status: 403 }
      );
    }

    // Check if report is in submittable state
    if (report.status !== 'pending' && report.status !== 'processing') {
      return NextResponse.json(
        { error: `Cannot submit report with status: ${report.status}` },
        { status: 400 }
      );
    }

    // Validate report data before submission using the centralized validation function
    const validationErrors = validateEventReportForSubmission(report);

    // Check cook percentages add up to 100% (if multiple cooks)
    if (report.cooks && report.cooks.length > 1) {
      const totalPercentage = report.cooks.reduce((sum: number, cook: any) => 
        sum + (cook.percentage || 0), 0
      );
      if (Math.abs(totalPercentage - 100) > 0.01) {
        validationErrors.push('Cook percentages must add up to 100%');
      }
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      );
    }

    // Trigger reservation linking before submission
    try {
      console.log(`Triggering reservation linking before submission for report ${p.id}`);
      const linkingService = new EventReservationLinkingService();
      const linkingResult = await linkingService.linkReservationsToEvent(p.id);

      console.log(`Reservation linking completed for submission:`, {
        totalLinked: linkingResult.totalLinked,
        agentResults: linkingResult.agentResults.length,
        errors: linkingResult.errors.length
      });

      // Log any linking errors but don't fail the submission
      if (linkingResult.errors.length > 0) {
        console.warn('Reservation linking errors during submission:', linkingResult.errors);
      }
    } catch (error) {
      console.error('Failed to perform reservation linking before submission:', error);
      // Don't fail the submission if linking fails, but log the error
    }

    // Update report status to submitted
    const updatedReport = await EventReport.findByIdAndUpdate(
      p.id,
      {
        status: 'submitted',
        $push: {
          history: {
            action: 'Report submitted for validation (with reservation linking)',
            changedBy: new mongoose.Types.ObjectId(userId),
            changedAt: new Date(),
            previousValue: { status: report.status },
            newValue: { status: 'submitted' }
          }
        }
      },
      { new: true }
    )
      .populate('eventId', 'name location startDate endDate status')
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .lean();

    // Update associated event status to awaiting_validation
    if (report.eventId) {
      await Event.findByIdAndUpdate(
        report.eventId._id,
        { status: 'awaiting_validation' }
      );
    }

    return NextResponse.json({
      ...updatedReport,
      message: 'Report submitted successfully for validation'
    });

  } catch (error) {
    console.error('Error submitting event report:', error);
    return NextResponse.json(
      { error: 'Failed to submit event report' },
      { status: 500 }
    );
  }
}
