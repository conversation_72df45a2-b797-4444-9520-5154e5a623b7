import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import { EventReport } from '@/models/EventReport';
import { Event } from '@/models/Event';
import mongoose from 'mongoose';
import { canUserValidateEventReports } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { EventCommissionCalculationService } from '@/lib/services/event-commission-calculation';
import { EventReservationLinkingService } from '@/lib/services/event-reservation-linking';
import dbConnect from '@/lib/db';

interface Params {
  params: {
    id: string;
  };
}

export async function POST(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p = params;
  
  if (!canUserValidateEventReports(session.user)) {
    return NextResponse.json(
      { error: 'Insufficient permissions to validate event reports' },
      { status: 403 }
    );
  }
  
  try {
  await dbConnect();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid report ID' },
        { status: 400 }
      );
    }

    const { approved, comments, commissionData } = await request.json();
    
    if (approved === undefined) {
      return NextResponse.json(
        { error: 'Approval status is required' },
        { status: 400 }
      );
    }

    // Get current report
    const report = await EventReport.findById(p.id).populate('eventId');
    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Check if report is in validatable state
    if (report.status !== 'submitted') {
      return NextResponse.json(
        { error: `Cannot validate report with status: ${report.status}` },
        { status: 400 }
      );
    }

    let updateData: any = {};
    let eventStatus = '';
    let historyAction = '';

    if (approved) {
      // Approve the report without automatic relinking
      // Manual reservation links made during validation are preserved
      updateData.status = 'validated';
      eventStatus = 'done';
      historyAction = 'Report validated and approved (manual reservation links preserved)';

      // Add commission data if provided
      if (commissionData) {
        updateData.commissions = {
          calculatedAt: new Date(),
          calculatedBy: new mongoose.Types.ObjectId(session.user.id),
          paps: commissionData.paps || [],
          cooks: commissionData.cooks || [],
          total: commissionData.total || 0
        };
      }
    } else {
      // Reject the report - send back to processing
      updateData.status = 'processing';
      eventStatus = 'processing_report';
      historyAction = 'Report rejected - returned for revision';
    }

    // Add validation comments if provided
    if (comments) {
      updateData.validationComments = comments;
    }

    // Add history entry
    updateData.$push = {
      history: {
        action: historyAction,
        changedBy: new mongoose.Types.ObjectId(session.user.id),
        changedAt: new Date(),
        previousValue: { status: report.status },
        newValue: { 
          status: updateData.status,
          comments: comments,
          approved: approved
        }
      }
    };

    // Update the report
    const updatedReport = await EventReport.findByIdAndUpdate(
      p.id,
      updateData,
      { new: true }
    )
      .populate({
        path: 'eventId',
        select: 'name location startDate endDate status branchId partnerId eventTypeId supervisors',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'partnerId', select: 'name' },
          { path: 'eventTypeId', select: 'name code' },
          { path: 'supervisors', select: 'name email' }
        ]
      })
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .lean();

    // If approved, freeze reservation links and trigger commission calculation
    if (approved && updatedReport) {
      try {
        // Explicitly freeze reservation links to ensure they're available for commission calculation
        console.log(`Freezing reservation links for validated report ${p.id}`);
        const linkingService = new EventReservationLinkingService();
        await linkingService.freezeReservationLinks(p.id);

        const commissionService = new EventCommissionCalculationService();
        const calculationResult = await commissionService.calculateEventCommissions(p.id);

        console.log(`Commission calculation completed for event report ${p.id}:`, {
          totalCommissions: calculationResult.commissions.length,
          totalAmount: calculationResult.summary.totalAmount,
          errors: calculationResult.errors
        });

        // Log any calculation errors but don't fail the validation
        if (calculationResult.errors.length > 0) {
          console.warn('Commission calculation errors:', calculationResult.errors);
        }

      } catch (error) {
        console.error('Commission calculation failed for event report:', p.id, error);
        // Don't fail the validation if commission calculation fails
        // This ensures the validation process is robust
      }
    }

    // Update associated event status
    if (report.eventId) {
      await Event.findByIdAndUpdate(
        report.eventId._id,
        { status: eventStatus }
      );
    }

    return NextResponse.json({
      ...updatedReport,
      message: approved ? 'Report validated and approved successfully' : 'Report rejected and returned for revision'
    });

  } catch (error) {
    console.error('Error validating event report:', error);
    return NextResponse.json(
      { error: 'Failed to validate event report' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve validation status and history
export async function GET(_request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p = params;
  
  try {
  await dbConnect();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid report ID' },
        { status: 400 }
      );
    }

    const report = await EventReport.findById(p.id)
      .select('status validationComments commissions history')
      .populate('history.changedBy', 'name email')
      .lean();

    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Filter history to validation-related entries
    const validationHistory = (report as any).history.filter((entry: any) =>
      entry.action.includes('validated') ||
      entry.action.includes('rejected') ||
      entry.action.includes('submitted')
    );

    return NextResponse.json({
      status: (report as any).status,
      validationComments: (report as any).validationComments,
      commissions: (report as any).commissions,
      validationHistory
    });

  } catch (error) {
    console.error('Error fetching validation status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch validation status' },
      { status: 500 }
    );
  }
}
