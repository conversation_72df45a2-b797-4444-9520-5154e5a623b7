import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { EventReport } from '@/models/EventReport';
import Reservation from '@/models/Reservation';
import mongoose from 'mongoose';
import { canUserEditEventReports, canUserViewEventReports } from '@/lib/utils/permissions-utils';
import { getUserPermissions, getUserRoles } from '@/app/api/utils/server-permission-utils';
import { transformReportForFrontend, validateTimeRange, validatePersonnelTimeRanges } from '@/app/api/utils/event-report-utils';
import { EventReservationLinkingService } from '@/lib/services/event-reservation-linking';
import { getUserFromToken } from '@/app/api/affectations/utils/mobile_auth_utils';

import dbConnect from '@/lib/db';

interface Params {
  params: {
    id: string;
  };
}

/**
 * Enhance report with populated reservation data for validation page
 */
async function enhanceReportWithReservations(report: any) {
  try {
    // Get all reservation IDs linked to PAPs
    const allLinkedReservationIds = new Set<string>();

    // Collect all linked reservation IDs from PAPs
    report.paps?.forEach((pap: any) => {
      if (pap.linkedReservationIds) {
        pap.linkedReservationIds.forEach((id: any) => {
          allLinkedReservationIds.add(id.toString());
        });
      }
    });

    // Also include general linked reservations
    if (report.linkedReservationIds) {
      report.linkedReservationIds.forEach((id: any) => {
        allLinkedReservationIds.add(id.toString());
      });
    }

    // Fetch all linked reservations with populated data
    const linkedReservations = allLinkedReservationIds.size > 0
      ? await Reservation.find({
          _id: { $in: Array.from(allLinkedReservationIds) }
        })
        .populate('partnerId', 'name email')
        .select('_id customerInfo partnerId createdAt')
        .lean()
      : [];

    // Create a map for quick lookup
    const reservationMap = new Map();
    linkedReservations.forEach((res: any) => {
      reservationMap.set(res._id.toString(), {
        _id: res._id,
        customerInfo: res.customerInfo,
        partnerId: res.partnerId,
        createdAt: res.createdAt
      });
    });

    // Enhance PAPs with populated reservation data
    if (report.paps) {
      report.paps = report.paps.map((pap: any) => ({
        ...pap,
        linkedReservations: pap.linkedReservationIds
          ? pap.linkedReservationIds
              .map((id: any) => reservationMap.get(id.toString()))
              .filter(Boolean)
          : []
      }));
    }

    // Get all reservations for PAPs in this report (regardless of event date)
    const papUserIds = report.paps?.map((pap: any) => pap.userId._id || pap.userId) || [];

    const allPapReservations = papUserIds.length > 0
      ? await Reservation.find({
          partnerId: { $in: papUserIds },
          isDeleted: { $ne: true }
        })
        .populate('partnerId', 'name email')
        .select('_id customerInfo partnerId createdAt linkedEventId')
        .lean()
      : [];

    // Group reservations by PAP and add link status
    const reservationsByPap = new Map();

    allPapReservations.forEach((res: any) => {
      const papId = res.partnerId._id.toString();
      if (!reservationsByPap.has(papId)) {
        reservationsByPap.set(papId, []);
      }

      let linkStatus = 'unlinked';
      let linkedToPapId = null;

      // Check if linked to any PAP in this report
      for (const pap of report.paps || []) {
        if (pap.linkedReservationIds?.some((id: any) => id.toString() === res._id.toString())) {
          linkStatus = 'linked';
          linkedToPapId = pap.userId._id || pap.userId;
          break;
        }
      }

      // Check if linked to another event
      if (linkStatus === 'unlinked' && res.linkedEventId && res.linkedEventId.toString() !== report.eventId._id.toString()) {
        linkStatus = 'linked_other';
      }

      reservationsByPap.get(papId).push({
        _id: res._id,
        customerInfo: res.customerInfo,
        partnerId: res.partnerId,
        createdAt: res.createdAt,
        linkStatus,
        linkedToPapId
      });
    });

    // Add the enhanced data to the report
    report.reservationsByPap = Object.fromEntries(reservationsByPap);
    report.linkedReservationsSummary = {
      total: linkedReservations.length,
      byPap: report.paps?.reduce((acc: any, pap: any) => {
        acc[pap.userId._id || pap.userId] = pap.linkedReservations?.length || 0;
        return acc;
      }, {}) || {}
    };

    return report;
  } catch (error) {
    console.error('Error enhancing report with reservations:', error);
    // Return original report if enhancement fails
    return report;
  }
}

export async function GET(request: Request, { params }: Params) {
  const requestHeaders = request.headers;

  // Check for mobile authentication first
  let session = null;
  const authHeader = requestHeaders.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    session = await getUserFromToken({ isMobile: true }, requestHeaders);
  }

  // Fall back to web session if no mobile session
  if (!session) {
    session = await getServerSession(authOptions);
  }

  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p = await params;
  
  try {
  await dbConnect();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid report ID' },
        { status: 400 }
      );
    }

    const report = await EventReport.findById(p.id)
      .populate({
        path: 'eventId',
        select: 'name location startDate endDate status branchId partnerId eventTypeId supervisors',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'partnerId', select: 'name' },
          { path: 'eventTypeId', select: 'name code' },
          { path: 'supervisors', select: 'name email' }
        ]
      })
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .lean();

    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Get user ID (handle both web and mobile sessions)
    const userId = session.user.id || session.user._id?.toString();

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid user session' },
        { status: 401 }
      );
    }

    // Check permissions
    const isSupervisor = (report as any).supervisors?.some((supervisor: any) =>
      supervisor._id.toString() === userId
    ) || false;

    if (!canUserViewEventReports(session.user) && !isSupervisor) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view this report' },
        { status: 403 }
      );
    }

    // Fetch and populate reservation data for validation page
    const enhancedReport = await enhanceReportWithReservations(report);

    // Transform the report data to match frontend expectations
    const transformedReport = await transformReportForFrontend(enhancedReport);

    return NextResponse.json(transformedReport);

  } catch (error) {
    console.error('Error fetching event report:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event report' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, { params }: Params) {
  const requestBody = await request.json();
  const requestHeaders = request.headers;

  // Check for mobile authentication first
  let session = null;
  const authHeader = requestHeaders.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    session = await getUserFromToken({ isMobile: true }, requestHeaders);
  }

  // Fall back to web session if no mobile session
  if (!session) {
    session = await getServerSession(authOptions);
  }

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Ensure user has roles and permissions loaded
  if (!session.user.roles) {
    session.user.roles = await getUserRoles(session);
  }
  if (!session.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }

  const p = await params;

  try {
  await dbConnect();

    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid report ID' },
        { status: 400 }
      );
    }

    const reportData = requestBody;
    
    // Get current report
    const currentReport = await EventReport.findById(p.id);
    if (!currentReport) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Get user ID (handle both web and mobile sessions)
    const userId = session.user.id || session.user._id?.toString();

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid user session' },
        { status: 401 }
      );
    }

    // Check permissions
    const isSupervisor = currentReport.supervisors.some((supervisorId: any) =>
      supervisorId.toString() === userId
    );

    if (!canUserEditEventReports(session.user) && !isSupervisor) {
      return NextResponse.json(
        { error: 'Insufficient permissions to edit this report' },
        { status: 403 }
      );
    }

    // Check if report is in editable state
    if (currentReport.status === 'validated') {
      return NextResponse.json(
        { error: 'Cannot edit validated report' },
        { status: 400 }
      );
    }

    // Validate and normalize event time range if being updated
    if (reportData.eventStartTime || reportData.eventEndTime) {
      try {
        // Use current values if only one is being updated
        const currentStartTime = reportData.eventStartTime ? new Date(reportData.eventStartTime) : currentReport.eventStartTime;
        const currentEndTime = reportData.eventEndTime ? new Date(reportData.eventEndTime) : currentReport.eventEndTime;

        // Check if dates are valid
        if (isNaN(currentStartTime.getTime()) || isNaN(currentEndTime.getTime())) {
          return NextResponse.json(
            { error: 'Invalid date format for event times. Please provide valid ISO date strings.' },
            { status: 400 }
          );
        }

        // Validate that startTime is before endTime
        if (currentStartTime >= currentEndTime) {
          return NextResponse.json(
            { error: 'Event start time must be before end time' },
            { status: 400 }
          );
        }

        // Ensure dates are stored as UTC
        if (reportData.eventStartTime) {
          reportData.eventStartTime = currentStartTime.toISOString();
        }
        if (reportData.eventEndTime) {
          reportData.eventEndTime = currentEndTime.toISOString();
        }

      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid date format for event times. Please provide valid ISO date strings.' },
          { status: 400 }
        );
      }
    }

    // Validate personnel time ranges are within event time range
    const eventStartTime = reportData.eventStartTime || currentReport.eventStartTime;
    const eventEndTime = reportData.eventEndTime || currentReport.eventEndTime;

    if (eventStartTime && eventEndTime) {
      // Collect all personnel from the update data
      const allPersonnel = [];

      if (reportData.paps) {
        allPersonnel.push(...reportData.paps.map((pap: any) => ({
          userId: pap.userId,
          timeRange: pap.timeRange,
          name: 'PAP'
        })));
      }

      if (reportData.cooks) {
        allPersonnel.push(...reportData.cooks.map((cook: any) => ({
          userId: cook.userId,
          timeRange: cook.timeRange,
          name: 'Cook'
        })));
      }

      if (allPersonnel.length > 0) {
        const validationErrors = validatePersonnelTimeRanges(
          eventStartTime,
          eventEndTime,
          allPersonnel
        );

        if (validationErrors.length > 0) {
          return NextResponse.json(
            {
              error: 'Personnel time validation failed',
              details: validationErrors.map(e => e.message)
            },
            { status: 400 }
          );
        }
      }
    }

    // Prepare update data
    const updateData: any = {};
    const historyEntry: any = {
      action: 'Report updated',
      changedBy: new mongoose.Types.ObjectId(userId),
      changedAt: new Date(),
      previousValue: {},
      newValue: {}
    };

    // Track changes for history
    const fieldsToTrack = ['eventStartTime', 'eventEndTime', 'paps', 'cooks'];
    fieldsToTrack.forEach(field => {
      if (reportData[field] !== undefined) {
        historyEntry.previousValue[field] = currentReport[field];
        historyEntry.newValue[field] = reportData[field];
        updateData[field] = reportData[field];
      }
    });

    // Convert user IDs to ObjectIds for paps and cooks
    if (reportData.paps) {
      updateData.paps = reportData.paps.map((pap: any) => ({
        userId: new mongoose.Types.ObjectId(pap.userId),
        timeRange: pap.timeRange
      }));
    }

    if (reportData.cooks) {
      updateData.cooks = reportData.cooks.map((cook: any) => ({
        userId: new mongoose.Types.ObjectId(cook.userId),
        timeRange: cook.timeRange,
        percentage: cook.percentage || 100
      }));
    }

    // Update status to processing if it was pending
    if (currentReport.status === 'pending') {
      updateData.status = 'processing';
    }

    // Add history entry
    updateData.$push = {
      history: historyEntry
    };

    // Update the report
    const updatedReport = await EventReport.findByIdAndUpdate(
      p.id,
      updateData,
      { new: true }
    )
      .populate({
        path: 'eventId',
        select: 'name location startDate endDate status branchId partnerId eventTypeId supervisors',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'partnerId', select: 'name' },
          { path: 'eventTypeId', select: 'name code' },
          { path: 'supervisors', select: 'name email' }
        ]
      })
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .lean();

    // Trigger automatic reservation linking after every report update
    // Only if the report is not validated (frozen)
    if (updatedReport && (updatedReport as any).status !== 'validated') {
      try {
        const linkingService = new EventReservationLinkingService();
        await linkingService.linkReservationsToEvent(p.id);
        console.log(`Automatic reservation linking completed for report ${p.id}`);
      } catch (error) {
        console.error('Failed to automatically link reservations:', error);
        // Don't fail the update if linking fails, just log the error
      }
    }

    return NextResponse.json(updatedReport);

  } catch (error) {
    console.error('Error updating event report:', error);
    return NextResponse.json(
      { error: 'Failed to update event report' },
      { status: 500 }
    );
  }
}


