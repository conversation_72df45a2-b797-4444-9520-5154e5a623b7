import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import { EventReport } from '@/models/EventReport';
import { Event } from '@/models/Event';
import mongoose from 'mongoose';
import { canUserViewEventReports, canUserViewSupervisedEvents } from '@/lib/utils/permissions-utils';
import { getUserPermissions, getUserRoles } from '@/app/api/utils/server-permission-utils';
import * as RoleUtils from '@/lib/utils/role-utils';
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  if(session && !session?.user.roles){
    session.user.roles = await getUserRoles(session);
  }
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
  await dbConnect();

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const supervisorId = searchParams.get('supervisorId');

    // Build filter object
    const filter: any = {};
    
    if (status) {
      filter.status = status;
    }
    
    if (startDate) {
      filter.eventStartTime = { $gte: new Date(startDate) };
    }
    
    if (endDate) {
      const end = new Date(endDate);
      if (/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        end.setUTCHours(23, 59, 59, 999);
      }
      filter.eventEndTime = { $lte: end };
    }
    console.log(session.user.roles);
    // Apply role-based filtering
    if (RoleUtils.isEventSupervisor(session.user.roles) && !RoleUtils.isSuperAdmin(session.user.roles)) {
      // Supervisors can only see their own reports
      console.log("hellllo")
      filter.supervisors = new mongoose.Types.ObjectId(session.user.id);
    } else if (supervisorId && canUserViewEventReports(session.user)) {
      // Admins can filter by specific supervisor
      filter.supervisors = new mongoose.Types.ObjectId(supervisorId);
    } else if (!canUserViewEventReports(session.user)) {
      // No permission to view reports
      return NextResponse.json([], { status: 200 });
    }

    // Get event reports with populated references
    const reports = await EventReport.find(filter)
      .populate({
        path: 'eventId',
        select: 'name location startDate endDate status branchId supervisors',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'supervisors', select: 'name email' }
        ]
      })
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .sort({ eventStartTime: -1 })
      .lean();

    return NextResponse.json({ reports });

  } catch (error) {
    console.error('Error fetching event reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event reports' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
  await dbConnect();
    const reportData = await request.json();
    
    // Validate required fields
    if (!reportData.eventId || !reportData.eventStartTime || !reportData.eventEndTime) {
      return NextResponse.json(
        { error: 'Missing required fields: eventId, eventStartTime, eventEndTime' },
        { status: 400 }
      );
    }

    // Validate and normalize dates to ensure they are valid UTC dates
    try {
      const startTime = new Date(reportData.eventStartTime);
      const endTime = new Date(reportData.eventEndTime);

      // Check if dates are valid
      if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
        return NextResponse.json(
          { error: 'Invalid date format for event times. Please provide valid ISO date strings.' },
          { status: 400 }
        );
      }

      // Validate that startTime is before endTime
      if (startTime >= endTime) {
        return NextResponse.json(
          { error: 'Event start time must be before end time' },
          { status: 400 }
        );
      }

      // Ensure dates are stored as UTC
      reportData.eventStartTime = startTime.toISOString();
      reportData.eventEndTime = endTime.toISOString();

    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid date format for event times. Please provide valid ISO date strings.' },
        { status: 400 }
      );
    }

    // Check if event exists
    const event = await Event.findById(reportData.eventId);
    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Check if report already exists for this event
    const existingReport = await EventReport.findOne({ eventId: reportData.eventId });
    if (existingReport) {
      return NextResponse.json(
        { error: 'Event report already exists for this event' },
        { status: 400 }
      );
    }

    // Create new event report
    const newReportData = {
      ...reportData,
      eventId: new mongoose.Types.ObjectId(reportData.eventId),
      supervisors: reportData.supervisors ? reportData.supervisors.map((id: string) => new mongoose.Types.ObjectId(id)) : [],
      paps: reportData.paps ? reportData.paps.map((pap: any) => ({
        userId: new mongoose.Types.ObjectId(pap.userId),
        timeRange: pap.timeRange
      })) : [],
      cooks: reportData.cooks ? reportData.cooks.map((cook: any) => ({
        userId: new mongoose.Types.ObjectId(cook.userId),
        timeRange: cook.timeRange,
        percentage: cook.percentage || 100
      })) : [],
      history: [{
        action: 'Report created',
        changedBy: new mongoose.Types.ObjectId(session.user.id),
        changedAt: new Date()
      }]
    };

    const newReport = new EventReport(newReportData);
    await newReport.save();

    // Update event with reportId
    await Event.findByIdAndUpdate(reportData.eventId, { reportId: newReport._id });

    // Populate references for the response
    const populatedReport = await EventReport.findById(newReport._id)
      .populate({
        path: 'eventId',
        select: 'name location startDate endDate status branchId supervisors',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'supervisors', select: 'name email' }
        ]
      })
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .lean();

    return NextResponse.json(populatedReport, { status: 201 });

  } catch (error) {
    console.error('Error creating event report:', error);
    return NextResponse.json(
      { error: 'Failed to create event report' },
      { status: 500 }
    );
  }
}
