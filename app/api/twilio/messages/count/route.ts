import { NextResponse } from 'next/server';
import { getMessagesForPhoneNumber, normalizePhoneNumber } from '@/lib/twilio';
import { validateBranchAccess, handleBranchValidationError } from '@/lib/branch-utils';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    let phoneNumber = searchParams.get('phoneNumber');
    const branchId = searchParams.get('branchId');
    
    // Get user from session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    // Return unauthorized error if no session found
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    if (!phoneNumber) {
      return NextResponse.json({ error: 'Phone number is required' }, { status: 400 });
    }
    
    if (!branchId) {
      return NextResponse.json({ error: 'Branch ID is required' }, { status: 400 });
    }
    
    phoneNumber = normalizePhoneNumber(phoneNumber);
    
    // Connect to database
  await dbConnect();
    
    // Validate branch access and get phone number
    let branchPhone;
    try {
      branchPhone = await validateBranchAccess(branchId, userId,undefined,session);
    } catch (error) {
      const { message, status } = handleBranchValidationError(error);
      return NextResponse.json({ error: message }, { status });
    }

    // Get all messages for this phone number using centralized function
    const messages = await getMessagesForPhoneNumber(branchPhone, phoneNumber);
  
    return NextResponse.json({ count: messages.length });
  } catch (error) {
    console.error('Error fetching message count:', error);
    return NextResponse.json(
      { error: 'Failed to fetch message count' },
      { status: 500 }
    );
  }
} 