import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Db, ObjectId } from 'mongodb';
import dbConnect from '@/lib/db';

async function sendScheduledSms(db: Db) {
    const templateId = new ObjectId("6807c9b4e39ce75bce0395bc");
    const scheduledAt = new Date("2025-04-24T08:50:00.000Z");
    
    const smsTemplate = `Bonjour {{customerInfo.client1Name}} ,
    
    J’espère que tout va bien de votre côté ! ☺️
    
    Vous aviez une réservation de prévue pour notre dégustation 5 services (offerte avec gracieuseté par nos producteurs locaux 🍷🧀), mais on comprend que les imprévus arrivent!
    
    Bonne nouvelle : votre coupon est toujours valide et vous donne droit à :
    
    ✨ Une dégustation gratuite pour 2
    🛍️ 25 $ de crédit en boutique
    👌 Aucun achat requis – juste un bon moment à partager
    🎁 Et la chance de participer à notre grand tirage : 30 lots de 2500 $ en produits + un Hyundai Tucson 2025 à gagner ! 🚗🎉
    
    Quand vous serez disponible, on sera ravis de vous accueillir. Écrivez-nous, nous pourrons vous proposer un nouveau créneau..
    
    À très bientôt,
    
    Alimentation mon quartier`;
    
    const pipeline = [
        {
          $match: {
            createdAt: {
              $gte: new Date("2025-04-24T02:01:00.000Z"),
              $lt: new Date("2025-04-24T02:02:00.000Z")
            },
            content: {
              $regex: /(?=.*\[SYSTEM\])(?=.*absent)/i
            }
          }
        },
        {
          $lookup: {
            from: "reservations",
            localField: "reservationId",
            foreignField: "_id",
            as: "reservation"
          }
        },
        {
          $unwind: "$reservation"
        },
       
      ]
    
    const scheduledSmsCollection = db.collection('scheduledsms');
    let i=0;
    console.log("Connected to database");

    const cursor = db.collection('reservationnotes').aggregate(pipeline);
    const docs = await cursor.toArray();
    console.log("Documents found:", docs);

    for (const doc of docs) {
      const reservation = doc.reservation;
      const name = reservation?.customerInfo?.client1Name || '';

      const personalizedBody = smsTemplate.replace("{{customerInfo.client1Name}}", name);

      try {
        const result = await scheduledSmsCollection.insertOne({
          templateId: templateId,
          body: personalizedBody,
          reservationId: reservation._id,
          createdAt: new Date(),
          status: "pending",
          scheduledAt: scheduledAt
        });
        console.log("Insert result:", result);
        i++;
      } catch (error) {
        console.error("Error inserting document:", error);
      }
    }

    console.log("Total documents inserted:", i);
}

export async function POST(request: NextRequest) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if(!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    //const db= (await dbConnect()).connection;
    //await sendScheduledSms(db);
    return NextResponse.json({ message: "SMS sent" }, { status: 200 });
  } catch (error) {
    console.log("Error sending SMS", error);
    return NextResponse.json({ error: error }, { status: 500 });
  }
} 

