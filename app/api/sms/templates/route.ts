import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db'; 
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SMS_PERMISSIONS } from '@/types/permission-codes';
import { ObjectId } from 'mongodb';
import { getUserRoles, getUserPermissions } from '../../utils/server-permission-utils';

const PROTECTED_CRONJOBS = ['thank_client', 'reservation_assignment'];

// GET handler to fetch SMS templates
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const db= (await dbConnect()).connection;
    const templates = await db.collection('sms_templates').find({}).toArray();

    return NextResponse.json({ templates });
  } catch (error) {
    console.error('Error fetching SMS templates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST handler to add a new SMS template
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and has the required permissions
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Check for required permission
    const userPermissions = session.user.permissions || [];
    if (!userPermissions.includes(SMS_PERMISSIONS.MANAGE_SMS)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { name } = await request.json();
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    const db= (await dbConnect()).connection;
    
    const newTemplate = {
      name,
      description: '',
      template: '',
      variables: [],
      type: 'status-trigger',
      triggerStatus: '',
      delayHours: 0,
      exactHour: null,
      disabled: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      dayDelay: 0,
    };
    const result = await db.collection('sms_templates').insertOne(newTemplate);

    return NextResponse.json({ success: true, template: { ...newTemplate, _id: result.insertedId } });
  } catch (error) {
    console.error('Error adding SMS template:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH handler to edit a template by id
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if(!session.user.roles){
      session.user.roles=await getUserRoles(session);
    }
    if(!session.user.permissions){
      session.user.permissions=await getUserPermissions(session);
    }
    const userPermissions = session.user.permissions || [];
    if (!userPermissions.includes(SMS_PERMISSIONS.MANAGE_SMS)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    const { _id, ...updateFields } = await request.json();
    if (!_id) {
      return NextResponse.json({ error: 'Template _id is required' }, { status: 400 });
    }
    const db= (await dbConnect()).connection;
    let template;
    try {
      template = await db.collection('sms_templates').findOne({ _id: new ObjectId(_id) });
    } catch (e) {
      return NextResponse.json({ error: 'Invalid _id format' }, { status: 400 });
    }
    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }
    if (template.type === 'cronjob' && PROTECTED_CRONJOBS.includes(template.id)) {
      if ('type' in updateFields || 'id' in updateFields) {
        return NextResponse.json({ error: 'Cannot change type or id of protected cronjob template' }, { status: 403 });
      }
    }
    updateFields.updatedAt = new Date();
    if (updateFields.exactHour === undefined) {
      updateFields.exactHour = null;
    }
    if (updateFields.dayDelay === undefined) {
      updateFields.dayDelay = 0;
    }
    await db.collection('sms_templates').updateOne({ _id: new ObjectId(_id) }, { $set: updateFields });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error editing SMS template:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE handler to delete a template by id
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const userPermissions = session.user.permissions || [];
    if (!userPermissions.includes(SMS_PERMISSIONS.MANAGE_SMS)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    const { _id } = await request.json();
    if (!_id) {
      return NextResponse.json({ error: 'Template _id is required' }, { status: 400 });
    }
    const db= (await dbConnect()).connection;
    let template;
    try {
      template = await db.collection('sms_templates').findOne({ _id: new ObjectId(_id) });
    } catch (e) {
      return NextResponse.json({ error: 'Invalid _id format' }, { status: 400 });
    }
    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }
    if (template.type === 'cronjob' && PROTECTED_CRONJOBS.includes(template.id)) {
      return NextResponse.json({ error: 'Cannot delete protected cronjob template' }, { status: 403 });
    }
    await db.collection('sms_templates').deleteOne({ _id: new ObjectId(_id) });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting SMS template:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 