import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import RecontactReservation from '@/models/RecontactReservation';
import mongoose from 'mongoose';
import { createObjectCsvStringifier } from 'csv-writer';
import dbConnect from '@/lib/db';

// GET - Export recontact reservations data
export const GET = async (request: NextRequest) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv';
    const statusId = searchParams.get('statusId');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Validate format
    if (!['csv', 'json'].includes(format)) {
      return NextResponse.json(
        { message: 'Invalid format. Supported formats: csv, json' },
        { status: 400 }
      );
    }

    // Build filter query
    const filter: any = {};

    if (statusId) {
      filter.statusId = new mongoose.Types.ObjectId(statusId);
    }

    if (dateFrom || dateTo) {
      filter.recontactDate = {};
      if (dateFrom) filter.recontactDate.$gte = new Date(dateFrom);
      if (dateTo) filter.recontactDate.$lte = new Date(dateTo);
    }

    // Fetch data with status information
    const data = await RecontactReservation.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'recontactreservationstatuses',
          localField: 'statusId',
          foreignField: '_id',
          as: 'status'
        }
      },
      { $unwind: '$status' },
      {
        $project: {
          _id: 1,
          recontactDate: 1,
          createdAt: 1,
          updatedAt: 1,
          statusName: '$status.name',
          statusCode: '$status.code',
          customerName: '$reservation.customerInfo.client1Name',
          customerName2: '$reservation.customerInfo.client2Name',
          customerPhone: '$reservation.customerInfo.phone',
          customerEmail: '$reservation.customerInfo.email',
          originalVisitDate: '$reservation.preferences.visitDate',
          originalVisitTime: '$reservation.preferences.visitTime',
          originalBranch: '$reservation.preferences.branchId',
          sellingAmount: '$reservation.sellingAmount',
          adultsCount: '$reservation.preferences.adultsCount',
          childrenCount: '$reservation.preferences.childrenCount',
          allergies: '$reservation.allergies',
          notes: '$reservation.notes'
        }
      },
      { $sort: { recontactDate: 1, createdAt: -1 } }
    ]);

    if (format === 'json') {
      return NextResponse.json({
        data,
        exportedAt: new Date().toISOString(),
        totalRecords: data.length,
        filters: {
          statusId,
          dateFrom,
          dateTo
        }
      });
    }

    // CSV Export
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: '_id', title: 'ID' },
        { id: 'recontactDate', title: 'Recontact Date' },
        { id: 'statusName', title: 'Status' },
        { id: 'customerName', title: 'Customer Name' },
        { id: 'customerName2', title: 'Customer Name 2' },
        { id: 'customerPhone', title: 'Phone' },
        { id: 'customerEmail', title: 'Email' },
        { id: 'originalVisitDate', title: 'Original Visit Date' },
        { id: 'originalVisitTime', title: 'Original Visit Time' },
        { id: 'sellingAmount', title: 'Selling Amount' },
        { id: 'adultsCount', title: 'Adults Count' },
        { id: 'childrenCount', title: 'Children Count' },
        { id: 'allergies', title: 'Allergies' },
        { id: 'notes', title: 'Notes' },
        { id: 'createdAt', title: 'Created At' },
        { id: 'updatedAt', title: 'Updated At' }
      ]
    });

    // Format data for CSV
    const csvData = data.map(item => ({
      ...item,
      recontactDate: item.recontactDate ? new Date(item.recontactDate).toISOString().split('T')[0] : '',
      originalVisitDate: item.originalVisitDate ? new Date(item.originalVisitDate).toISOString().split('T')[0] : '',
      createdAt: item.createdAt ? new Date(item.createdAt).toISOString() : '',
      updatedAt: item.updatedAt ? new Date(item.updatedAt).toISOString() : '',
      allergies: Array.isArray(item.allergies) ? item.allergies.join('; ') : item.allergies || ''
    }));

    const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(csvData);

    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `recontact-reservations-${timestamp}.csv`;

    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('Error exporting recontact reservations:', error);
    return NextResponse.json(
      { message: 'Failed to export data' },
      { status: 500 }
    );
  }
};
