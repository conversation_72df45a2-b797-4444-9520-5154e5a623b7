import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import mongoose from 'mongoose';
import { RecontactConversionService, ConvertToReservationParams } from '@/lib/services/recontact-conversion-service';
import ReservationStatus from '@/models/ReservationStatus';
import User from '@/models/User';
import Appointment from '@/models/Appointment';
import dbConnect from '@/lib/db';

interface ConversionRequestBody {
  appointmentId: string;
  partnerId: string;
  initialStatusId: string;
  customerInfo: {
    client1Name: string;
    hasCompanion?: boolean;
    client2Name?: string;
    city?: string;
    postalCode?: string;
    address?: string;
    phone: string;
    phone2?: string;
    email?: string;
    isPostalCodeValid?: boolean;
  };
  preferences: {
    preferredLanguage?: string;
    allergies?: string;
    foods?: string[];
    hasChildren?: boolean;
    childrenAges?: {
      age0to5?: number;
      age6to12?: number;
      age13to17?: number;
    };
    branchId: string;
    visitDate: string;
    visitTime: string;
    adultServiceTypes?: object;
    childServiceTypes?: object;
  };
}

// POST - Convert recontact reservation to normal reservation
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    const body: ConversionRequestBody = await request.json();

    // Validate recontact reservation ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: 'Invalid recontact reservation ID format' },
        { status: 400 }
      );
    }

    // Validate required fields
    const validationResult = validateRequestBody(body);
    if (!validationResult.isValid) {
      return NextResponse.json(
        { message: 'Validation failed', errors: validationResult.errors },
        { status: 400 }
      );
    }

    // Validate ObjectId formats
    const objectIdValidation = validateObjectIds(body);
    if (!objectIdValidation.isValid) {
      return NextResponse.json(
        { message: 'Invalid ID format', errors: objectIdValidation.errors },
        { status: 400 }
      );
    }

    // Validate that referenced entities exist
    const entityValidation = await validateEntities(body);
    if (!entityValidation.isValid) {
      return NextResponse.json(
        { message: 'Referenced entities not found', errors: entityValidation.errors },
        { status: 400 }
      );
    }

    // Prepare conversion parameters
    const conversionParams: ConvertToReservationParams = {
      recontactReservationId: id,
      appointmentId: body.appointmentId,
      partnerId: body.partnerId,
      initialStatusId: body.initialStatusId,
      customerInfo: {
        client1Name: body.customerInfo.client1Name,
        hasCompanion: body.customerInfo.hasCompanion || false,
        client2Name: body.customerInfo.client2Name || '',
        city: body.customerInfo.city || '',
        postalCode: body.customerInfo.postalCode || '',
        address: body.customerInfo.address || '',
        phone: body.customerInfo.phone,
        phone2: body.customerInfo.phone2 || '',
        email: body.customerInfo.email || '',
        isPostalCodeValid: body.customerInfo.isPostalCodeValid || false
      },
      preferences: {
        preferredLanguage: body.preferences.preferredLanguage || 'fr',
        allergies: body.preferences.allergies || '',
        foods: body.preferences.foods || [],
        hasChildren: body.preferences.hasChildren || false,
        childrenAges: {
          age0to5: body.preferences.childrenAges?.age0to5 || 0,
          age6to12: body.preferences.childrenAges?.age6to12 || 0,
          age13to17: body.preferences.childrenAges?.age13to17 || 0
        },
        branchId: body.preferences.branchId,
        visitDate: body.preferences.visitDate,
        visitTime: body.preferences.visitTime,
        adultServiceTypes: body.preferences.adultServiceTypes || {},
        childServiceTypes: body.preferences.childServiceTypes || {}
      },
      userId: session.user.id
    };

    // Perform the conversion
    const result = await RecontactConversionService.convertToReservation(conversionParams);

    if (!result.success) {
      return NextResponse.json(
        { message: result.error || 'Conversion failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Recontact reservation converted successfully',
      reservationId: result.reservationId
    }, { status: 201 });

  } catch (error) {
    console.error('Convert to reservation API error:', error);
    
    // Handle specific error types
    if (error instanceof mongoose.Error.ValidationError) {
      return NextResponse.json(
        { message: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { message: 'Conversion failed', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Validate request body structure and required fields
 */
function validateRequestBody(body: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check required top-level fields
  if (!body.appointmentId) errors.push('appointmentId is required');
  if (!body.partnerId) errors.push('partnerId is required');
  if (!body.initialStatusId) errors.push('initialStatusId is required');
  if (!body.customerInfo) errors.push('customerInfo is required');
  if (!body.preferences) errors.push('preferences is required');

  // Check required customer info fields
  if (body.customerInfo) {
    if (!body.customerInfo.client1Name?.trim()) {
      errors.push('customerInfo.client1Name is required');
    }
    if (!body.customerInfo.phone?.trim()) {
      errors.push('customerInfo.phone is required');
    }
  }

  // Check required preferences fields
  if (body.preferences) {
    if (!body.preferences.branchId?.trim()) {
      errors.push('preferences.branchId is required');
    }
    if (!body.preferences.visitDate?.trim()) {
      errors.push('preferences.visitDate is required');
    }
    if (!body.preferences.visitTime?.trim()) {
      errors.push('preferences.visitTime is required');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate ObjectId formats
 */
function validateObjectIds(body: ConversionRequestBody): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!mongoose.Types.ObjectId.isValid(body.appointmentId)) {
    errors.push('appointmentId must be a valid ObjectId');
  }

  if (!mongoose.Types.ObjectId.isValid(body.partnerId)) {
    errors.push('partnerId must be a valid ObjectId');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate that referenced entities exist in the database
 */
async function validateEntities(body: ConversionRequestBody): Promise<{ isValid: boolean; errors: string[] }> {
  const errors: string[] = [];

  try {
    // Validate appointment exists
    const appointment = await Appointment.findById(body.appointmentId);
    if (!appointment) {
      errors.push('Appointment not found');
    }

    // Validate partner (user) exists
    const partner = await User.findById(body.partnerId);
    if (!partner) {
      errors.push('Partner not found');
    }

    // Validate status exists (if it's an ObjectId)
    if (mongoose.Types.ObjectId.isValid(body.initialStatusId)) {
      const status = await ReservationStatus.findById(body.initialStatusId);
      if (!status) {
        errors.push('Reservation status not found');
      }
    }
    // If it's a string status code, we'll let the reservation creation handle validation

  } catch (error) {
    console.error('Error validating entities:', error);
    errors.push('Error validating referenced entities');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
