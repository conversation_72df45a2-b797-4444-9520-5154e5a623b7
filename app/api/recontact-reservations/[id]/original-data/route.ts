import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import RecontactReservation from '@/models/RecontactReservation';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// GET - Fetch original reservation data from recontact reservation
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;

    // Validate recontact reservation ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: 'Invalid recontact reservation ID format' },
        { status: 400 }
      );
    }

    // Fetch recontact reservation
    const recontactReservation = await RecontactReservation.findById(id)
      .populate('statusId', 'name code color')
      .lean();

    if (!recontactReservation) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    // Check if original reservation data exists
    if (!recontactReservation.reservation) {
      return NextResponse.json(
        { message: 'Original reservation data not found' },
        { status: 404 }
      );
    }

    // Extract and format original reservation data
    const originalReservation = recontactReservation.reservation;
    
    // Prepare response data with original reservation info
    const responseData = {
      recontactReservationId: recontactReservation._id,
      recontactDate: recontactReservation.recontactDate,
      recontactStatus: recontactReservation.statusId,
      isConverted: !!recontactReservation.reservationId,
      convertedReservationId: recontactReservation.reservationId || null,
      originalReservation: {
        _id: originalReservation._id,
        appointmentId: originalReservation.appointmentId,
        partnerId: originalReservation.partnerId,
        type: originalReservation.type,
        status: originalReservation.status,
        source: originalReservation.source,
        customerInfo: {
          client1Name: originalReservation.customerInfo?.client1Name || '',
          hasCompanion: originalReservation.customerInfo?.hasCompanion || false,
          client2Name: originalReservation.customerInfo?.client2Name || '',
          city: originalReservation.customerInfo?.city || '',
          postalCode: originalReservation.customerInfo?.postalCode || '',
          address: originalReservation.customerInfo?.address || '',
          phone: originalReservation.customerInfo?.phone || '',
          phone2: originalReservation.customerInfo?.phone2 || '',
          email: originalReservation.customerInfo?.email || '',
          isPostalCodeValid: originalReservation.customerInfo?.isPostalCodeValid || false
        },
        preferences: {
          preferredLanguage: originalReservation.preferences?.preferredLanguage || 'fr',
          allergies: originalReservation.preferences?.allergies || '',
          foods: originalReservation.preferences?.foods || [],
          hasChildren: originalReservation.preferences?.hasChildren || false,
          childrenAges: {
            age0to5: originalReservation.preferences?.childrenAges?.age0to5 || 0,
            age6to12: originalReservation.preferences?.childrenAges?.age6to12 || 0,
            age13to17: originalReservation.preferences?.childrenAges?.age13to17 || 0
          },
          branchId: originalReservation.preferences?.branchId || '',
          visitDate: originalReservation.preferences?.visitDate || '',
          visitTime: originalReservation.preferences?.visitTime || '',
          adultServiceTypes: originalReservation.preferences?.adultServiceTypes || {},
          childServiceTypes: originalReservation.preferences?.childServiceTypes || {}
        },
        createdAt: originalReservation.createdAt,
        updatedAt: originalReservation.updatedAt
      }
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Get original data API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { message: 'Failed to fetch original data', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
