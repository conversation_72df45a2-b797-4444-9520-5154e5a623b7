import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import InvitationGroup from '@/models/InvitationGroup';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to access this resource' },
        { status: 401 }
      );
    }

  await dbConnect();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const partnerId = searchParams.get('partnerId');
    const showInactive = searchParams.get('showInactive') === 'true';

    // Build query object
    const query: any = {};

    // Filter by partner if specified
    if (partnerId && partnerId !== 'all') {
      query.partnerId = new mongoose.Types.ObjectId(partnerId);
    }

    // Filter by active status - by default only show active groups
    if (!showInactive) {
      query.isActive = { $ne: false }; // Include groups where isActive is true or undefined (for backward compatibility)
    }

    // Fetch invitation groups with invitation and reservation counts
    const groups = await InvitationGroup.aggregate([
      { $match: query },
      {
        $lookup: {
          from: 'invitations',
          localField: '_id',
          foreignField: 'groupId',
          as: 'invitations'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'partnerId',
          foreignField: '_id',
          as: 'partner'
        }
      },
      {
        $unwind: '$partner'
      },
      {
        $addFields: {
          // Count invitations with non-null reservation_id
          reservationCount: {
            $size: {
              $filter: {
                input: '$invitations',
                cond: { $ne: ['$$this.reservation_id', null] }
              }
            }
          }
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          createdAt: 1,
          isActive: { $ifNull: ['$isActive', true] }, // Default to true for backward compatibility
          partnerId: {
            _id: '$partner._id',
            name: '$partner.name',
            email: '$partner.email'
          },
          invitationCount: { $size: '$invitations' },
          reservationCount: 1,
          conversionRate: {
            $cond: {
              if: { $gt: [{ $size: '$invitations' }, 0] },
              then: {
                $multiply: [
                  { $divide: ['$reservationCount', { $size: '$invitations' }] },
                  100
                ]
              },
              else: 0
            }
          }
        }
      },
      { $sort: { createdAt: -1 } }
    ]);

    return NextResponse.json({ groups });

  } catch (error: any) {
    console.error('Error fetching invitation groups:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch invitation groups' },
      { status: 500 }
    );
  }
}
