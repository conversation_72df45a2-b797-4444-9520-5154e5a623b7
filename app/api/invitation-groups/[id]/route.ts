import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import InvitationGroup from '@/models/InvitationGroup';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to update invitation groups' },
        { status: 401 }
      );
    }

  await dbConnect();

    // Validate the group ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: 'Invalid group ID' },
        { status: 400 }
      );
    }

    const body = await req.json();

    // Validate the request body
    if (typeof body.isActive !== 'boolean') {
      return NextResponse.json(
        { error: 'isActive field is required and must be a boolean' },
        { status: 400 }
      );
    }

    // Update the invitation group
    const updatedGroup = await InvitationGroup.findByIdAndUpdate(
      params.id,
      { isActive: body.isActive },
      { new: true, runValidators: true }
    );

    if (!updatedGroup) {
      return NextResponse.json(
        { error: 'Invitation group not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      group: updatedGroup 
    });

  } catch (error: any) {
    console.error('Error updating invitation group:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update invitation group' },
      { status: 500 }
    );
  }
}
