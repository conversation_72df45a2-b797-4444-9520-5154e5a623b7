import { NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';
import Appointment from '@/models/Appointment';
import Reservation from '@/models/Reservation';
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const month = searchParams.get('month');
    const year = searchParams.get('year');
    const branchId = searchParams.get('branchId');
    const hasChildren = searchParams.get('enfants') === 'true';

    if (!month || !year || !branchId) {
      return NextResponse.json(
        { error: 'Month, year and branchId are required' },
        { status: 400 }
      );
    }

    // Connect to database
  await dbConnect();
    
    // Create date range for the specified month
    const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
    const endDate = new Date(parseInt(year), parseInt(month), 0);

    // 1. Get all appointments for the month for this branch
    const appointments = await Appointment.find({
      date: {
        $gte: startDate.toISOString().split('T')[0],
        $lte: endDate.toISOString().split('T')[0]
      },
      branchId: new ObjectId(branchId),
      capacity: { $gt: 0 }
    }).lean();

    // 2. Get all reservations for these appointments
    const appointmentIds = appointments.map(app => app._id);
    const reservations = await Reservation.find({
      appointmentId: { $in: appointmentIds },
      type: 'branch',
      status: { $ne: 'cancelled' }
    }).lean();

    // 3. Count total reservations and family reservations per appointment
    const reservationCounts = reservations.reduce((acc, res) => {
      const appointmentId = res.appointmentId.toString();
      if (!acc[appointmentId]) {
        acc[appointmentId] = { total: 0, family: 0 };
      }
      acc[appointmentId].total += 1;
      if (res.preferences?.hasChildren) {
        acc[appointmentId].family += 1;
      }
      return acc;
    }, {} as Record<string, { total: number; family: number }>);

    // 4. Calculate actual availability based on capacity constraints
    const availableDates = new Set<string>();
    const timeSlotsByDate = appointments.reduce((acc, app) => {
      const counts = reservationCounts[app._id.toString()] || { total: 0, family: 0 };
      const totalReservations = counts.total;
      const familyReservations = counts.family;
      
      let remainingSpots = app.capacity - totalReservations;
      let remainingFamilySpots = app.max_capacity_family - familyReservations;

      // Determine if the slot is full
      const isFull = hasChildren
        ? (remainingSpots <= 0 || remainingFamilySpots <= 0)
        : (remainingSpots <= 0);

      if (!acc[app.date]) {
        acc[app.date] = [];
        availableDates.add(app.date);
      }

      acc[app.date].push({
        _id: app._id.toString(),
        startHour: app.startHour,
        endHour: app.endHour,
        remainingSpots,
        remainingFamilySpots,
        capacity: app.capacity,
        maxCapacityFamily: app.max_capacity_family,
        isFull
      });

      return acc;
    }, {} as Record<string, Array<{
      _id: string;
      startHour: string;
      endHour: string;
      remainingSpots: number;
      remainingFamilySpots: number;
      capacity: number;
      maxCapacityFamily: number;
      isFull: boolean;
    }>>);

    // Filter out time slots before 11:00 AM and sort time slots by startHour for each date
    for (const date in timeSlotsByDate) {
      // Filter out time slots before 11:00 AM
      timeSlotsByDate[date] = timeSlotsByDate[date].filter(slot => {
        const startHour = parseInt(slot.startHour.split(':')[0]);
        return startHour >= 11;
      });

      // Sort remaining time slots by startHour
      timeSlotsByDate[date].sort((a, b) => {
        return a.startHour.localeCompare(b.startHour);
      });
    }

    // Remove dates that have no available slots after filtering out time slots before 11am
    const finalAvailableDates = Array.from(availableDates).filter(date =>
      timeSlotsByDate[date] && timeSlotsByDate[date].length > 0
    );

    return NextResponse.json({
      availableDates: finalAvailableDates,
      timeSlotsByDate
    });
  } catch (error) {
    console.error('[RESERVATIONS_BRANCH_DISPONIBILITIES_GET]', error);
    return NextResponse.json(
      { error: 'Failed to fetch disponibilities' },
      { status: 500 }
    );
  }
} 