import { NextResponse } from 'next/server';
import mongoose from 'mongoose';
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { ObjectId } from "mongodb";
import Reservation from "@/models/Reservation";
import { canUserViewAssignedReservations, canUserViewOwnBranchReservations, canUserViewOwnReservations, canUserViewPhoneReservationStatuses, canUserViewAllReservations, canUserViewReservationStatuses, doesUserHaveReservationAccess, canUserViewOnlyOwnReservations, canUserViewOnlyAssignedReservations, canUserViewSellerReservationStatuses } from '@/lib/utils/permissions-utils';
import { normalizePhoneNumber } from '@/lib/twilio';
import ReservationStatus from '@/models/ReservationStatus';
import { isSeller } from '@/lib/utils/role-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { convertDateToTimezone, createDateRange } from '@/lib/utils/timezone-utils';
import dbConnect from '@/lib/db';

// Fast endpoint that skips expensive operations for initial page load
export async function GET(request: Request) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);

    // Get the user session
    const session = await getServerSession(authOptions);
    
    if(session && !session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a SuperAdmin
    const isSuperAdmin = session.user.roles?.some((role: any) =>
      (typeof role === 'object' ? role._id?.toString() === '67add3214badd3283e873329' : role?.toString() === '67add3214badd3283e873329')
    );

    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const skip = (page - 1) * limit;
    
    // Sorting parameters
    const sort = searchParams.get('sort');
    const order = searchParams.get('order') === 'desc' ? -1 : 1;
    const sortOptions: { [key: string]: 1 | -1 } = {};

    if (sort) {
      if (sort === 'preferences.visitTime' || sort === 'preferences_visitTime') {
        sortOptions['visitTimeForSort'] = order;
      } else {
        sortOptions[sort] = order;
      }
    } else {
      sortOptions['createdAt'] = -1; // Default sort
    }

    // Basic filtering parameters
    const search = searchParams.get('search');
    const includeDeleted = searchParams.get('includeDeleted') === 'true';
    const branchId = searchParams.get('branchId');
    const assignedTo = searchParams.get('assignedTo');
    const createdAtDate = searchParams.get('createdAtDate');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const allDates = searchParams.get('allDates') === 'true';
    const status = searchParams.get('status');
    const statusCodes = searchParams.get('statusCodes');
    const type = searchParams.get('type');
    const partnerId = searchParams.get('partnerId');
    const visitTime = searchParams.get('visitTime');
    const createdAtStartDate = searchParams.get('createdAtStartDate');
    const createdAtEndDate = searchParams.get('createdAtEndDate');
    const clientTimezone = searchParams.get('timezone') || 'America/Toronto'; // Default to Quebec timezone
    const phones = searchParams.get('phones');
    const ids = searchParams.get('ids');

    const db= (await dbConnect()).connection;


    if(!doesUserHaveReservationAccess(session.user)){
      return NextResponse.json({ reservations: [], totalCount: -1, page, limit });
    }



    // Build a minimal pipeline for fast loading
    const pipeline: any[] = [];

    // Add search filter
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      pipeline.push({
        $match: {
          $or: [
            { 'customerInfo.client1Name': searchRegex },
            { 'customerInfo.client2Name': searchRegex },
            { 'customerInfo.phone': searchRegex },
            { 'customerInfo.email': searchRegex },
          ]
        }
      });
    }

    // Basic date filtering
    if (!allDates && (startDate || endDate)) {
      pipeline.push({
        $addFields: {
          visitDateOnly: {
            $substr: ["$preferences.visitDate", 0, 10]
          }
        }
      });

      const dateMatch: any = {};
      if (startDate) dateMatch['visitDateOnly'] = { $gte: startDate };
      if (endDate) dateMatch['visitDateOnly'] = { ...dateMatch['visitDateOnly'], $lte: endDate };

      pipeline.push({ $match: dateMatch });
    }

    // Creation date filtering (with timezone adjustment)
    if (createdAtDate) {
      const { startOfDay, endOfDay } = createDateRange(createdAtDate, clientTimezone);
      console.log('startOfDay:', startOfDay.toISOString());
      console.log('endOfDay:', endOfDay.toISOString());
      pipeline.push({
        $match: {
          createdAt: {
            $gte: startOfDay,
            $lte: endOfDay
          }
        }
      });
    }

    // Delete status filter
    pipeline.push({
      $match: includeDeleted
        ? { isDeleted: true }
        : { isDeleted: { $ne: true }}
    });

    // Branch access control (matches original API logic)
    const branchCollection = db.collection('branches');
    let accessibleBranchIds: string[] = [];

    if (canUserViewAllReservations(session.user) || canUserViewOnlyOwnReservations(session.user) || isSuperAdmin) {
      // User can see all branches
      accessibleBranchIds = (await branchCollection.find({}).project({ _id: 1 }).toArray()).map(branch => branch._id.toString());
    } else if (canUserViewOwnBranchReservations(session.user) || canUserViewAssignedReservations(session.user)) {
      const userBranches = await branchCollection.find({
        $or: [
          { responsible: new ObjectId(session.user.id) },
          { agents: new ObjectId(session.user.id) },
          { sellers: new ObjectId(session.user.id) }
        ]
      }).toArray();
      accessibleBranchIds = userBranches.map(branch => branch._id.toString());
    }

    // Apply branch filtering (matches original API logic)
    if (accessibleBranchIds.length > 0) {
      // If specific branches were requested
      if (branchId && branchId !== 'all') {
        // Handle comma-separated branch IDs
        const requestedBranchIds = branchId.split(',');

        // Filter to only include branches the user has access to
        const filteredBranchIds = requestedBranchIds.filter(id =>
          accessibleBranchIds.includes(id)
        );

        // If user has access to at least one requested branch
        if (filteredBranchIds.length > 0) {
          // Convert to ObjectId for MongoDB query
          const objectIds = filteredBranchIds.map(id => new ObjectId(id));

          pipeline.push({
            $match: {
              $or: [
                // Match string IDs (for compatibility)
                { 'preferences.branchId': { $in: filteredBranchIds } },
                // Match ObjectId IDs
                { 'preferences.branchId': { $in: objectIds } }
              ]
            }
          });
        } else {
          // User doesn't have access to any requested branch
          // Return empty result by using a non-existent branch ID
          pipeline.push({
            $match: { 'preferences.branchId': 'non-existent-id' }
          });
        }
      } else {
        // No specific branch requested, show all branches user has access to
        pipeline.push({
          $match: {
            $or: [
              { 'preferences.branchId': { $in: accessibleBranchIds } },
              { 'preferences.branchId': { $in: accessibleBranchIds.map(id => new ObjectId(id)) } }
            ]
          }
        });
      }
    }

    // User-specific filters
    if(canUserViewOnlyOwnReservations(session.user)){
      pipeline.push({
        $match: { partnerId: new ObjectId(session.user.id) }
      });
    }

    if(canUserViewOnlyAssignedReservations(session.user)){
      pipeline.push({
        $match: { assigned_user_id: new ObjectId(session.user.id) }
      });
    }

    // If user is a seller, only show their assigned reservations
    if (isSeller(session.user.permissions) && session?.user?.id) {
      pipeline.push({
        $match: { assigned_user_id: new ObjectId(session.user.id) }
      });
    }

    // Assigned user filter (handle comma-separated values)
    if (assignedTo) {
      const assignedToIds = assignedTo.split(',').map(id => new ObjectId(id));
      pipeline.push({
        $match: {
          assigned_user_id: assignedToIds.length > 1 ? { $in: assignedToIds } : assignedToIds[0]
        }
      });
    }

    // Status filter (basic - without complex lookups)
    if (status && status !== 'all') {
      const statusArray = status.split(',');
      pipeline.push({
        $match: { status: { $in: statusArray } }
      });
    }

    // Status codes filter
    if (statusCodes && statusCodes !== 'all') {
      const statusCodesArray = statusCodes.split(',');
      pipeline.push({
        $match: { status: { $in: statusCodesArray } }
      });
    }

    // Type filter
    if (type && type !== 'all') {
      const typeArray = type.split(',');
      pipeline.push({
        $match: { type: { $in: typeArray } }
      });
    }

    // Partner filter
    if (partnerId && partnerId !== 'all') {
      const partnerArray = partnerId.split(',');
      const partnerObjectIds = partnerArray.map(id => new ObjectId(id));
      pipeline.push({
        $match: { partnerId: { $in: partnerObjectIds } }
      });
    }

    // Visit time filter (matches original API logic)
    if (visitTime && visitTime !== 'all') {
      // Handle comma-separated list of visit times
      const visitTimes = visitTime.split(',');

      // Create an array of patterns to match against (if filters are ["11", "14"], create patterns ["11:00", "14:00"])
      const timePatterns = visitTimes.map(time => `${time}:00`);

      pipeline.push({
        $match: {
          $expr: {
            $let: {
              vars: {
                startTime: { $arrayElemAt: [{ $split: ["$preferences.visitTime", "-"] }, 0] }
              },
              in: { $in: ["$$startTime", timePatterns] }
            }
          }
        }
      });
    }

    // Creation date range filter (with timezone adjustment)
    if (createdAtStartDate || createdAtEndDate) {
      const createdAtMatch: any = {};
      if (createdAtStartDate) {
        // Handle both ISO datetime strings and YYYY-MM-DD date strings
        const startDateStr = createdAtStartDate.includes('T')
          ? createdAtStartDate.split('T')[0]
          : createdAtStartDate;
        createdAtMatch.$gte = convertDateToTimezone(startDateStr, false, clientTimezone);
        console.log('Creation date start filter:', {
          original: createdAtStartDate,
          parsed: startDateStr,
          converted: createdAtMatch.$gte.toISOString(),
          timezone: clientTimezone
        });
      }
      if (createdAtEndDate) {
        // Handle both ISO datetime strings and YYYY-MM-DD date strings
        const endDateStr = createdAtEndDate.includes('T')
          ? createdAtEndDate.split('T')[0]
          : createdAtEndDate;
        createdAtMatch.$lte = convertDateToTimezone(endDateStr, true, clientTimezone);
        console.log('Creation date end filter:', {
          original: createdAtEndDate,
          parsed: endDateStr,
          converted: createdAtMatch.$lte.toISOString(),
          timezone: clientTimezone
        });
      }
      pipeline.push({
        $match: { createdAt: createdAtMatch }
      });
    }

    // Add visitTimeForSort field if needed
    if (sort === 'preferences.visitTime' || sort === 'preferences_visitTime') {
      pipeline.push({
        $addFields: {
          visitTimeForSort: {
            $substr: ["$preferences.visitTime", 0, 5]
          }
        }
      });
    }

    // Add sorting
    pipeline.push({ $sort: sortOptions });

    // Add pagination
    pipeline.push(
      { $skip: skip },
      { $limit: limit }
    );

    // Execute the minimal pipeline
    const reservations = await Reservation.aggregate(pipeline);

    // Return with totalCount = -1 to signal that count was skipped
    return NextResponse.json({
      reservations,
      totalCount: -1, // Signal that count was skipped for performance
      page,
      limit
    });

  } catch (error) {
    console.error('Error fetching reservations (fast):', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      {
        error: 'Failed to fetch reservations',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}
