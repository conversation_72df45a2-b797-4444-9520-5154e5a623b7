import { NextResponse } from 'next/server';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/db';
import Reservation from '@/models/Reservation';
import { RESERVATION_PERMISSIONS } from '@/types/permission-codes';
import { Session } from 'next-auth';
import mongoose from 'mongoose';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

export async function DELETE(
  request: Request,
  context: { params: { id: string } }
) {
  // Await params in Next.js route handlers
  const { id } = await Promise.resolve(context.params);
  const session = await getServerSession(authOptions) as Session;

  if (!session?.user) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }
  if (!session.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }

  // Check if the user has permission to delete reservations
  if (!session.user.permissions.includes(RESERVATION_PERMISSIONS.DELETE_RESERVATIONS)) {
    return NextResponse.json({
      message: 'Forbidden: You do not have permission to delete reservations.',
      debug: {
        userPermissions: session.user.permissions,
        requiredPermission: RESERVATION_PERMISSIONS.DELETE_RESERVATIONS
      }
    }, { status: 403 });
  }

  if (!id || typeof id !== 'string') {
    return NextResponse.json({ message: 'Invalid reservation ID' }, { status: 400 });
  }

  try {
    console.log('Connecting to database and attempting soft delete...');
    const db= (await dbConnect()).connection;

    // First, ensure the document exists
    const exists = await Reservation.findById(id);
    if (!exists) {
      return NextResponse.json({ message: 'Reservation not found' }, { status: 404 });
    }

    if (exists.isDeleted) {
      return NextResponse.json({ message: 'Reservation is already deleted' }, { status: 400 });
    }

    // Force add the soft delete fields using raw MongoDB operation
    console.log('Soft deleting reservation:', id);

    

    const collection = db.collection('reservations');
    const objectId = new mongoose.Types.ObjectId(id);

    // Perform the update
    const updateResult = await collection.updateOne(
      { _id: objectId },
      {
        $set: {
          isDeleted: true,
          deletedAt: new Date()
        }
      },
      { upsert: false }
    );

    console.log('Update operation result:', updateResult);

    if (!updateResult.acknowledged || updateResult.modifiedCount === 0) {
      throw new Error('Failed to soft delete reservation');
    }

    // Verify the update by fetching the document
    const updatedDoc = await collection.findOne({ _id: objectId });

    console.log('Updated document:', updatedDoc);

    if (!updatedDoc) {
      throw new Error('Failed to verify update - document not found');
    }

    return NextResponse.json({
      message: 'Reservation soft deleted successfully',
      debug: 'Fields added using raw MongoDB operation',
      reservation: {
        id: updatedDoc._id.toString(),
        isDeleted: Boolean(updatedDoc.isDeleted),
        deletedAt: updatedDoc.deletedAt ? new Date(updatedDoc.deletedAt).toISOString() : null
      }
    }, { status: 200 });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error soft deleting reservation:', {
      error: errorMessage,
      reservationId: id,
      userId: session.user.id
    });

    return NextResponse.json({
      message: 'Internal Server Error',
      error: errorMessage
    }, { status: 500 });
  }
}