import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { RecontactTransferService } from '@/lib/services/recontact-transfer-service';
import dbConnect from '@/lib/db';

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    const body = await request.json();
    
    const { recontactDate, statusId } = body;

    if (!recontactDate || !statusId) {
      return NextResponse.json(
        { message: 'Recontact date and status ID are required' },
        { status: 400 }
      );
    }

    // Validate date format
    const parsedDate = new Date(recontactDate);
    if (isNaN(parsedDate.getTime())) {
      return NextResponse.json(
        { message: 'Invalid recontact date format' },
        { status: 400 }
      );
    }

    // Perform the transfer
    const result = await RecontactTransferService.transferToRecontact({
      reservationId: id,
      recontactDate: parsedDate,
      statusId,
      userId: session.user.id
    });

    if (!result.success) {
      return NextResponse.json(
        { message: result.error || 'Transfer failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Reservation transferred to recontact successfully',
      recontactReservationId: result.recontactReservationId
    });

  } catch (error) {
    console.error('Transfer to recontact API error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
