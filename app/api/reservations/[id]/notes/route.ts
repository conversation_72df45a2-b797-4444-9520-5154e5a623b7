import { NextRequest, NextResponse } from 'next/server';
import ReservationNote from '@/models/ReservationNote';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { logAuditEvent } from '@/lib/utils/audit-utils';
import dbConnect from '@/lib/db';

// Helper to get reservation ID from params
async function getReservationId(params: { id: string }) {
  // This function is an async wrapper around accessing params.id
  let ps=await params;
  return ps.id;
}

// GET handler to fetch notes for a specific reservation
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
  await dbConnect();
    // Get reservation ID using the async helper
    const reservationId = await getReservationId(params);

    // Validate reservationId format
    if (!mongoose.Types.ObjectId.isValid(reservationId)) {
      return NextResponse.json({ error: 'Invalid reservation ID format' }, { status: 400 });
    }

    // Check if count parameter is present
    const searchParams = request.nextUrl.searchParams;
    const countOnly = searchParams.get('count') === 'true';

    if (countOnly) {
      // Return only the count if count parameter is true
      const count = await ReservationNote.countDocuments({ reservationId });
      return NextResponse.json({ count });
    }

    // Fetch notes for the reservation, sorted by creation date (oldest first)
    const notes = await ReservationNote.find({ reservationId })
      .sort({ createdAt: 1 })
      .populate({
        path: 'userId',
        select: 'name email',
        // Handle deleted users
        transform: doc => doc || { name: 'Deleted User', email: '' }
      })
      .lean();
    return NextResponse.json({ notes });
  } catch (error: any) {
    console.error('Error fetching reservation notes:', error);
    return NextResponse.json({ error: error.message || 'Failed to fetch notes' }, { status: 500 });
  }
}

// POST handler to add a new note to a reservation
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

  await dbConnect();

    // Get reservation ID using the async helper
    const reservationId = await getReservationId(params);
    const { content } = await request.json();

    // Validate reservationId format
    if (!mongoose.Types.ObjectId.isValid(reservationId)) {
      return NextResponse.json({ error: 'Invalid reservation ID format' }, { status: 400 });
    }

    // Validate content
    if (!content || content.trim() === '') {
      return NextResponse.json({ error: 'Note content cannot be empty' }, { status: 400 });
    }

    // Create a new note
    const note = await ReservationNote.create({
      reservationId,
      userId: new mongoose.Types.ObjectId(session.user.id), // Assuming user.id is available from session
      content,
    });

    // Fetch the created note with user information
    const populatedNote = await ReservationNote.findById(note._id)
      .populate({
        path: 'userId',
        select: 'name email',
        // Handle deleted users
        transform: doc => doc || { name: 'Deleted User', email: '' }
      })
      .lean();

    // Log audit event for note creation
    try {
      await logAuditEvent(request, {
        action: 'CREATED',
        entityType: 'Reservation',
        entityId: reservationId,
        entityName: `Note added to reservation`,
        description: `Note added to reservation: "${content.substring(0, 50)}${content.length > 50 ? '...' : ''}"`,
        metadata: {
          noteId: note._id.toString(),
          noteContent: content,
          noteAction: 'created'
        }
      }, session);
    } catch (auditError) {
      console.error('Failed to log note creation audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    return NextResponse.json({ note: populatedNote }, { status: 201 });
  } catch (error: any) {
    console.error('Error adding reservation note:', error);
    return NextResponse.json({ error: error.message || 'Failed to add note' }, { status: 500 });
  }
}

// PUT handler to update a note
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; noteId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

  await dbConnect();

    // Parse the request URL to get noteId
    const url = new URL(request.url);
    const path = url.pathname.split('/');
    const noteId = path[path.length - 1];

    // Validate noteId format
    if (!mongoose.Types.ObjectId.isValid(noteId)) {
      return NextResponse.json({ error: 'Invalid note ID format' }, { status: 400 });
    }

    const { content } = await request.json();

    // Validate content
    if (!content || content.trim() === '') {
      return NextResponse.json({ error: 'Note content cannot be empty' }, { status: 400 });
    }

    // Find the note
    const note = await ReservationNote.findById(noteId);

    if (!note) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 });
    }

    // Check if the user is the author of the note
    if (note.userId.toString() !== session.user.id) {
      return NextResponse.json({ error: 'You can only edit your own notes' }, { status: 403 });
    }

    // Update the note
    note.content = content;
    await note.save();

    // Fetch the updated note with user information
    const updatedNote = await ReservationNote.findById(noteId)
      .populate({
        path: 'userId',
        select: 'name email',
        // Handle deleted users
        transform: doc => doc || { name: 'Deleted User', email: '' }
      })
      .lean();

    // Log audit event for note update
    try {
      await logAuditEvent(request, {
        action: 'UPDATED',
        entityType: 'Reservation',
        entityId: reservationId,
        entityName: `Note updated in reservation`,
        description: `Note updated in reservation: "${content.substring(0, 50)}${content.length > 50 ? '...' : ''}"`,
        metadata: {
          noteId: noteId,
          noteContent: content,
          noteAction: 'updated'
        }
      }, session);
    } catch (auditError) {
      console.error('Failed to log note update audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    return NextResponse.json({ note: updatedNote });
  } catch (error: any) {
    console.error('Error updating reservation note:', error);
    return NextResponse.json({ error: error.message || 'Failed to update note' }, { status: 500 });
  }
}

// DELETE handler to delete a note
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; noteId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

  await dbConnect();

    // Parse the request URL to get noteId
    const url = new URL(request.url);
    const path = url.pathname.split('/');
    const noteId = path[path.length - 1];

    // Validate noteId format
    if (!mongoose.Types.ObjectId.isValid(noteId)) {
      return NextResponse.json({ error: 'Invalid note ID format' }, { status: 400 });
    }

    // Find the note
    const note = await ReservationNote.findById(noteId);

    if (!note) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 });
    }

    // Check if the user is the author of the note
    if (note.userId.toString() !== session.user.id) {
      return NextResponse.json({ error: 'You can only delete your own notes' }, { status: 403 });
    }

    // Delete the note
    await ReservationNote.findByIdAndDelete(noteId);

    return NextResponse.json({ message: 'Note deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting reservation note:', error);
    return NextResponse.json({ error: error.message || 'Failed to delete note' }, { status: 500 });
  }
}