import { NextRequest, NextResponse } from 'next/server';
import ReservationNote from '@/models/ReservationNote';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// Helper to validate ObjectId
const isValidObjectId = (id: string) => mongoose.Types.ObjectId.isValid(id);

// PUT handler to update a note
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; noteId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

  await dbConnect();
    
    const { id: reservationId, noteId } =await params;

    // Validate IDs format
    if (!isValidObjectId(reservationId) || !isValidObjectId(noteId)) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }

    const { content } = await request.json();

    // Validate content
    if (!content || content.trim() === '') {
      return NextResponse.json({ error: 'Note content cannot be empty' }, { status: 400 });
    }

    // Find the note
    const note = await ReservationNote.findById(noteId);
    
    if (!note) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 });
    }
    
    // Verify note belongs to the reservation
    if (note.reservationId.toString() !== reservationId) {
      return NextResponse.json({ error: 'Note does not belong to this reservation' }, { status: 400 });
    }
    
    // Check if the user is the author of the note
    if (note.userId.toString() !== session.user.id) {
      return NextResponse.json({ error: 'You can only edit your own notes' }, { status: 403 });
    }
    
    // Update the note
    note.content = content;
    await note.save();
    
    // Fetch the updated note with user information
    const updatedNote = await ReservationNote.findById(noteId)
      .populate('userId', 'name email')
      .lean();
      
    return NextResponse.json({ note: updatedNote });
  } catch (error: any) {
    console.error('Error updating reservation note:', error);
    return NextResponse.json({ error: error.message || 'Failed to update note' }, { status: 500 });
  }
}

// DELETE handler to delete a note
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; noteId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

  await dbConnect();
    
    const { id: reservationId, noteId } =await params;

    // Validate IDs format
    if (!isValidObjectId(reservationId) || !isValidObjectId(noteId)) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }

    // Find the note
    const note = await ReservationNote.findById(noteId);
    
    if (!note) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 });
    }
    
    // Verify note belongs to the reservation
    if (note.reservationId.toString() !== reservationId) {
      return NextResponse.json({ error: 'Note does not belong to this reservation' }, { status: 400 });
    }
    
    // Check if the user is the author of the note
    if (note.userId.toString() !== session.user.id) {
      return NextResponse.json({ error: 'You can only delete your own notes' }, { status: 403 });
    }
    
    // Delete the note
    await ReservationNote.findByIdAndDelete(noteId);
      
    return NextResponse.json({ message: 'Note deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting reservation note:', error);
    return NextResponse.json({ error: error.message || 'Failed to delete note' }, { status: 500 });
  }
} 