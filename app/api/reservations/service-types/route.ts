import { NextRequest, NextResponse } from 'next/server';
import ServiceType from '@/models/ServiceType';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);


    // Basic authentication check
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Get all service types
    const serviceTypes = await ServiceType.find({}).sort({ name: 1 });

    return NextResponse.json(serviceTypes);
  } catch (error) {
    console.error('Error fetching service types:', error);
    return NextResponse.json({ error: 'Failed to fetch service types' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);
    // Basic authentication check
    if (!session) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const data = await req.json();

    // Validate required fields
    if (!data.code || !data.name) {
      return NextResponse.json({ error: 'Code and name are required' }, { status: 400 });
    }

    // Check if code already exists
    const existingServiceType = await ServiceType.findOne({ code: data.code });
    if (existingServiceType) {
      return NextResponse.json({ error: 'Service type with this code already exists' }, { status: 400 });
    }
    console.log('data', data);
    // Create new service type
    const serviceType = await ServiceType.create({
      code: data.code,
      name: data.name,
      description: data.description || '',
      forAdults: !!data.forAdults,
      forChildren: !!data.forChildren,
      isActive: data.isActive !== undefined ? data.isActive : true
    });

    return NextResponse.json(serviceType, { status: 201 });
  } catch (error) {
    console.error('Error creating service type:', error);
    return NextResponse.json({ error: 'Failed to create service type' }, { status: 500 });
  }
} 