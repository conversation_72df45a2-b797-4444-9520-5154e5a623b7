import { NextRequest, NextResponse } from 'next/server';
import ServiceType from '@/models/ServiceType';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// GET endpoint to fetch a specific service type
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    // Basic authentication check
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }

    // Find the service type
    const serviceType = await ServiceType.findById(id);

    if (!serviceType) {
      return NextResponse.json({ error: 'Service type not found' }, { status: 404 });
    }

    return NextResponse.json(serviceType);
  } catch (error) {
    console.error('Error fetching service type:', error);
    return NextResponse.json({ error: 'Failed to fetch service type' }, { status: 500 });
  }
}

// PUT endpoint to update a specific service type
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);
    if(!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { id } = await params;
    const data = await req.json();

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }

    // Validate required fields
    if (data.code !== undefined && !data.code) {
      return NextResponse.json({ error: 'Code cannot be empty' }, { status: 400 });
    }

    if (data.name !== undefined && !data.name) {
      return NextResponse.json({ error: 'Name cannot be empty' }, { status: 400 });
    }

    // Check if service type exists
    const existingServiceType = await ServiceType.findById(id);
    if (!existingServiceType) {
      return NextResponse.json({ error: 'Service type not found' }, { status: 404 });
    }

    // Check if code already exists (if changing code)
    if (data.code && data.code !== existingServiceType.code) {
      const codeExists = await ServiceType.findOne({ code: data.code, _id: { $ne: id } });
      if (codeExists) {
        return NextResponse.json({ error: 'Service type with this code already exists' }, { status: 400 });
      }
    }

    // Update service type
    const updatedServiceType = await ServiceType.findByIdAndUpdate(
      id,
      {
        ...(data.code !== undefined && { code: data.code }),
        ...(data.name !== undefined && { name: data.name }),
        ...(data.description !== undefined && { description: data.description }),
        ...(data.forAdults !== undefined && { forAdults: !!data.forAdults }),
        ...(data.forChildren !== undefined && { forChildren: !!data.forChildren }),
        ...(data.isActive !== undefined && { isActive: !!data.isActive })
      },
      { new: true }
    );

    return NextResponse.json(updatedServiceType);
  } catch (error) {
    console.error('Error updating service type:', error);
    return NextResponse.json({ error: 'Failed to update service type' }, { status: 500 });
  }
}

// DELETE endpoint to delete a specific service type
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and has admin role
    if (!session || !(session.user as any)?.roles?.includes('admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }

    // Check if service type exists
    const serviceType = await ServiceType.findById(id);
    if (!serviceType) {
      return NextResponse.json({ error: 'Service type not found' }, { status: 404 });
    }

    // Instead of actually deleting, set to inactive
    const deletedServiceType = await ServiceType.findByIdAndUpdate(
      id,
      { isActive: false },
      { new: true }
    );

    return NextResponse.json(deletedServiceType);
  } catch (error) {
    console.error('Error deleting service type:', error);
    return NextResponse.json({ error: 'Failed to delete service type' }, { status: 500 });
  }
} 