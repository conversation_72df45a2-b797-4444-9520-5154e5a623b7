import { NextResponse } from "next/server";
import Allergy from "@/models/Allergy";
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  try {
    // Get the language from query params
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'fr';

    // Connect to database
  await dbConnect();

    // Fetch allergies and await the result
    const allergies = await Allergy
      .find({ active: { $ne: false } })
      .select({
        _id: 1,
        name: 1,
        [`name_${lang}`]: 1,
        code: 1
      })
      .sort({ name: 1 })
      .lean();

    // Transform the response
    const activeAllergies = allergies.map(allergy => ({
      _id: allergy._id,
      name: allergy[`name_${lang}`] || allergy.name,
      code: allergy.code
    }));

    return NextResponse.json(activeAllergies);
  } catch (error) {
    console.error("[RESERVATIONS_ALLERGIES_GET]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 