import { NextRequest, NextResponse } from 'next/server';
import Reservation from '@/models/Reservation';
import ReservationNote from '@/models/ReservationNote';
import { processTemplate } from '@/lib/sms-templates';
import { ObjectId } from 'mongodb';
import ScheduledSMS from '@/models/ScheduledSMS';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';
export async function GET(request: NextRequest) {
  const logs: string[] = [];
  const stepResults: any[] = [];
  const startTime = Date.now();
  try {
    logs.push('Starting absent-status-cron API endpoint execution');
    if(process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DEV_SERVER !== 'true') {
      logs.push('Disabled in production');
      return NextResponse.json({ message: 'Disabled', logs }, { status: 200 });
    }

    const apiKey = request.headers.get('x-api-key');
    if (apiKey !== process.env.CRON_API_KEY) {
      logs.push('Unauthorized access attempt with invalid API key');
      return NextResponse.json({ error: 'Unauthorized', logs }, { status: 401 });
    }
  await dbConnect();
    logs.push('Connected to database');
    const now = new Date();
    const isLastCron = now.getHours() === 22 || process.env.NODE_ENV === 'development';
    let absentStatusCount = 0;
    let absentStatusErrors: any[] = [];
    if (isLastCron) {
      const todayStr = new Date().toLocaleString('en-CA', { timeZone: 'America/Toronto', year: 'numeric', month: '2-digit', day: '2-digit' }).slice(0, 10);


      const candidates = await Reservation.find({
        isDeleted: { $ne: true },
        status: { $nin: ['confirmed', 'sales', 'recontact', 'absent','en-suivi','non-vendu','wrongnumbe','canceled'] },
        $or: [
          { assigned_user_id: { $exists: false } },
          { assigned_user_id: null }
        ]
      }).populate('appointmentId');
      logs.push(`Found ${candidates.length} candidate reservations for absent status check`);
      for (const reservation of candidates) {
        const resLog: string[] = [];
        const appointment: any = reservation.appointmentId;
        if (!appointment || !appointment.endHour || !appointment.date) {
          resLog.push('Missing appointment, endHour, or date');
          stepResults.push({ reservationId: reservation._id, status: 'skipped', logs: resLog });
          continue;
        }
        const apptDate = appointment.date;
        const apptEndHour = parseInt(appointment.endHour.split(':')[0], 10);
        const isPast = (apptDate < todayStr) || (apptDate === todayStr && apptEndHour < now.getHours());
        stepResults.push({apptDate:apptDate,todayStr:todayStr,apptEndHour:apptEndHour,now:now.getHours(),isPast:isPast});
        if (isPast) {
          try {
            await Reservation.findByIdAndUpdate(reservation._id, { $set: { status: 'absent' } });
            resLog.push('Marked as absent');
            await ReservationNote.create({
              userId: "67eab74ed78036c9f68c7686",
              reservationId: reservation._id,
              content: `[SYSTEM] Status automatically changed to 'absent' (no show at appointment :${appointment.startHour} ${appointment.endHour})`,
              type: 'system',
              createdBy: 'system',
              createdAt: new Date(),
            });
            resLog.push('Added absent status note');
            const db = (await dbConnect()).connection;
            const absentTemplate = await db.collection('sms_templates').findOne({
              _id:new ObjectId('6807c9b4e39ce75bce0395bc')
            })
            const templateData: Record<string, string> = {
              'customerInfo.client1Name': reservation?.customerInfo.client1Name || '',
            };
            
            // Add branch data for template variables
            try {
              if (reservation.appointmentId) {
                const appointment = await mongoose.model('Appointment').findById(reservation.appointmentId);
                if (appointment && appointment.branchId) {
                  const branch = await mongoose.model('Branch').findById(appointment.branchId);
                  if (branch) {
                    // Add all branch fields with 'branch.' prefix
                    const branchObj = typeof branch.toObject === 'function' ? branch.toObject() : branch;
                    
                    for (const [key, value] of Object.entries(branchObj)) {
                      // Skip MongoDB internal fields and arrays/objects
                      if (key !== '_id' && key !== '__v' && typeof value !== 'object') {
                        templateData[`branch.${key}`] = String(value);
                      } else if (key === '_id') {
                        templateData['branch._id'] = String(value);
                      }
                    }
                    
                    resLog.push(`Added branch fields to template data: ${Object.keys(templateData).filter(k => k.startsWith('branch.')).join(', ')}`);
                  }
                }
              }
            } catch (err) {
              resLog.push(`Error adding branch data: ${err}`);
            }
            
            const tomorrow = new Date();
            tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
            tomorrow.setUTCHours(8, 50, 0, 0);
            const scheduledAt = new Date(tomorrow);
            const body = processTemplate(absentTemplate?.template, templateData);
            ScheduledSMS.create({
              templateId: absentTemplate?._id,
              body,
              reservationId: reservation._id,
              status: 'pending',
              error: undefined,
              createdAt: new Date(),
              scheduledAt: scheduledAt,
            });
            resLog.push('Scheduled SMS for absent client');
            await ReservationNote.create({
              userId: "67eab74ed78036c9f68c7686",
              reservationId: reservation._id,
              content: `[SYSTEM] SMS scheduled to be sent to absent client :${body} at ${scheduledAt}`,
              type: 'system',
              createdBy: 'system',
              createdAt: new Date(),
            });
            resLog.push('Added SMS scheduled note');
            absentStatusCount++;
            stepResults.push({ reservationId: reservation._id, status: 'absent-marked', logs: resLog });
          } catch (err) {
            absentStatusErrors.push({ id: reservation._id, error: err });
            resLog.push('Error: ' + err);
            stepResults.push({ reservationId: reservation._id, status: 'error', logs: resLog });
          }
        } else {
          resLog.push('Not past appointment time, skipping');
          stepResults.push({ reservationId: reservation._id, status: 'not-past', logs: resLog });
        }
      }
    }
    const endTime = Date.now();
    logs.push(`Absent status cron completed. Marked absent: ${absentStatusCount}, Errors: ${absentStatusErrors.length}`);
    logs.push(`Total execution time: ${endTime - startTime}ms`);
    return NextResponse.json({
      message: 'Absent status cron completed',
      absentStatusCount,
      absentStatusErrors,
      stepResults,
      logs
    });
  } catch (error) {
    logs.push('Error in absent status cron: ' + error);
    return NextResponse.json({
      error: 'Failed to process absent status cron',
      message: error instanceof Error ? error.message : 'Unknown error',
      logs
    }, { status: 500 });
  }
} 