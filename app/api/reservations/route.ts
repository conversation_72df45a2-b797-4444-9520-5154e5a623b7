import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { ObjectId } from "mongodb";
import Reservation from "@/models/Reservation";
import twilio from 'twilio';
import { getMessagesForContact, getMessagesForPhoneNumber, normalizePhoneNumber, sendSMS } from '@/lib/twilio';
import Appointment from '@/models/Appointment';
import Branch from '@/models/Branch';
import { RequestDocument } from '@/app/types/request';
import { Contact } from '@/app/models/Contact';
import { createTwilioConversation } from '@/lib/conversations/createTwilioConversation';
import { canUserViewAssignedReservations, canUserViewAllBranches, canUserViewOwnBranches, canUserViewOwnBranchReservations, canUserViewOwnReservations, canUserViewPhoneReservationStatuses, canUserViewAllReservations, canUserViewReservationStatuses, doesUserHaveReservationAccess, canUserViewOnlyOwnReservations, canUserViewOnlyAssignedReservations, canUserViewSellerReservationStatuses } from '@/lib/utils/permissions-utils';
import ReservationStatus from '@/models/ReservationStatus';
import { isBranchesAdmin, isBranchesAgent, isSeller } from '@/lib/utils/role-utils';
import { getUserFromToken } from '../affectations/utils/mobile_auth_utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { ReservationAuditLogger } from '@/lib/utils/audit-utils';
import { createQuebecDate, QUEBEC_TIMEZONE } from '@/app/admin-dashboard/utils/timezone-utils';
import ContactRequestStatus from '@/models/ContactRequestStatus';

export async function POST(req: Request) {
  try {
    const requestBody = await req.json();
  const requestHeaders = req.headers;
  let session=await getUserFromToken(requestBody,requestHeaders);
  if(!session || session==null){
    session=await getServerSession(authOptions);
  }
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get request data
    const data = requestBody;

    // --- Validation ---
    const requiredFields = [
      'appointmentId',
      'customerInfo.client1Name',
      'customerInfo.phone',
      'preferences.branchId',
      'preferences.visitDate',
      'preferences.visitTime'
    ];

    const missingFields = requiredFields.filter(field => {
      const keys = field.split('.');
      let value = data;
      for (const key of keys) {
        if (value === null || typeof value !== 'object' || !(key in value) || !value[key]) {
          return true;
        }
        value = value[key];
      }
      return false;
    });

    if (missingFields.length > 0) {
      return new NextResponse(
        `Missing required fields: ${missingFields.join(', ')}`,
        { status: 400 }
      );
    }

    const db= (await dbConnect()).connection;

    // --- Duplicate Check ---
    const phoneToCheck = normalizePhoneNumber(data.customerInfo?.phone);
    if (phoneToCheck) { // Only check if a valid phone number exists
      const existingReservation = await Reservation.findOne({isDeleted:{$ne:true},"customerInfo.phone":phoneToCheck}).lean(); // Use .lean() for faster, read-only query

      if (existingReservation) {
        return NextResponse.json(
          { error: "A reservation with this phone number already exists." },
          { status: 409 } // 409 Conflict is appropriate for duplicates
        );
      }

      // Validate phone number before creating any documents
      if (!/^\d{10}$/.test(phoneToCheck)) {
        return NextResponse.json(
          {
            error: "Validation failed",
            details: {
              "phone": {
                "name": "ValidatorError",
                "message": "Phone number must be exactly 10 digits",
                "properties": {
                  "message": "Phone number must be exactly 10 digits",
                  "type": "user defined",
                  "path": "phone",
                  "value": phoneToCheck
                },
                "kind": "user defined",
                "path": "phone",
                "value": phoneToCheck
              }
            }
          },
          { status: 400 }
        );
      }
    }
    // --- End Duplicate Check ---

    // Calculate total number of attendees (optional, keep if needed)
    const totalChildren = data.preferences?.hasChildren ?
      (data.preferences.childrenAges?.age0to5 || 0) +
      (data.preferences.childrenAges?.age6to12 || 0) +
      (data.preferences.childrenAges?.age13to17 || 0) : 0;
    // --- Contact Request Integration Logic ---
    // Set source to 'contact_request' when contactRequestSourceId is provided
    let reservationSource = data.source || 'direct';
    let contactRequestId = null;
    let contactRequestSourceId = null;

    if (data.contactRequestSourceId) {
      reservationSource = 'contact_request';
      contactRequestSourceId = new ObjectId(data.contactRequestSourceId);
    }

    if (data.contactRequestId) {
      contactRequestId = new ObjectId(data.contactRequestId);
    }

    // Prepare reservation data
    const reservation = {
      appointmentId: new ObjectId(data.appointmentId), // Required
      partnerId: data.partnerId ? new ObjectId(data.partnerId) : null,
      type: data.type || 'branch', // Optional, default 'branch'
      status: data.status || 'new', // Optional, default 'pending'
      source: reservationSource, // Set to 'contact_request' when contactRequestSourceId provided
      contactRequestId: contactRequestId, // Add contact request reference
      contactRequestSourceId: contactRequestSourceId, // Add contact request source reference
      customerInfo: {
        client1Name: data.customerInfo.client1Name, // Required
        hasCompanion: !!data.customerInfo?.client2Name, // Optional
        client2Name: data.customerInfo?.client2Name || undefined, // Optional
        city: data.customerInfo?.city || undefined, // Optional
        postalCode: data.customerInfo?.postalCode || undefined, // Optional
        phone: phoneToCheck, // Store normalized phone number
        phone2: normalizePhoneNumber(data.customerInfo?.phone2), // Store normalized phone number 2
        email: data.customerInfo?.email || undefined, // Optional
        isPostalCodeValid: data.customerInfo?.isPostalCodeValid || false // Optional
      },
      preferences: {
        preferredLanguage: data.preferences?.preferredLanguage || 'fr', // Optional, default 'fr'
        allergies: data.preferences?.allergies || '', // Optional - NEW, expect string, default to empty string
        hasChildren: data.preferences?.hasChildren || false, // Optional
        childrenAges: { // Optional
          age0to5: data.preferences?.childrenAges?.age0to5 || 0,
          age6to12: data.preferences?.childrenAges?.age6to12 || 0,
          age13to17: data.preferences?.childrenAges?.age13to17 || 0
        },
        branchId: data.preferences.branchId, // Required
        visitDate: data.preferences.visitDate, // Required
        visitTime: data.preferences.visitTime, // Required
        adultServiceTypes: data.preferences?.adultServiceTypes || {}, // Optional, object mapping adult IDs to service types
        childServiceTypes: data.preferences?.childServiceTypes || {} // Optional, object mapping child IDs to service types
      },
      // Keep other fields if they exist in the model and are needed
      // accompNumber: data.accompNumber, // Example: Check if needed
      // totalAttendees: data.accompNumber + totalChildren, // Example: Check if needed
      notes: data.notes || "", // Optional
      createdBy: new ObjectId(session.user.id), // Set createdBy using session user ID
      // updatedBy: session.user.id, // Mongoose timestamps might handle this, or set explicitly if needed
    };

    // Create a new Reservation document instance to validate
    const reservationDoc = new Reservation(reservation);

    // Validate the document against the schema before saving
    // This will throw a ValidationError if validation fails
    const validationError = reservationDoc.validateSync();
    if (validationError) {
      return NextResponse.json(
        { error: "Validation failed", details: validationError.errors },
        { status: 400 }
      );
    }

    // Validate phone for Contact model before creating reservation
    const customerPhone = data.customerInfo?.phone ? normalizePhoneNumber(data.customerInfo.phone) : null;
    if (customerPhone) {
      try {
        // Pre-validate Contact data
        const contactData = {
          conversation: {
            sid: '',
            linkedReservationId: 'temp', // Will be replaced if validation passes
            linkedBranch: data.preferences?.branchId || null,
          },
          phone: customerPhone,
          fullname: data.customerInfo?.client1Name || '',
          postalCode: data.customerInfo?.postalCode || '',
        };

        // Create a temporary Contact instance to validate (don't save it)
        const tempContact = new Contact(contactData);
        const contactValidationError = tempContact.validateSync();

        if (contactValidationError) {
          return NextResponse.json(
            { error: "Validation failed", details: contactValidationError.errors },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error("Error validating contact data:", error);
        if (error instanceof mongoose.Error.ValidationError) {
          return NextResponse.json(
            { error: "Validation failed", details: error.errors },
            { status: 400 }
          );
        }
        throw error; // Re-throw for general error handling
      }
    }

    // Create new reservation using Mongoose model
    const newReservation = await reservationDoc.save();

    // --- PendingReservation Update ---
    // Update PendingReservation after successful reservation creation (without status change)
    if (data.contactRequestId) {
      try {
        // Update the PendingReservation with the new reservation ID and archive it
        // Note: Status is no longer automatically changed to 'reserved'
        await db.collection('pendingReservations').updateOne(
          { _id: new ObjectId(data.contactRequestId) },
          {
            $set: {
              reservationId: new ObjectId(newReservation._id),
              isArchived: true,
              updatedAt: new Date()
            }
          }
        );

        console.log(`PendingReservation ${data.contactRequestId} updated with reservation ${newReservation._id} and archived`);
      } catch (pendingReservationError) {
        console.error('Error updating PendingReservation:', pendingReservationError);
        // Don't fail the reservation creation if PendingReservation update fails
        // This ensures reservation creation continues even if contact request updates fail
      }
    }

    // --- Contact & Twilio Conversation Creation ---
    if (customerPhone) {
      let contact = await Contact.findOne({ phone: customerPhone });
      if (!contact) {
        contact = await Contact.create({
          conversation: {
            sid: '',
            linkedReservationId: newReservation._id.toString(),
            linkedBranch: data.preferences?.branchId || null,
          },
          phone: customerPhone,
          fullname: data.customerInfo?.client1Name || '',
          postalCode: data.customerInfo?.postalCode || '',
        });
      } else {
        // Optionally update linkedReservationId and linkedBranch if you want to keep track of the latest
        contact.conversation.linkedReservationId = newReservation._id.toString();
        contact.conversation.linkedBranch = data.preferences?.branchId || null;
        await contact.save();
      }
      // Only create a Twilio conversation if not already present
      if (!contact.conversation.sid) {
        const twilioResult = await createTwilioConversation({
          friendlyName: contact.fullname || contact.phone,
          contactId: contact._id.toString(),
          participants: [], // Optionally add SMS/WhatsApp addresses if available,
        });
        contact.conversation.sid = twilioResult.sid;
        await contact.save();
      }
    }

    // Log audit event for reservation creation
    try {
      await ReservationAuditLogger.logReservationCreated(
        req as any, // Cast to NextRequest for audit logging
        newReservation._id.toString(),
        newReservation.customerInfo.client1Name,
        data,
        session
      );
    } catch (auditError) {
      console.error('Failed to log reservation creation audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    // Send SMS confirmation to customer
    try {
      const visitDate = new Date(data.preferences.visitDate).toLocaleDateString('fr-CA', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      // Get branch information for the SMS
      const branchForSMS = await db.collection('branches').findOne(
        { _id: ObjectId.createFromHexString(data.preferences.branchId) },
        { projection: { name: 1, address: 1, city: 1, phone: 1 } }
      );

      const fromNumber = branchForSMS?.phone;
      if (fromNumber && data.customerInfo.phone) {
        const smsMessage = `Bonjour ${data.customerInfo.client1Name},
Votre dégustation gratuite AMQ est  confirmée ! 🎉 

Voici un petit récapitulatif :
 📍 Lieu : ${branchForSMS?.address}
📅 Date : ${visitDate}
🕒 Heure : ${data.preferences.visitTime.split('-')[0]}}

🍽️ Goûtez aux produits de nos producteurs locaux
👨‍👩‍👧‍👦 Venez vivre l’expérience en famille !
🎁 Votre présence offre un déjeuner au Club des petits déjeuners
🎟️ Et vous donne accès à tous nos tirages !
✅ Gratuit, sans obligation.

Des questions ? **************
— Équipe Alimentation Mon Quartier`;

        await sendSMS(
          fromNumber,
          data.customerInfo.phone,
          smsMessage,
          true, // auto message
        );

        console.log('SMS confirmation sent successfully');

      }
    } catch (smsError) {
      console.error('Error sending SMS confirmation:', smsError);
      // Don't fail the reservation if SMS fails
    }

    return NextResponse.json({
      message: "Reservation created successfully",
      _id: newReservation._id
    });

  } catch (error) {
    console.error("Error creating reservation:", error);
    // Handle Mongoose validation errors specifically
    if (error instanceof mongoose.Error.ValidationError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    // Handle other errors
    if (error instanceof Error) {
      return NextResponse.json(
        { error: "Failed to create reservation", details: error.message },
        { status: 500 }
      );
    }
    // Fallback for unknown errors
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

// Profiling utility
const createProfiler = () => {
  const startTime = process.hrtime();
  const steps: Record<string, number> = {};
  const stepStartTimes: Record<string, [number, number]> = {};
  let lastStepName = 'start';

  return {
    step: (name: string) => {
      const now = process.hrtime();
      const elapsedSinceStart = (now[0] * 1000) + (now[1] / 1000000);

      // Calculate time spent on previous step
      const lastStepTime = stepStartTimes[lastStepName];
      if (lastStepTime) {
        const stepDuration = [
          now[0] - lastStepTime[0],
          now[1] - lastStepTime[1]
        ];
        const stepDurationMs = (stepDuration[0] * 1000) + (stepDuration[1] / 1000000);
        steps[lastStepName] = stepDurationMs;
      }

      // Mark the start time for this step
      stepStartTimes[name] = now;
      lastStepName = name;

      return elapsedSinceStart;
    },
    getResults: () => {
      return {
        steps,
        total: process.hrtime(startTime)
      };
    },
    logResults: () => {
      const isDev = process.env.NODE_ENV === 'development';
      if (!isDev) return;

      // Finalize the last step if needed
      const now = process.hrtime();
      if (stepStartTimes[lastStepName]) {
        const stepDuration = [
          now[0] - stepStartTimes[lastStepName][0],
          now[1] - stepStartTimes[lastStepName][1]
        ];
        const stepDurationMs = (stepDuration[0] * 1000) + (stepDuration[1] / 1000000);
        steps[lastStepName] = stepDurationMs;
      }

      const elapsed = process.hrtime(startTime);
      const totalMs = (elapsed[0] * 1000) + (elapsed[1] / 1000000);

      // Calculate total time of all steps
      const totalStepTime = Object.values(steps).reduce((sum, time) => sum + time, 0);

      // Sort steps by execution time (descending)
      const sortedSteps = Object.entries(steps)
        .sort((a, b) => b[1] - a[1])
        .map(([name, time]) => ({
          name,
          time: time.toFixed(2),
          ms: time,
          percentage: (time / totalMs * 100).toFixed(2)
        }));

      console.log('\n📊 RESERVATION API PROFILING RESULTS 📊');
      console.log('========================================');
      console.table(sortedSteps);
      console.log(`Total execution time: ${totalMs.toFixed(2)}ms`);
      console.log(`Sum of all steps: ${totalStepTime.toFixed(2)}ms (${(totalStepTime/totalMs*100).toFixed(2)}% accounted for)`);
      console.log('========================================\n');
    }
  };
};

export async function GET(request: Request) {
  const profiler = createProfiler();
  const profileResults: Record<string, number> = {};
  const isDev = process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEV_SERVER === 'true';

  try {
    profiler.step('start');

    await dbConnect();
    profileResults['database-connection'] = profiler.step('database-connection');

    const { searchParams } = new URL(request.url);
    profileResults['parse-url'] = profiler.step('parse-url');

    // Get the user session
    const session = await getServerSession(authOptions);
    profileResults['auth-session'] = profiler.step('auth-session');

    if(session && !session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
      profileResults['get-permissions'] = profiler.step('get-permissions');
    }

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a SuperAdmin
    const isSuperAdmin = session.user.roles?.some((role: any) =>
      (typeof role === 'object' ? role._id?.toString() === '67add3214badd3283e873329' : role?.toString() === '67add3214badd3283e873329')
    );

    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const skip = (page - 1) * limit;
    // Sorting parameters
    const sort = searchParams.get('sort');
    const order = searchParams.get('order') === 'desc' ? -1 : 1;
    const sortOptions: { [key: string]: 1 | -1 } = {};

    // Define sort options based on query parameters
    if (sort) {
      // Special handling for visitTime sorting
      if (sort === 'preferences.visitTime' || sort === 'preferences_visitTime') {
        // Use preferences.visitTime which contains the format (HH:MM-HH:MM)
        sortOptions['visitTimeForSort'] = order;
      } else {
        // For all other fields, use the original field name
        sortOptions[sort] = order;
      }
    } else {
      sortOptions['createdAt'] = -1; // Default sort
    }

    profileResults['parse-params'] = profiler.step('parse-params');

    // Filtering parameters
    const status = searchParams.get('status');
    const statusCodes = searchParams.get('statusCodes'); // Client-side passed status codes
    const type = searchParams.get('type');
    const search = searchParams.get('search');
    const includeDeleted = searchParams.get('includeDeleted') === 'true';
    const branchId = searchParams.get('branchId'); // Add branch filter
    const assignedTo = searchParams.get('assignedTo'); // Add assigned user filter
    const createdAtDate = searchParams.get('createdAtDate'); // Add created at date filter
    const createdAtStartDate = searchParams.get('createdAtStartDate'); // Add creation date range start
    const createdAtEndDate = searchParams.get('createdAtEndDate'); // Add creation date range end
    const countOnly = searchParams.get('countOnly') === 'true'; // Add count only parameter
    const partnerId = searchParams.get('partnerId');
    const visitTime = searchParams.get('visitTime'); // Add visit time filter

    // Get date range parameters
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const allDates = searchParams.get('allDates') === 'true';

    // Check if we should skip expensive operations for faster initial load
    const skipCount = searchParams.get('skipCount') === 'true';

    const db= (await dbConnect()).connection;


    if(!doesUserHaveReservationAccess(session.user)){
      return NextResponse.json([]);
    }

    //---Branch Filter---
    const branchCollection = db.collection('branches');
    let accessibleBranchIds: string[] = [];
    if (canUserViewAllReservations(session.user) || canUserViewOnlyOwnReservations(session.user) || isSuperAdmin) {
      //branch does not matter
      accessibleBranchIds = (await branchCollection.find({}).project({ _id: 1 }).toArray()).map(branch => branch._id.toString());
    } else if (canUserViewOwnBranchReservations(session.user) || canUserViewAssignedReservations(session.user)) {
      const userBranches = await branchCollection.find({
        $or: [
          { responsible: new ObjectId(session.user.id) },
          { agents: new ObjectId(session.user.id) },
          { sellers: new ObjectId(session.user.id) }
        ]
      }).toArray();
      accessibleBranchIds = userBranches.map(branch => branch._id.toString());
    }
    profileResults['branch-filter'] = profiler.step('branch-filter');

    const pipeline: any[] = [];
     // Add search filter
     if (search) {
      const searchRegex = new RegExp(search, 'i');
      pipeline.push({
        $match: {
          $or: [
            { 'customerInfo.client1Name': searchRegex },
            { 'customerInfo.client2Name': searchRegex },
            { 'customerInfo.phone': searchRegex },
            { 'customerInfo.email': searchRegex },
          ]
        }
      });
    }

    pipeline.push(
      // Add a field to extract the date part from visitDate for filtering and sorting
      {
        $addFields: {
          visitDateOnly: {
            $substr: [
              "$preferences.visitDate",
              0,
              10  // Just get YYYY-MM-DD part
            ]
          }
        }
      },
      // Match by date if date range is provided and allDates is false
      ...((!allDates && (startDate || endDate)) ? [{
        $match: {
          $and: [
            ...(startDate ? [{ 'visitDateOnly': { $gte: startDate } }] : []),
            ...(endDate ? [{ 'visitDateOnly': { $lte: endDate } }] : [])
          ]
        }
      }] : []),
      // Match by delete status
      {
        $match: includeDeleted
          ? { isDeleted: true }
          : { isDeleted: { $ne: true }}
      },
      // Lookup reservation statuses
      {
        $lookup: {
          from: 'reservationstatuses',
          localField: 'status',
          foreignField: '_id',
          as: 'statusObject'
        }
      },
      // Lookup partner information for invitation sources
      {
        $lookup: {
          from: 'users',
          localField: 'partnerId',
          foreignField: '_id',
          as: 'partnerObject'
        }
      },
      // Lookup contact request information for contact_request sources
      {
        $lookup: {
          from: 'pendingreservations',
          localField: 'contactRequestId',
          foreignField: '_id',
          as: 'contactRequestObject'
        }
      },
      // Lookup contact request source information for contact_request sources
      {
        $lookup: {
          from: 'contactrequestsources',
          localField: 'contactRequestSourceId',
          foreignField: '_id',
          as: 'contactRequestSourceObject'
        }
      },
      // Add a field to track the status code for easier filtering
      {
        $addFields: {
          statusCode: {
            $cond: {
              if: { $gt: [{ $size: '$statusObject' }, 0] },
              then: { $arrayElemAt: ['$statusObject.code', 0] },
              else: '$status' // Fallback to status ID if status object not found
            }
          },
          // Add populated partner data for invitation sources
          partnerData: {
            $cond: {
              if: { $eq: ['$source', 'invitation'] },
              then: { $arrayElemAt: ['$partnerObject', 0] },
              else: null
            }
          },
          // Add populated contact request data for contact_request sources
          contactRequestData: {
            $cond: {
              if: { $eq: ['$source', 'contact_request'] },
              then: { $arrayElemAt: ['$contactRequestObject', 0] },
              else: null
            }
          },
          // Add populated contact request source data for contact_request sources
          contactRequestSourceData: {
            $cond: {
              if: { $eq: ['$source', 'contact_request'] },
              then: { $arrayElemAt: ['$contactRequestSourceObject', 0] },
              else: null
            }
          }
        }
      }
    );
    profileResults['build-initial-pipeline'] = profiler.step('build-initial-pipeline');

    if (accessibleBranchIds.length > 0) {
      // If specific branches were requested
      if (branchId && branchId !== 'all') {
        // Handle comma-separated branch IDs
        const requestedBranchIds = branchId.split(',');

        // Filter to only include branches the user has access to
        const filteredBranchIds = requestedBranchIds.filter(id =>
          accessibleBranchIds.includes(id)
        );

        // If user has access to at least one requested branch
        if (filteredBranchIds.length > 0) {
          // Convert to ObjectId for MongoDB query
          const objectIds = filteredBranchIds.map(id => new ObjectId(id));

          pipeline.push({
            $match: {
              $or: [
                // Match string IDs (for compatibility)
                { 'preferences.branchId': { $in: filteredBranchIds } },
                // Match ObjectId IDs
                { 'preferences.branchId': { $in: objectIds } }
              ]
            }
          });
        } else {
          // User doesn't have access to any requested branch
          // Return empty result by using a non-existent branch ID
          pipeline.push({
            $match: { 'preferences.branchId': 'non-existent-id' }
          });
        }
      } else {
        // No specific branch requested, show all branches user has access to
        pipeline.push({
          $match: {
            $or: [
              { 'preferences.branchId': { $in: accessibleBranchIds } },
              { 'preferences.branchId': { $in: accessibleBranchIds.map(id => new ObjectId(id)) } }
            ]
          }
        });
      }
    }
    profileResults['add-branch-filter-to-pipeline'] = profiler.step('add-branch-filter-to-pipeline');
    //---End of Branch Filter---

    // Add createdAt date filter for "today's reservations" feature
    if (createdAtDate) {
      // Use Quebec timezone for proper "today" filtering
      // This ensures reservations created "today" in Quebec time are correctly filtered
      const startOfTodayQuebec = createQuebecDate(createdAtDate, false);
      const endOfTodayQuebec = createQuebecDate(createdAtDate, true);

      pipeline.push({
        $match: {
          createdAt: {
            $gte: startOfTodayQuebec,
            $lte: endOfTodayQuebec
          }
        }
      });

      pipeline.push({ $sort: sortOptions });

      const countPipeline = [...pipeline, { $count: 'total' }];
      profileResults['prepare-count-pipeline-created-date'] = profiler.step('prepare-count-pipeline-created-date');

      const [countResult] = await Reservation.aggregate(countPipeline);
      profileResults['aggregate-count-created-date'] = profiler.step('aggregate-count-created-date');

      const totalCount = countResult?.total || 0;

      if (countOnly) {
        if (isDev) profiler.logResults();
        return Response.json({ totalCount });
      }

      pipeline.push(
        { $skip: skip },
        { $limit: limit }
      );

      let reservations = await Reservation.aggregate(pipeline);
      profileResults['aggregate-reservations-created-date'] = profiler.step('aggregate-reservations-created-date');

      const response = NextResponse.json({
        reservations,
        totalCount,
        page,
        limit,
        ...(isDev ? { profileResults } : {})
      });

      profileResults['total'] = profiler.step('total');
      if (isDev) profiler.logResults();
      return response;
    }
    //--------------------------------

    // Add creation date range filter for date range filtering
    if (createdAtStartDate || createdAtEndDate) {
      const createdAtFilter: any = {};

      if (createdAtStartDate) {
        // Extract date part and apply Quebec timezone logic
        let dateString: string;
        if (createdAtStartDate.includes('T')) {
          // ISO timestamp string - extract date part (YYYY-MM-DD)
          dateString = createdAtStartDate.split('T')[0];
        } else {
          // Already YYYY-MM-DD string
          dateString = createdAtStartDate;
        }
        // Parse in Quebec timezone (beginning of day)
        createdAtFilter.$gte = createQuebecDate(dateString, false);
      }

      if (createdAtEndDate) {
        // Extract date part and apply Quebec timezone logic
        let dateString: string;
        if (createdAtEndDate.includes('T')) {
          // ISO timestamp string - extract date part (YYYY-MM-DD)
          dateString = createdAtEndDate.split('T')[0];
        } else {
          // Already YYYY-MM-DD string
          dateString = createdAtEndDate;
        }
        // Parse in Quebec timezone (end of day)
        createdAtFilter.$lte = createQuebecDate(dateString, true);
      }

      pipeline.push({
        $match: {
          createdAt: createdAtFilter
        }
      });
    }



    //---Status Filter---
    let allowedStatuses: Set<string> = new Set();

    if(canUserViewPhoneReservationStatuses(session.user)){
      ['new','pasreponse','present','wrongnumbe','canceled','sales','3times'].forEach(status => {
        allowedStatuses.add(status);
      });
    }
    if(canUserViewSellerReservationStatuses(session.user)){
      ['confirmed', 'sales', 'en-suivi', 'non-vendu'].forEach(status => {
        allowedStatuses.add(status);
      });
    }

    //MOVE STATUS AVAILABLITY TO A TABLE???
    if(canUserViewReservationStatuses(session.user) ||canUserViewAllReservations(session.user) || isSuperAdmin){
      const statuses = await ReservationStatus.find({}).lean();
      statuses.forEach(status => {
        allowedStatuses.add(status.code);
      });
    }
    profileResults['build-allowed-statuses'] = profiler.step('build-allowed-statuses');

     // Add status filter
    if (status && status !== 'all') {
      const statusValues = status.split(',');
      const intersection = Array.from(allowedStatuses).filter(status => statusValues.includes(status));
      pipeline.push({
        $match: {
          $or: [
            { statusCode: { $in: intersection } },
            { status: { $in: intersection } } // Fallback for direct status codes
          ]
        }
      });
    }else{
      pipeline.push({
        $match: {
          $or: [
            { statusCode: { $in: Array.from(allowedStatuses) } },
            { status: { $in: Array.from(allowedStatuses) } } // Fallback for direct status codes
          ]
        }
      });
    }
    if (statusCodes) {
      const codesList = statusCodes.split(',');
      const intersection = Array.from(allowedStatuses).filter(status => codesList.includes(status));
      pipeline.push({
        $match: {
          $or: [
            { statusCode: { $in: intersection } },
            { status: { $in: intersection } } // Fallback for direct status codes
          ]
        }
      });
    }
    else {
      pipeline.push({
        $match: {
          $or: [
            { statusCode: { $in: Array.from(allowedStatuses) } },
            { status: { $in: Array.from(allowedStatuses) } } // Fallback for direct status codes
          ]
        }
      });
    }
    profileResults['add-status-filter-to-pipeline'] = profiler.step('add-status-filter-to-pipeline');
    //---End of Status Filter---

    // Add type filter
    if (type && type !== 'all') {
      const typeValues = type.split(',');
      pipeline.push({
        $match: {
          type: typeValues.length > 1 ? { $in: typeValues } : typeValues[0]
        }
      });
    }

    // Add assignedTo filter - only show reservations assigned to the specific user
    if (assignedTo) {
      const assignedToIds = assignedTo.split(',').map(id => new ObjectId(id));
      pipeline.push({
        $match: {
          assigned_user_id: assignedToIds.length > 1 ? { $in: assignedToIds } : assignedToIds[0]
        }
      });
    }

    // Add visitTime filter
    if (visitTime) {
      // Handle comma-separated list of visit times
      const visitTimes = visitTime.split(',');

      // Create an array of patterns to match against (if filters are ["11", "14"], create patterns ["11:00", "14:00"])
      const timePatterns = visitTimes.map(time => `${time}:00`);

      pipeline.push({
        $match: {
          $expr: {
            $let: {
              vars: {
                startTime: { $arrayElemAt: [{ $split: ["$preferences.visitTime", "-"] }, 0] }
              },
              in: { $in: ["$$startTime", timePatterns] }
            }
          }
        }
      });
    }

    //---Partner/assigned_user Filter---
    if(canUserViewOnlyOwnReservations(session.user)){
      pipeline.push({
        $match: {
          partnerId: new ObjectId(session.user.id)
        }
      });
    }

    if(canUserViewOnlyAssignedReservations(session.user) ){
      pipeline.push({
        $match: {
          assigned_user_id: new ObjectId(session.user.id)
        }
      });
    }
    if (partnerId) {
      // Handle comma-separated list of partner IDs
      const partnerIds = partnerId.split(',').map(id => new ObjectId(id));
      pipeline.push({
        $match: {
          partnerId: partnerIds.length > 1 ? { $in: partnerIds } : partnerIds[0]
        }
      });
    }
    profileResults['add-user-filters-to-pipeline'] = profiler.step('add-user-filters-to-pipeline');

    // Add phones filter to look up reservations by multiple phone numbers
    const phones = searchParams.get('phones');
    if (phones) {
      const phonesList = phones.split(',');
      // Normalize each phone number for comparison
      const normalizedPhones = phonesList.map(p => normalizePhoneNumber(p));

      pipeline.push({
        $match: {
          $expr: {
            $in: [
              { $function: {
                  body: function(phone: string) {
                    // Remove all non-digit characters
                    const digits = phone.replace(/\D/g, '');
                    // If it starts with 1, remove it
                    return digits.startsWith('1') ? digits.slice(1) : digits;
                  },
                  args: ["$customerInfo.phone"],
                  lang: "js"
              }},
              normalizedPhones
            ]
          }
        }
      });

      // When searching by phone numbers, prioritize the latest reservation
      pipeline.push({ $sort: { createdAt: -1 } });
    }

    // Add support for fetching reservations by ID list
    const ids = searchParams.get('ids');
    if (ids) {
      const idsList = ids.split(',').map(id => new ObjectId(id));
      pipeline.push({
        $match: {
          _id: { $in: idsList }
        }
      });
    }

    // Add visitTimeForSort field for sorting if needed
    if (sort === 'preferences.visitTime' || sort === 'preferences_visitTime') {
      pipeline.push({
        $addFields: {
          visitTimeForSort: {
            $substr: ["$preferences.visitTime", 0, 5] // Extract the first HH:MM part
          }
        }
      });
    }
    profileResults['add-special-filters-to-pipeline'] = profiler.step('add-special-filters-to-pipeline');

    // Add sorting
    pipeline.push({ $sort: sortOptions });

    let totalCount = 0;

    if (!skipCount) {
      // Get total count using the pipeline without skip/limit
      const countPipeline = [...pipeline, { $count: 'total' }];
      profileResults['prepare-count-pipeline'] = profiler.step('prepare-count-pipeline');

      const [countResult] = await Reservation.aggregate(countPipeline);
      profileResults['aggregate-count'] = profiler.step('aggregate-count');

      totalCount = countResult?.total || 0;

      // If countOnly is true, return just the count
      if (countOnly) {
        const response = Response.json({
          totalCount,
          ...(isDev ? { profileResults } : {})
        });
        profileResults['total'] = profiler.step('total');
        if (isDev) profiler.logResults();
        return response;
      }
    } else {
      // For fast initial load, use a simple estimate without any database operations
      totalCount = -1; // Signal that count was skipped
      profileResults['skip-count-optimization'] = profiler.step('skip-count-optimization');
    }

    // Add pagination to the main pipeline
    pipeline.push(
      { $skip: skip },
      { $limit: limit }
    );

    // Execute the main pipeline
    let reservations = await Reservation.aggregate(pipeline);
    profileResults['aggregate-reservations'] = profiler.step('aggregate-reservations');

    // Fetch requests for all reservations in this page
    const requestsCollection = db.collection<RequestDocument>('requests');
    const reservationIds = reservations.map((r: any) => r._id);
    let requestsByReservation: Record<string, RequestDocument[]> = {};

    if (reservationIds.length > 0) {
      const requests = await requestsCollection
        .find({ reservationId: { $in: reservationIds } })
        .toArray();
      // Group requests by reservationId
      requestsByReservation = requests.reduce((acc, req) => {
        const key = req.reservationId?.toString();
        if (key) {
          if (!acc[key]) acc[key] = [];
          acc[key].push(req);
        }
        return acc;
      }, {} as Record<string, RequestDocument[]>);
    }
    profileResults['fetch-requests'] = profiler.step('fetch-requests');

    // Add requests and map data for frontend compatibility (fast operations only)
    reservations = reservations.map((reservation: any) => {
      return {
        ...reservation,
        // Set messagesCount to undefined - will be loaded by secondary API
        messagesCount: undefined,
        requests: requestsByReservation[reservation._id.toString()] || [],
        // Map partnerData to partner for frontend compatibility
        partner: reservation.partnerData || null,
        // Map contactRequestData to contactRequest for frontend compatibility
        contactRequest: reservation.contactRequestData || null,
        // Map contactRequestSourceData to contactRequestSource for frontend compatibility
        contactRequestSource: reservation.contactRequestSourceData || null
      };
    });
    profileResults['map-reservations'] = profiler.step('map-reservations');

    const response = NextResponse.json({
      reservations,
      totalCount,
      page,
      limit,
      ...(isDev ? { profileResults } : {})
    });

    profileResults['total'] = profiler.step('total');
    if (isDev) profiler.logResults();
    return response;

  } catch (error) {
    profileResults['error'] = profiler.step('error');
    console.error('Error fetching reservations:', error);
    // Ensure error is an instance of Error before accessing message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    const response = NextResponse.json(
      {
        error: 'Failed to fetch reservations',
        details: errorMessage,
        ...(isDev ? { profileResults } : {})
      },
      { status: 500 }
    );

    profileResults['total'] = profiler.step('total');
    if (isDev) profiler.logResults();
    return response;
  }
}
