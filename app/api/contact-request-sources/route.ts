import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import ContactRequestSource from '@/models/ContactRequestSource';
import { getUserPermissions } from '../utils/server-permission-utils';
import { CONTACT_REQUEST_PERMISSIONS, PermissionCode } from '@/types/permission-codes';
import { hasPermission } from '@/lib/permissions';
import BitlyService from '@/lib/services/bitly';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const userPermissions = await getUserPermissions(session);
    if (!hasPermission(userPermissions as PermissionCode[], CONTACT_REQUEST_PERMISSIONS.MANAGE_CONTACT_REQUESTS_SOURCES)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

  await dbConnect();

    // Get query parameters for filtering and pagination
    const url = new URL(req.url);
    const search = url.searchParams.get('search');
    const source = url.searchParams.get('source');
    const influencer = url.searchParams.get('influencer');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '25', 10);

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Build query based on filters
    const query: any = {};
    
    // Add search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { tag: searchRegex },
        { source: searchRegex },
        { influencer: searchRegex }
      ];
    }

    // Add specific filters
    if (source) {
      query.source = source;
    }

    if (influencer) {
      query.influencer = influencer;
    }
    
    // Get total count for pagination
    const total = await ContactRequestSource.countDocuments(query);

    // Fetch sources with pagination
    const sources = await ContactRequestSource.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: sources,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching contact request sources:', error);
    return NextResponse.json({ error: 'Failed to fetch contact request sources.' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const userPermissions = await getUserPermissions(session);
    if (!hasPermission(userPermissions as PermissionCode[], CONTACT_REQUEST_PERMISSIONS.MANAGE_CONTACT_REQUESTS_SOURCES)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

  await dbConnect();
    const data = await req.json();
    
    // Validate required fields
    if (!data.tag || !data.source || !data.influencer) {
      return NextResponse.json(
        { error: 'Tag, source, and influencer are required' },
        { status: 400 }
      );
    }

    // Validate tag format
    if (!/^[A-Za-z0-9]{6}$/.test(data.tag)) {
      return NextResponse.json(
        { error: 'Tag must be exactly 6 alphanumeric characters' },
        { status: 400 }
      );
    }

    // Check if tag already exists
    const existingSource = await ContactRequestSource.findOne({ tag: data.tag });
    if (existingSource) {
      return NextResponse.json(
        { error: 'Tag already exists' },
        { status: 409 }
      );
    }

    // Validate enum values
    const validSources = ['facebook', 'instagram', 'tiktok', 'youtube'];
    const validInfluencers = ['AMQ', 'Dominique Paquet'];

    if (!validSources.includes(data.source)) {
      return NextResponse.json(
        { error: 'Invalid source type' },
        { status: 400 }
      );
    }

    if (!validInfluencers.includes(data.influencer)) {
      return NextResponse.json(
        { error: 'Invalid influencer' },
        { status: 400 }
      );
    }

    // Create Bitly URL
    const baseUrl = process.env.BASE_URL || 'https://www.alimentationmonquartier.com';
    const longUrl = `${baseUrl}/invitation?source=${data.tag}`;
    
    let bitlyUrl = '';
    try {
      const bitlyService = new BitlyService();
      const bitlyResponse = await bitlyService.createBitlink(longUrl, {
        title: `AMQ Contact Request - ${data.source} - ${data.influencer} - ${data.tag}`
      });
      bitlyUrl = bitlyResponse.link;
    } catch (bitlyError) {
      console.error('Bitly API error:', bitlyError);
      return NextResponse.json(
        { error: 'Failed to create shortened URL' },
        { status: 502 }
      );
    }

    // Create the contact request source
    const newSource = await ContactRequestSource.create({
      tag: data.tag,
      source: data.source,
      influencer: data.influencer,
      bitlyUrl: bitlyUrl,
      hits: 0,
      webhookData: []
    });

    return NextResponse.json(newSource, { status: 201 });
  } catch (error) {
    console.error('Error creating contact request source:', error);
    return NextResponse.json(
      { error: 'Failed to create contact request source' },
      { status: 500 }
    );
  }
}