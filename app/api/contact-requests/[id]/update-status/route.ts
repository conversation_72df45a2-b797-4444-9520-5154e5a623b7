import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';
import PendingReservation from '@/models/PendingReservation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const db= (await dbConnect()).connection;
    const { statusId } = await req.json();
    
    if (!statusId) {
      return NextResponse.json(
        { error: 'StatusId is required' },
        { status: 400 }
      );
    }
    
    // Update the pending reservation
    await db.collection('pendingReservations').updateOne(
      { _id: new mongoose.Types.ObjectId(params.id) },
      { 
        $set: {
          statusId: new mongoose.Types.ObjectId(statusId),
          statusUpdatedAt: new Date()
        }
      }
    );
    
    // Get the updated document
    const updatedRequest = await db.collection('pendingReservations').findOne(
      { _id: new mongoose.Types.ObjectId(params.id) }
    );
    
    if (!updatedRequest) {
      return NextResponse.json(
        { error: 'Contact request not found' },
        { status: 404 }
      );
    }
    
    // Get the status object
    const statusObj = await db.collection('contactrequeststatuses').findOne(
      { _id: new mongoose.Types.ObjectId(statusId) }
    );
    
    // Format the response to match the expected structure
    const formattedRequest = {
      ...updatedRequest,
      status: statusObj 
        ? {
            _id: statusObj._id,
            name: statusObj.name,
            name_en: statusObj.name_en,
            code: statusObj.code,
            color: statusObj.color,
            order: statusObj.order || 0
          }
        : undefined
    };
    
    return NextResponse.json(formattedRequest);
  } catch (error) {
    console.error('Error updating contact request status:', error);
    return NextResponse.json(
      { error: 'Failed to update contact request status' },
      { status: 500 }
    );
  }
} 