import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { getSalesStatusCodes, getPresenceStatusCodes } from '@/lib/utils/reservation-status-utils';
import { ContactRequestStatsResponse } from '@/app/types/pending-reservation';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const db= (await dbConnect()).connection;

    // Get total contact requests count
    const totalContactRequests = await db.collection('pendingReservations').countDocuments();

    // Get contact requests that have been converted to reservations
    const convertedRequests = await db.collection('pendingReservations').find({
      reservationId: { $exists: true, $ne: null }
    }).project({ reservationId: 1 }).toArray();

    const convertedToReservations = convertedRequests.length;
    
    // Calculate conversion rate
    const conversionRate = totalContactRequests > 0 
      ? Math.round((convertedToReservations / totalContactRequests) * 100) 
      : 0;

    // If no conversions, return early with zero rates
    if (convertedToReservations === 0) {
      const response: ContactRequestStatsResponse = {
        totalContactRequests,
        convertedToReservations: 0,
        conversionRate: 0,
        reservationsWithPresence: 0,
        presenceRate: 0,
        reservationsWithSales: 0,
        salesRate: 0
      };
      return NextResponse.json(response);
    }

    // Get reservation IDs for converted requests
    const reservationIds = convertedRequests.map(req => req.reservationId);

    // Get status codes for presence and sales
    const [presenceStatusCodes, salesStatusCodes] = await Promise.all([
      getPresenceStatusCodes(),
      getSalesStatusCodes()
    ]);

    // Count reservations with presence status
    const reservationsWithPresence = await db.collection('reservations').countDocuments({
      _id: { $in: reservationIds },
      status: { $in: presenceStatusCodes }
    });

    // Count reservations with sales status
    const reservationsWithSales = await db.collection('reservations').countDocuments({
      _id: { $in: reservationIds },
      status: { $in: salesStatusCodes }
    });

    // Calculate presence and sales rates based on converted reservations
    const presenceRate = convertedToReservations > 0 
      ? Math.round((reservationsWithPresence / convertedToReservations) * 100) 
      : 0;

    const salesRate = convertedToReservations > 0 
      ? Math.round((reservationsWithSales / convertedToReservations) * 100) 
      : 0;

    const response: ContactRequestStatsResponse = {
      totalContactRequests,
      convertedToReservations,
      conversionRate,
      reservationsWithPresence,
      presenceRate,
      reservationsWithSales,
      salesRate
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching contact request stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contact request statistics' },
      { status: 500 }
    );
  }
}
