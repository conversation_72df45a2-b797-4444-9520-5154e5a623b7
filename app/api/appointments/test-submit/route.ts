import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { ObjectId } from 'mongodb';

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json().catch(err => {
      console.error("Failed to parse JSON body:", err);
      throw new Error("Invalid JSON in request body");
    });
    
    const { branchId, date } = body;
    
    console.log("Test submit request params:", { branchId, date });

    if (!branchId || !date) {
      return NextResponse.json(
        { error: 'branchId and date are required' },
        { status: 400 }
      );
    }

    // Connect to database
    const db= (await dbConnect()).connection;
    const collectionName = 'appointments';
    
    // Create a test slot
    const testSlot = {
      branchId: new ObjectId(branchId),
      date: new Date(date),
      startHour: "09:00",
      endHour: "10:00",
      enabled: true,
      capacity: 5,
      online: 2,
      home: 1,
      max_capacity_family: 2
    };
    
    // Insert the test slot
    const result = await db.collection(collectionName).insertOne(testSlot);
    
    console.log(`Test slot created with ID: ${result.insertedId}`);
    
    return NextResponse.json({
      message: 'Test slot created successfully',
      slotId: result.insertedId.toString()
    });
  } catch (error: any) {
    console.error('Error in test submit:', error);
    return NextResponse.json(
      { 
        error: error.message || 'Failed to create test slot',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 