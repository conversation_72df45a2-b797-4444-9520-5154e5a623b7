import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import dbConnect from '@/lib/db';
import { ObjectId } from "mongodb";
import { getExcludedStatusCodes } from "@/lib/utils/reservation-status-utils";

// Add an interface for the slot type
interface AppointmentSlot {
  _id: ObjectId;
  branchId: ObjectId;
  date: string; // Date is stored as string in YYYY-MM-DD format
  startHour: string;
  endHour: string;
  enabled: boolean;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
  [key: string]: any; // For any additional fields
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const date = searchParams.get('date');

    if (!branchId || !date) {
      return NextResponse.json(
        { error: "Branch ID and date are required" },
        { status: 400 }
      );
    }

    const db= (await dbConnect()).connection;

    console.log(`Searching for slots with branchId=${branchId} and date=${date}`);

    // Find appointment slots for the specific branch and date
    // We're using the appointments collection for all our appointment slot operations
    // IMPORTANT: We store dates as strings now, so we search directly by string
    const appointments = await db.collection("appointments")
      .find({
        branchId: new ObjectId(branchId),
        date: date  // Search by exact string match
      })
      .sort({ startHour: 1 })
      .toArray();

    // Get excluded status codes dynamically
    const excludedStatusCodes = await getExcludedStatusCodes();

    // Get all appointment IDs
    const appointmentIds = appointments.map((app: any) => app._id);

    // Get excluded reservations count for capacity adjustment
    const excludedReservations = await db.collection("reservations")
      .aggregate([
        {
          $match: {
            appointmentId: { $in: appointmentIds },
            status: { $in: excludedStatusCodes },
            isDeleted: { $ne: true }
          }
        },
        { $group: { _id: '$appointmentId', count: { $sum: 1 } } }
      ])
      .toArray();

    const excludedMap = Object.fromEntries(excludedReservations.map((r: any) => [r._id.toString(), r.count]));

    // Make sure the _id field is converted to a string for JSON serialization
    // and adjust capacity by subtracting excluded reservations (same as calendar-appointments route)
    const formattedAppointments = appointments.map((slot: any) => {
      const excludedCount = excludedMap[slot._id.toString()] || 0;
      const adjustedCapacity = Math.max(0, (slot.capacity || 0) - excludedCount);

      return {
        ...slot,
        _id: slot._id.toString(),
        capacity: adjustedCapacity,
        // Store original capacity for reference if needed
        originalCapacity: slot.capacity
      };
    });

    console.log(`Fetched ${formattedAppointments.length} slots for branch ${branchId} on ${date}`);

    return NextResponse.json({ appointments: formattedAppointments });
  } catch (error) {
    console.error('Error in /api/appointments/by-date:', error);
    const errorMessage = error instanceof Error ? error.message : "Internal Server Error";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}