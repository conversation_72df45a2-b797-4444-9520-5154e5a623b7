import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { ObjectId } from 'mongodb';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

interface AppointmentSlot {
  branchId: string | ObjectId;
  date: string | Date;
  startHour: string;
  endHour: string;
  enabled: boolean;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
}

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json().catch(err => {
      console.error("Failed to parse JSON body:", err);
      throw new Error("Invalid JSON in request body");
    });
    
    const { branchId, date, slots } = body;
    
    console.log("Request params for submit:", { branchId, date, slotsCount: slots?.length });

    // Validate required fields
    if (!branchId || !date || !slots || !Array.isArray(slots)) {
      return NextResponse.json(
        { error: 'branchId, date, and slots array are required' },
        { status: 400 }
      );
    }

    // Connect to database
    const db= (await dbConnect()).connection;
    const collectionName = 'appointments';
    
    // Prepare slots for insertion with proper ObjectId and Date conversion
    const preparedSlots = slots.map((slot: AppointmentSlot) => ({
      ...slot,
      branchId: new ObjectId(branchId),
      date: new Date(date),
      enabled: slot.enabled ?? true
    }));
    
    // Insert all slots at once
    const result = await db.collection(collectionName).insertMany(preparedSlots);
    
    console.log(`Successfully created ${result.insertedCount} appointment slots`);
    
    return NextResponse.json({
      message: 'Appointment slots submitted successfully',
      created: result.insertedCount,
      slots: preparedSlots.map((slot, index) => ({
        ...slot,
        _id: result.insertedIds[index].toString()
      }))
    });
  } catch (error: any) {
    console.error('Error submitting appointment slots:', error);
    return NextResponse.json(
      { 
        error: error.message || 'Failed to submit appointment slots',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 