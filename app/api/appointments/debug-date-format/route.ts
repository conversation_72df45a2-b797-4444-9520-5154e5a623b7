import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { ObjectId } from 'mongodb';

// Define TypeScript interfaces
interface AppointmentSlot {
  _id: ObjectId;
  branchId: ObjectId;
  date: string | Date;
  startHour: number;
  endHour: number;
  enabled?: boolean;
  capacity?: number;
  online?: boolean;
  home?: boolean;
  max_capacity_family?: number;
  [key: string]: any; // For any additional fields
}

export async function GET(request: Request) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const date = searchParams.get('date');

    if (!branchId || !date) {
      return NextResponse.json(
        { error: 'branchId and date are required' },
        { status: 400 }
      );
    }

    // Connect to database

    const db= (await dbConnect()).connection;
    
    // IMPORTANT: We're focusing primarily on the 'appointments' collection
    // (also checking appointments for completeness)
    const collections = ['appointments', 'appointments'];
    const results: Record<string, any> = {};
    
    // Get collections info
    const collectionsInfo = await db.listCollections().toArray();
    const collectionNames = collectionsInfo.map((c: { name: string }) => c.name);
    console.log('Available collections:', collectionNames);
    
    for (const collection of collections) {
      console.log(`Checking collection: ${collection}`);
      
      // Check if collection exists
      if (!collectionNames.includes(collection)) {
        results[collection] = {
          error: `Collection '${collection}' does not exist in database`,
          exists: false
        };
        continue;
      }
      
      // Get a sample of appointments from this collection
      const items = await db.collection(collection)
        .find({
          branchId: new ObjectId(branchId)
        })
        .limit(10)
        .toArray();
      
      // Get collection stats
      const collectionStats = {
        documentCount: await db.collection(collection).countDocuments(),
        documentsWithBranchId: await db.collection(collection).countDocuments({ 
          branchId: new ObjectId(branchId) 
        }),
        datesAsStrings: await db.collection(collection).countDocuments({ 
          branchId: new ObjectId(branchId),
          date: { $type: "string" }
        }),
        datesAsDate: await db.collection(collection).countDocuments({ 
          branchId: new ObjectId(branchId),
          date: { $type: "date" }
        })
      };
      
      // Analyze how dates are stored
      const dateAnalysis = items.map((item: AppointmentSlot) => ({
        _id: item._id.toString(),
        date: item.date,
        dateType: typeof item.date,
        isDateObject: item.date instanceof Date,
        isString: typeof item.date === 'string',
        isoStringTest: typeof item.date === 'string' ? new Date(item.date).toISOString() : null,
        startHour: item.startHour,
        endHour: item.endHour,
        rawDate: item.date
      }));

      // Find slots matching the specific date in different formats
      const dateMatches = {
        exactString: await db.collection(collection)
          .find({ branchId: new ObjectId(branchId), date: date })
          .limit(5)
          .toArray(),
        dateObject: await db.collection(collection)
          .find({ branchId: new ObjectId(branchId), date: new Date(date) })
          .limit(5)
          .toArray(),
        orQuery: await db.collection(collection)
          .find({ 
            branchId: new ObjectId(branchId), 
            $or: [{ date: date }, { date: new Date(date) }] 
          })
          .limit(5)
          .toArray()
      };

      results[collection] = {
        exists: true,
        stats: collectionStats,
        sampleData: dateAnalysis,
        totalSamples: items.length,
        dateMatches: {
          exactStringCount: dateMatches.exactString.length,
          dateObjectCount: dateMatches.dateObject.length,
          orQueryCount: dateMatches.orQuery.length,
          samples: {
            exactString: dateMatches.exactString.map((a: AppointmentSlot) => ({ 
              _id: a._id.toString(),
              date: a.date, 
              dateType: typeof a.date 
            })),
            dateObject: dateMatches.dateObject.map((a: AppointmentSlot) => ({ 
              _id: a._id.toString(),
              date: a.date, 
              dateType: typeof a.date 
            })),
            orQuery: dateMatches.orQuery.map((a: AppointmentSlot) => ({ 
              _id: a._id.toString(),
              date: a.date, 
              dateType: typeof a.date 
            }))
          }
        }
      };
    }

    return NextResponse.json({
      message: 'Date format debug information for appointments collections',
      branchId,
      dateQueried: date,
      note: "IMPORTANT: We use the 'appointments' collection for all operations",
      results
    });
  } catch (error: any) {
    console.error('Error in debug date format:', error);
    return NextResponse.json(
      { 
        error: error.message || 'Failed to debug date format',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 