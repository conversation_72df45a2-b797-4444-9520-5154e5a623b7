import { NextRequest, NextResponse } from 'next/server';
import twilio from 'twilio';
import { createTwilioConversation } from '@/lib/conversations/createTwilioConversation';
import { Contact } from '@/app/models/Contact';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { normalizePhoneNumber } from '@/lib/twilio';
import { getUserPermissions } from '../utils/server-permission-utils';
import { canUserViewMessages } from '@/lib/utils/permissions-utils';
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const serviceSid = process.env.TWILIO_CONVERSATIONS_SERVICE_SID;
import dbConnect from '@/lib/db';



export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
  await dbConnect();

    if(!session){
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if(!session.user.permissions){
      session.user.permissions=await getUserPermissions(session);
    }
    if(!canUserViewMessages(session.user)){
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Parse pagination and search params
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const skip = (page - 1) * limit;
    const q = searchParams.get('q')?.trim();
    const branch = searchParams.get('branch');
    const showArchived = searchParams.get('showArchived') === 'true';

    // Build MongoDB query
    const query: any = { 'conversation.sid': { $ne: '' }, lastMessage: { $exists: true, $ne: null }, 'lastMessage.dateCreated': { $exists: true, $ne: null } };

    // Prepare $and array for combining filters
    const andFilters: any[] = [];

    // Search logic: match multiple fields, allow partial/normalized phone
    if (q) {
      const phoneDigits = q.replace(/\D/g, '');
      const orFilters: any[] = [
        { fullname: { $regex: q, $options: 'i' } },
        { 'conversation.customerName': { $regex: q, $options: 'i' } },
      ];
      if (phoneDigits.length > 0) {
        orFilters.push({ phone: { $regex: phoneDigits, $options: 'i' } });
      }
      andFilters.push({ $or: orFilters });
    }

    // Branch filter
    if (branch && branch !== '' && branch !== 'all') {
      andFilters.push({
        $or: [
          { 'conversation.linkedBranch': branch },
          { 'conversation.linkedBranch': null },
        ]
      });
    }

    // Archived filter
    if (!showArchived) {
      andFilters.push({
        $or: [
          { 'conversation.archivedBy': { $exists: false } },
          { 'conversation.archivedBy': { $size: 0 } },
        ]
      });
    }

    // Combine all filters
    if (andFilters.length > 0) {
      query.$and = andFilters;
    }

    // Count total filtered
    const total = await Contact.countDocuments(query);
    // Fetch filtered, sorted, and paginated
    const contacts = await Contact.find(query)
      .sort({ 'lastMessage.dateCreated': -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.debug('[Conversations API] Query:', JSON.stringify(query, null, 2));
      console.debug('[Conversations API] Results count:', contacts.length);
    }
    const pages = Math.ceil(total / limit);
    const result = contacts.map((contact: any) => ({
      sid: contact.conversation.sid,
      friendlyName: contact.fullname || contact.conversation.customerName || contact.phone,
      dateCreated: contact.createdAt ? new Date(contact.createdAt).toISOString() : '',
      dateUpdated: contact.updatedAt ? new Date(contact.updatedAt).toISOString() : '',
      state: null,
      readBy: contact.conversation.readBy || [],
      archivedBy: contact.conversation.archivedBy || [],
      linkedReservationId: contact.conversation.linkedReservationId || null,
      linkedBranch: contact.conversation.linkedBranch || null,
      customerName: contact.conversation.customerName || null,
      lastMessage: contact.lastMessage,
      isRead: userId ? (contact.conversation.readBy || []).includes(userId) : false,
    }));

    const response = {
      conversations: result,
      total,
      page,
      pages,
      limit,
      hasNextPage: page < pages,
      hasPrevPage: page > 1,
    };

    return NextResponse.json(response);
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('[Conversations API] Error:', error);
    }
    return NextResponse.json({ error: error.message || 'Failed to fetch conversations' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!accountSid || !authToken) {
    return NextResponse.json({ error: 'Twilio credentials not set' }, { status: 500 });
  }
  if(!session){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!session.user.permissions){
    session.user.permissions=await getUserPermissions(session);
  }
  if(!canUserViewMessages(session.user)){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const body = await req.json();
    const { friendlyName, phone, participants } = body;

    // Validate required fields
    if (!friendlyName) {
      return NextResponse.json({ error: 'friendlyName is required' }, { status: 400 });
    }
    if (!phone) {
      return NextResponse.json({ error: 'phone is required' }, { status: 400 });
    }
  await dbConnect();
    // Normalize phone to 10 digits
    let normalizedPhone = normalizePhoneNumber(phone);
    let contact = await Contact.findOne({ phone: normalizedPhone });
    if (!contact) {
      contact = await Contact.create({
        conversation: { sid: '' },
        phone: normalizedPhone,
        fullname: '',
      });
    }

    const result = await createTwilioConversation({
      friendlyName,
      contactId: contact._id.toString(),
      participants,
    });
    // Update the contact with the Twilio conversation SID
    contact.conversation.sid = result.sid;
    await contact.save();

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.debug('[Conversations API] Created conversation:', result);
    }

    return NextResponse.json(result, { status: 201 });
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('[Conversations API] Error:', error);
    }
    return NextResponse.json({ error: error.message || 'Failed to create conversation' }, { status: 500 });
  }
} 