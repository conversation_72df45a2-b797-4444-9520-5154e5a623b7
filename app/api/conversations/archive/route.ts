import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Contact } from '@/app/models/Contact';
import { canUserViewMessages } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '../../utils/server-permission-utils';
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
import dbConnect from '@/lib/db';

export async function POST(req: NextRequest) {
  if (!accountSid || !authToken) {
    return NextResponse.json({ error: 'Twilio credentials not set' }, { status: 500 });
  }
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if(!session.user.permissions){
      session.user.permissions=await getUserPermissions(session);
    }
    if(!canUserViewMessages(session.user)){
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const body = await req.json();
    const { sids, archive, beforeDate } = body;
    if (beforeDate) {
      // Archive all conversations whose lastMessage.dateCreated is before beforeDate
      const userId = session.user.id;
    await dbConnect();
      const before = new Date(beforeDate);
      // Find all contacts with lastMessage.dateCreated < beforeDate and not already archived by this user
      const contacts = await Contact.find({
        lastMessage: { $ne: null },
        'lastMessage.dateCreated': { $lt: before },
        'conversation.archivedBy': { $ne: userId }
      });
      const results = [];
      for (const contact of contacts) {
        try {
          let archivedBy = Array.isArray(contact.conversation.archivedBy) ? contact.conversation.archivedBy : [];
          if (archive) {
            if (!archivedBy.includes(userId)) archivedBy.push(userId);
          } else {
            archivedBy = [];//archivedBy.filter((id: string) => id !== userId);
          }
          contact.conversation.archivedBy = archivedBy;
          await contact.save();
          results.push({ sid: contact.conversation.sid, archived: archive });
        } catch (err) {
          results.push({ sid: contact.conversation.sid, error: (err as any)?.message || 'Failed to update', archived: archive });
        }
      }
      const notificationPayload = {
        type: 'archive_conversations',
        data: {
          archivedBy: {name: session.user.name, id: session.user.id},
          sids: results.map(r => ({ sid: r.sid, archived: archive })),
         },
      };
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL?.endsWith('/')?process.env.NEXT_PUBLIC_APP_URL:process.env.NEXT_PUBLIC_APP_URL+'/'}api/socket/emit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationPayload),
      });
      return NextResponse.json({ results });
    }
    if (!Array.isArray(sids) || typeof archive !== 'boolean') {
      return NextResponse.json({ error: 'sids (string[]) and archive (boolean) are required' }, { status: 400 });
    }
    const userId = session.user.id;
    const results = [];
  await dbConnect();
    for (const sid of sids) {
      try {
        // Find the contact by conversation.sid
        const contact = await Contact.findOne({ 'conversation.sid': sid });
        if (!contact) {
          results.push({ sid, error: 'Contact not found', archived: archive });
          continue;
        }
        let archivedBy: string[] = Array.isArray(contact.conversation.archivedBy) ? contact.conversation.archivedBy : [];
        if (archive) {
          if (!archivedBy.includes(userId)) archivedBy.push(userId);
        } else {
          archivedBy = [];//archivedBy.filter((id: string) => id !== userId);
        }
        contact.conversation.archivedBy = archivedBy;
        await contact.save();
        results.push({ sid, archived: archive });
      } catch (err) {
        results.push({ sid, error: (err as any)?.message || 'Failed to update', archived: archive });
      }
    }
    const notificationPayload = {
      type: 'archive_conversations',
      data: {
        archivedBy: {name: session.user.name, id: session.user.id},
        sids: results.map(r => ({ sid: r.sid, archived: archive })),
       },
    };
    await fetch(`${process.env.NEXT_PUBLIC_APP_URL?.endsWith('/')?process.env.NEXT_PUBLIC_APP_URL:process.env.NEXT_PUBLIC_APP_URL+'/'}api/socket/emit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notificationPayload),
    });
    return NextResponse.json({ results });
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('[Conversations API] Archive error:', error);
    }
    return NextResponse.json({ error: error.message || 'Failed to archive conversations' }, { status: 500 });
  }
} 