import { NextRequest, NextResponse } from 'next/server';
import twilio from 'twilio';
import { Contact } from '@/app/models/Contact';
import dbConnect from '@/lib/db';

export async function POST(req: NextRequest) {
  try {
    // Parse form data from Twilio
    const formData = await req.formData();
    const eventType = formData.get('EventType');
    const messageSid = formData.get('MessageSid');
    const conversationSid = formData.get('ConversationSid');
    const author = formData.get('Author');
    const body = formData.get('Body');
    const dateCreated = formData.get('DateCreated');

    // Only handle onMessageAdded events
    if (eventType !== 'onMessageAdded') {
      return NextResponse.json({ success: true, ignored: true });
    }

    // Optionally: Validate Twilio signature (skip in development)
    const twilioSignature = req.headers.get('x-twilio-signature');
    if (process.env.NODE_ENV !== 'development') {
      const url = `${process.env.NEXT_PUBLIC_APP_URL}/api/conversations/webhook`;
      const params = Object.fromEntries(formData.entries());
      const isValid = twilio.validateRequest(
        process.env.TWILIO_AUTH_TOKEN || '',
        twilioSignature || '',
        url,
        params
      );
      if (!isValid) {
        return NextResponse.json({ error: 'Invalid signature' }, { status: 403 });
      }
    }

    // Store the message in the Contact model
  await dbConnect();
    // Removed message persistence logic here

    // Emit notification through the web socket
    const notificationPayload = {
      type: 'conversation_message_added',
      data: {
        messageSid,
        conversationSid,
        author,
        body,
        dateCreated,
      },
    };
    await fetch(`${process.env.NEXT_PUBLIC_APP_URL?.endsWith('/')?process.env.NEXT_PUBLIC_APP_URL:process.env.NEXT_PUBLIC_APP_URL+'/'}api/socket/emit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notificationPayload),
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing Twilio conversation webhook:', error);
    return NextResponse.json({ error: 'Failed to process webhook' }, { status: 500 });
  }
} 