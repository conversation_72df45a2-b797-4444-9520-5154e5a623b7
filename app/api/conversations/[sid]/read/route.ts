import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Contact } from '@/app/models/Contact';
import { getUserPermissions } from '../../../utils/server-permission-utils';
import { canUserViewMessages } from '@/lib/utils/permissions-utils';
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
import dbConnect from '@/lib/db';

export async function PATCH(
  req: NextRequest,
  { params }: { params: { sid: string } }
) {
  const p = await params;
  if (!accountSid || !authToken) {
    return NextResponse.json({ error: 'Twilio credentials not set' }, { status: 500 });
  }
  const session = await getServerSession(authOptions);
  if(!session){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!session.user.permissions){
    session.user.permissions=await getUserPermissions(session);
  }
  if(!canUserViewMessages(session.user)){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const { sid } = p;
  if (!sid) {
    return NextResponse.json({ error: 'Conversation SID is required' }, { status: 400 });
  }

  try {
    const body = await req.json();
    const { read } = body;
    if (typeof read !== 'boolean') {
      return NextResponse.json({ error: 'read(boolean) is required' }, { status: 400 });
    }
    const userId = session.user.id;
  await dbConnect();
    // Find the contact by conversation.sid
    const contact = await Contact.findOne({ 'conversation.sid': sid });
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    let readBy: string[] = Array.isArray(contact.conversation.readBy) ? contact.conversation.readBy : [];
    if (read) {
      if (!readBy.includes(userId)) readBy.push(userId);
    } else {
      readBy = readBy.filter((id) => id !== userId);
    }
    await Contact.findOneAndUpdate(
      { 'conversation.sid': sid },
      { 'conversation.readBy': readBy }
    );
    return NextResponse.json({ readBy });
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('[Conversations API] Mark as read/unread error:', error);
    }
    return NextResponse.json({ error: error.message || 'Failed to update read status' }, { status: 500 });
  }
} 