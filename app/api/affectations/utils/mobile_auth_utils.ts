import User from "@/models/User";
import { NextResponse } from "next/server";
import dbConnect from "@/lib/db";
export async function getUserFromToken(requestBody:any,requestHeaders:any){
  let session:{user:any}|null=null;
  const db=(await dbConnect()).connection;

  if(requestBody.isMobile){
    const token=requestHeaders.get('Authorization')?.split(' ')[1];
    if(!token){
      return null;
    }
    const user=await db.collection('users').findOne({tokens:{$in:[token]}});
    if(!user){
      return null;
    }
    user.id=user._id;
    session={user:user};    
  }
  return session;
}