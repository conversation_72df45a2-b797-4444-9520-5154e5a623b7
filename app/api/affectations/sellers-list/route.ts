import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Branch from '@/models/Branch';
import Calendar from '@/models/Calendar';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ObjectId } from 'mongodb';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const db= (await dbConnect()).connection;

    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const appointmentId = searchParams.get('appointmentId');
    const appointmentDate = searchParams.get('appointmentDate');
    const appointmentStartTime = searchParams.get('appointmentStartTime');
    const appointmentEndTime = searchParams.get('appointmentEndTime');

    if (!branchId || !appointmentId || !appointmentDate || !appointmentStartTime || !appointmentEndTime) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // 1. Get all sellers for the branch
    const branch = await Branch.findById(branchId);
    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }
    const allUserIds = [
      ...(branch.responsible || []),
      ...(branch.agents || []),
      ...(branch.sellers || [])
    ].map(id => id.toString());
    const uniqueUserIds = Array.from(new Set(allUserIds));

    // 2. Get available sellers for the appointment slot
    // Generate all hours needed for the appointment
    const requiredHours = [];
    const startHour = parseInt(appointmentStartTime.split(':')[0], 10);
    const endHour = parseInt(appointmentEndTime.split(':')[0], 10);
    for (let hour = startHour; hour < endHour; hour++) {
      requiredHours.push(`${hour.toString().padStart(2, '0')}:00`);
    }
    const availabilityKey = `availability.${appointmentDate}`;
    const availableCalendars = await Calendar.find({
      userId: { $in: uniqueUserIds },
      [availabilityKey]: { $all: requiredHours }
    }).select('userId');
    const availableUserIds = availableCalendars.map(calendar => calendar.userId.toString());

    // 3. Get user assignment stats
    // Get the current appointment to exclude it from counting
    const currentAppointment = await dbConnect().then(() =>
      db.collection('appointments').findOne({ _id: new ObjectId(appointmentId) })
    );
    if (!currentAppointment) {
      return NextResponse.json({ error: 'Appointment not found' }, { status: 404 });
    }
    const sameDay = currentAppointment.date;
    // Find all appointments for the same day as the current appointment
    const appointmentsForSameDay = await dbConnect().then(() =>
     db.collection('appointments').find({
        date: sameDay,
        _id: { $ne: new ObjectId(appointmentId) }
      }).toArray()
    );
    const appointmentIdsForSameDay = appointmentsForSameDay.map((apt: any) => apt._id);
    // Daily stats
    const userDailyStats = await dbConnect().then(() =>
        db.collection('reservations').aggregate([
        {
          $match: {
            assigned_user_id: { $in: uniqueUserIds.map(id => new ObjectId(id)), $exists: true },
            appointmentId: { $in: appointmentIdsForSameDay }
          }
        },
        {
          $group: {
            _id: '$assigned_user_id',
            count: { $sum: 1 }
          }
        }
      ]).toArray()
    );
    // Global stats
    const userGlobalStats = await dbConnect().then(() =>
      db.collection('reservations').aggregate([
        {
          $match: {
            assigned_user_id: { $in: uniqueUserIds.map(id => new ObjectId(id)), $exists: true },
            appointmentId: { $ne: new ObjectId(appointmentId) }
          }
        },
        {
          $group: {
            _id: '$assigned_user_id',
            count: { $sum: 1 }
          }
        }
      ]).toArray()
    );
    // Create lookup tables
    const dailyStatsLookup = userDailyStats.reduce((acc: any, stat: any) => {
      acc[stat._id.toString()] = stat.count;
      return acc;
    }, {});
    const globalStatsLookup = userGlobalStats.reduce((acc: any, stat: any) => {
      acc[stat._id.toString()] = stat.count;
      return acc;
    }, {});

    // 4. Get user details
    const users = await User.find({ _id: { $in: uniqueUserIds } }).select('_id name email phone userType');

    // 5. Merge all data
    const sellers = users.map((user: any) => ({
      _id: user._id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      userType: user.userType,
      available: availableUserIds.includes(user._id.toString()),
      dailyAssignmentCount: dailyStatsLookup[user._id.toString()] || 0,
      globalAssignmentCount: globalStatsLookup[user._id.toString()] || 0
    }));

    return NextResponse.json({ sellers });
  } catch (error) {
    console.error('Error in sellers-list route:', error);
    return NextResponse.json({ error: 'Failed to fetch sellers list' }, { status: 500 });
  }
} 