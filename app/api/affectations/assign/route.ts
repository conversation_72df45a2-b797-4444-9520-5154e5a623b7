import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { ObjectId } from 'mongodb';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { sendTemplatedSMS } from '@/lib/sms-templates';
import ReservationStatus from '@/models/ReservationStatus';
import Reservation from '@/models/Reservation';
import ReservationNote from '@/models/ReservationNote';
import { generateCommissions, deleteCommissionsBasedOnRecipient } from '@/app/api/utils/commission-utils';
import { normalizePhoneNumber } from '@/lib/twilio';
import { validateBranchAccess, handleBranchValidationError } from '@/lib/branch-utils';
import User from '@/models/User';
import { getUserFromToken } from '../utils/mobile_auth_utils';
import { getUserRoles, getUserPermissions } from '../../utils/server-permission-utils';
import { emitSocketEvent } from '../../utils/socket-utils';

const statusMapping = {
  'assigned': 'confirmed',
}

export async function POST(request: Request) {
  const requestBody = await request.json();
  const requestHeaders = request.headers;
  let session=await getUserFromToken(requestBody,requestHeaders);
  try {
    // Connect to both MongoDB types for consistency

    const db= (await dbConnect()).connection;
    // Get user session for adding notes
    if(!session){
      session = await getServerSession(authOptions);
    }
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if(!session.user.roles){
      session.user.roles=await getUserRoles(session);
    }
    if(!session.user.permissions){
      session.user.permissions=await getUserPermissions(session);
    }
    
    const { sellerId, reservationId } = requestBody;

    if (!sellerId || !reservationId) {
      return NextResponse.json(
        { error: 'Seller ID and Reservation ID are required' },
        { status: 400 }
      );
    }

    
    
    // Get reservation details to include in SMS
    const reservation = await db.collection('reservations').findOne(
      { _id: new ObjectId(reservationId) }
    );

    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Get the appointment and branchId using the appointmentId
    const appointmentId = reservation.appointmentId;
    if (!appointmentId) {
      return NextResponse.json(
        { error: 'Appointment ID not found in reservation details' },
        { status: 400 }
      );
    }

    const appointment = await db.collection('appointments').findOne(
      { _id: new ObjectId(appointmentId) }
    );

    if (!appointment) {
      return NextResponse.json(
        { error: 'Appointment not found' },
        { status: 404 }
      );
    }

    const branchId = appointment.branchId;
    if (!branchId) {
      return NextResponse.json(
        { error: 'Branch ID not found in appointment details' },
        { status: 400 }
      );
    }
    
    // Validate branch access and get phone number
    let branchPhone;
    try {
      branchPhone = await validateBranchAccess(branchId.toString(), session.user._id,true,session);
    } catch (error) {
      const { message, status } = handleBranchValidationError(error);
      return NextResponse.json({ error: message }, { status });
    }

    // Get seller information to get phone number
    const seller = await db.collection('users').findOne(
      { _id: new ObjectId(sellerId) }
    );

    if (!seller) {
      return NextResponse.json(
        { error: 'Seller not found' },
        { status: 404 }
      );
    }
    
    // Get the status ID for 'confirmed' to use in commission generation
    const confirmedStatus = await ReservationStatus.findOne({ 
      code: statusMapping['assigned'].toUpperCase() 
    });

    // --- Push to previousStatuses ---
    let previousStatuses = Array.isArray(reservation.previousStatuses) ? [...reservation.previousStatuses] : [];
    if(previousStatuses.length==0){
      const newStatus=await ReservationStatus.find({ code: 'new' });
      previousStatuses.push({
        status: 'new',
        statusId: newStatus._id,
        changedAt: reservation.createdAt,
        changedBy: session.user.id ? new ObjectId(session.user.id) : undefined,
      });
    }
    if (confirmedStatus) {
      previousStatuses.push({
        status: statusMapping['assigned'],
        statusId: confirmedStatus._id,
        changedAt: new Date(),
        changedBy: session.user.id ? new ObjectId(session.user.id) : undefined,
      });
    }

    // Update the reservation with the assigned user, status, and previousStatuses
    const result = await db.collection('reservations').updateOne(
      { _id: new ObjectId(reservationId) },
      { 
        $set: { 
          assigned_user_id: new ObjectId(sellerId),
          status: statusMapping['assigned'],
          presentAt: new Date(),
          updatedAt: new Date(),
          previousStatuses
        }
      }
    );

    if (!result.matchedCount) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }
     // Get the updated reservation from Mongoose for consistency with the commission system
     const updatedReservation = await Reservation.findById(reservationId);
    const updateData:any={}
    updateData.status=updatedReservation.status
    if (updatedReservation && updatedReservation.status !== reservation.status){
      console.log("updateData", updateData);
      if(!updateData.assigned_user_id){
          updateData.assigned_user_id=updatedReservation.assigned_user_id
      }
      if(!updateData.sellingAmount){
          updateData.sellingAmount=updatedReservation.sellingAmount
      }
      if(!updateData.confirmationType){
          updateData.confirmationType=updatedReservation.confirmationType
      }
      await emitSocketEvent('status_change', {reservationId: reservationId, status: updateData,userId: session.user.id});
  }
    // Create a note about the assignment
    const noteContent = `Reservation assigned to ${seller.name}. Status changed to ${statusMapping['assigned']}.`;
    
    await db.collection('reservationnotes').insertOne({
      reservationId: new ObjectId(reservationId),
      userId: new ObjectId(session.user.id),
      content: noteContent,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    if (updatedReservation) {
      // If there was a previously assigned user, delete their commissions first
      if (reservation.assigned_user_id) {
        const deletedCommissionUsers = await deleteCommissionsBasedOnRecipient(
          db,
          reservationId,
          confirmedStatus._id.toString(),
          reservation
        );

        // Create a note about the commission deletions if any were deleted
        if (deletedCommissionUsers.length > 0) {
          const userNames = deletedCommissionUsers.join(', ');
          const commissionNoteContent = `Deleted previous commissions for user(s) ${userNames} due to reassignment.`;
          await db.collection('reservationnotes').insertOne({
            reservationId: new ObjectId(reservationId),
            userId: new ObjectId(session.user.id),
            content: commissionNoteContent,
            createdAt: new Date(),
            updatedAt: new Date()
          });
        }
      }

      // Generate commissions based on the new status
      await generateCommissions(updatedReservation, confirmedStatus._id.toString(), session.user._id);
    }

    // Send SMS notification to the assigned user if they have a phone number
    if (seller.phone) {

      try {
        const appointmentDate = new Date(reservation.appointmentDate || reservation.createdAt);
        const formattedDate = appointmentDate.toLocaleDateString('fr-FR');
        const customerName = reservation.customerInfo?.client1Name || 'Client';
        const normalizedPhone = normalizePhoneNumber(seller.phone);

        if(process.env.NODE_ENV === 'production'&& process.env.NEXT_PUBLIC_DEV_SERVER !== 'true') {
          

          // Get branch data for the template
          const branch = await db.collection('branches').findOne(
            { _id: branchId }
          );
          
          // Prepare template data with branch fields
          const templateData: Record<string, string> = {
            sellerName: seller.name || '',
            customerName: customerName,
            appointmentDate: formattedDate
          };
          
          // Add branch fields with 'branch.' prefix
          if (branch) {
            for (const [key, value] of Object.entries(branch)) {
              if (typeof value !== 'object' || value === null) {
                templateData[`branch.${key}`] = String(value);
              } else if (key === '_id') {
                templateData[`branch._id`] = String(value);
              }
            }
          }
          /*
          disabled for now
          await sendTemplatedSMS(
            branchPhone,
            normalizedPhone, 
            'reservation_assignment',
            templateData
          );
          */
        }
      } catch (smsError) {
        console.error('Failed to send SMS notification:', smsError);
        // Continue processing even if SMS fails
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in assign route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}