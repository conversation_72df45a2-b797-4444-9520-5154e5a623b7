import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { ObjectId } from 'mongodb';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

interface UserDetail {
  _id: ObjectId;
  name: string;
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const appointmentId = searchParams.get('appointmentId');
    
    if (!appointmentId) {
      return NextResponse.json(
        { error: 'Appointment ID is required' },
        { status: 400 }
      );
    }

    // Handle the "all" case (or any other non-ObjectId string)
    if (appointmentId === 'all') {
      // Return a default/empty result or handle as needed
      return NextResponse.json({ userStats: [] });
    }

    if (!ObjectId.isValid(appointmentId)) {
      return NextResponse.json(
        { error: 'Invalid Appointment ID' },
        { status: 400 }
      );
    }

    const db= (await dbConnect()).connection;
    
    // Get the current appointment to exclude it from counting
    const currentAppointment = await db.collection('appointments').findOne({
      _id: new ObjectId(appointmentId)
    });

    if (!currentAppointment) {
      return NextResponse.json({ error: 'Appointment not found' }, { status: 404 });
    }

    // Get all users that are available for this current appointment
    // We do this by looking at the users who are in the branch for this appointment
    const branchId = currentAppointment.branchId;
    const branch = await db.collection('branches').findOne({
      _id: new ObjectId(branchId)
    });

    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }

    // Get all users from the branch (responsible, agents, and sellers)
    const allUserIds = [
      ...(branch.responsible || []),
      ...(branch.agents || []),
      ...(branch.sellers || [])
    ].map(id => new ObjectId(id));

    // Find all appointments for the same day as the current appointment
    const sameDay = currentAppointment.date;
    const appointmentsForSameDay = await db.collection('appointments')
      .find({
        date: sameDay,
        _id: { $ne: new ObjectId(appointmentId) } // Exclude current appointment
      })
      .toArray();
    
    const appointmentIdsForSameDay = appointmentsForSameDay.map(apt => apt._id);

    // Find all reservations for other appointments on the same day
    // where these users have been assigned
    const userDailyStats = await db.collection('reservations').aggregate([
      {
        $match: {
          assigned_user_id: { $in: allUserIds, $exists: true },
          appointmentId: { $in: appointmentIdsForSameDay }
        }
      },
      {
        $group: {
          _id: '$assigned_user_id',
          count: { $sum: 1 }
        }
      }
    ]).toArray();

    // Get global stats (all assignments for these users, regardless of date)
    const userGlobalStats = await db.collection('reservations').aggregate([
      {
        $match: {
          assigned_user_id: { $in: allUserIds, $exists: true },
          appointmentId: { $ne: new ObjectId(appointmentId) } // Exclude current appointment
        }
      },
      {
        $group: {
          _id: '$assigned_user_id',
          count: { $sum: 1 }
        }
      }
    ]).toArray();

    // Collect all user IDs from both queries
    const userIdsDaily = userDailyStats.map(stat => new ObjectId(stat._id));
    const userIdsGlobal = userGlobalStats.map(stat => new ObjectId(stat._id));
    
    // Combine and deduplicate user IDs
    const allUserIdStrings = [
      ...userIdsDaily.map(id => id.toString()),
      ...userIdsGlobal.map(id => id.toString())
    ];
    const uniqueUserIdStrings = Array.from(new Set(allUserIdStrings));
    const uniqueUserIds = uniqueUserIdStrings.map(id => new ObjectId(id));
    
    // Get user details to include names
    let userDetails: UserDetail[] = [];
    
    if (uniqueUserIds.length > 0) {
      userDetails = await db.collection('users')
        .find({ _id: { $in: uniqueUserIds } })
        .project({ _id: 1, name: 1 })
        .toArray() as UserDetail[];
    }

    // Create a lookup for daily stats
    const dailyStatsLookup = userDailyStats.reduce((acc, stat) => {
      acc[stat._id.toString()] = stat.count;
      return acc;
    }, {} as Record<string, number>);

    // Create a lookup for global stats
    const globalStatsLookup = userGlobalStats.reduce((acc, stat) => {
      acc[stat._id.toString()] = stat.count;
      return acc;
    }, {} as Record<string, number>);

    // Combine all users and their stats
    const combinedStats = uniqueUserIds.map(userId => {
      const userIdStr = userId.toString();
      const user = userDetails.find(u => u._id.toString() === userIdStr);
      return {
        userId,
        name: user?.name || 'Unknown User',
        dailyAssignmentCount: dailyStatsLookup[userIdStr] || 0,
        globalAssignmentCount: globalStatsLookup[userIdStr] || 0
      };
    });

    return NextResponse.json({ userStats: combinedStats });
  } catch (error) {
    console.error('Error fetching user assignment stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user assignment statistics' },
      { status: 500 }
    );
  }
} 