import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { ObjectId } from 'mongodb';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get branchId from query parameters
    const searchParams = request.nextUrl.searchParams;
    const branchId = searchParams.get('branchId');

    if (!branchId) {
      return NextResponse.json({ error: 'Branch ID is required' }, { status: 400 });
    }

    const db= (await dbConnect()).connection;

    // First, get the branch to fetch its sellers array
    const branch = await db.collection('branches').findOne({
      _id: new ObjectId(branchId)
    });

    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }

    // Get the sellers  IDs from the branch
    const sellerIds = branch.sellers || [];
    
    // Combine sellers 
    const combinedUserIds = [...sellerIds];
    
    if (!combinedUserIds.length) {
      return NextResponse.json({ sellers: [] });
    }

    // Convert string IDs to ObjectId if needed
    const objectIdUserIds = combinedUserIds.map((id: string | ObjectId) => 
      typeof id === 'string' ? new ObjectId(id) : id
    );

    // Fetch all users that are in the sellers and paps arrays
    const sellers = await db.collection('users').find({
      _id: { $in: objectIdUserIds }
    }).project({
      _id: 1,
      name: 1,
      email: 1,
      phone: 1,
      userType: 1
    }).toArray();

    return NextResponse.json({ sellers });
  } catch (error) {
    console.error('Error fetching branch sellers:', error);
    return NextResponse.json({ error: 'Failed to fetch branch sellers' }, { status: 500 });
  }
} 