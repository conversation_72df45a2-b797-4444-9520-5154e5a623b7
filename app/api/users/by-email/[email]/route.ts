import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import dbConnect from '@/lib/db';
import { authOptions } from '@/lib/auth';
import { ObjectId } from 'mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: { email: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }
    const p=await params;
    const email = decodeURIComponent(await p.email);
    
    // Connect to MongoDB
    const db= (await dbConnect()).connection;
    
    // Find user by email
    const user = await db.collection('users').findOne(
      { email },
      { projection: { _id: 1, name: 1, email: 1 } }
    );

    if (!user) {
      return new NextResponse('User not found', { status: 404 });
    }

    // Convert _id to string
    const userData = {
      ...user,
      _id: user._id.toString()
    };

    return NextResponse.json(userData);
  } catch (error) {
    console.error('Error in users/by-email route:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}