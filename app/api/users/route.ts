import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import bcrypt from 'bcryptjs';
import Role from '@/models/Role';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';
import Branch from '@/models/Branch';
import { generateRandomPassword } from '@/lib/utils/password';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import EmailTemplate from '@/models/EmailTemplate';

interface Role {
  id?: string;
  _id?: string;
  name: string;
}

interface UserQuery {
  roles?: Types.ObjectId | Types.ObjectId[] | { $in: Types.ObjectId[] };
}

export const GET =async (request: Request) => {
  await dbConnect();
  
  // Parse query parameters
  const url = new URL(request.url);
  const includeDeleted = url.searchParams.get('includeDeleted') === 'true';
  
  // Build query - exclude deleted users by default
  const query = includeDeleted ? {} : { deletedAt: null };
  
  const users = await User.find(query)
    .populate('roles')
    .populate('directPermissions')
    .select('-password')
    .lean();

  return NextResponse.json({ users });
};

export const POST = async (request: Request) => {
  await dbConnect();
  const body = await request.json();
  
  // Generate a random password (8-10 characters)
  const randomPassword = generateRandomPassword(10);
  
  // Create a clean user object
  const userData: any = {
    name: body.name,
    email: body.email ? body.email.toLowerCase() : undefined,
    roles: body.roles || [],
    directPermissions: body.directPermissions || [],
    isActive: body.isActive !== undefined ? body.isActive : true
  };

  // Handle optional fields
  if (body.phone) {
    userData.phone = body.phone;
  }
  if (body.branchIds) {
    if (Array.isArray(body.branchIds) && body.branchIds.every((id: string) => Types.ObjectId.isValid(id))) {
      userData.branchIds = body.branchIds;
    } else {
      return NextResponse.json(
        { error: 'Invalid branchIds' },
        { status: 400 }
      );
    }
  }
  
  // Handle metadata field - parse JSON string and convert to Map for Mongoose
  if (body.metadata) {
    try {
      // Parse the JSON string to get a regular object
      const metadataObj = JSON.parse(body.metadata);
      // Convert the object to a Map that Mongoose can handle
      userData.metadata = new Map(Object.entries(metadataObj));
    } catch (error) {
      console.error('Error parsing metadata:', error);
      // If there's an error parsing the JSON, we won't include metadata
    }
  }

  // Hash the random password
  userData.password = await bcrypt.hash(randomPassword, 10);

  // Validate required fields
  if (!userData.name || !userData.email) {
    return NextResponse.json(
      { error: 'Name and email are required' },
      { status: 400 }
    );
  }

  // Validate phone number format if provided
  if (userData.phone && (typeof userData.phone !== 'string' || !userData.phone.startsWith('+1') || userData.phone.length < 12)) {
    return NextResponse.json(
      { error: 'Phone number must be in format +1XXXXXXXXXX' },
      { status: 400 }
    );
  }

  // Get the requesting user's roles
  const requestingUserRoles = await Role.find({
    _id: { $in: body.requestingUserRoles || [] }
  });

  // Check if user is trying to assign restricted roles
  const restrictedRoles = ['SuperAdmin', 'BranchesAdmin'];
  const isBranchAdmin = requestingUserRoles.some(role => role.name === 'BranchesAdmin');

  if (isBranchAdmin) {
    // If the user is a BranchAdmin, check if they're trying to assign restricted roles
    const rolesToAssign = await Role.find({ _id: { $in: userData.roles } });
    const hasRestrictedRole = rolesToAssign.some(role => restrictedRoles.includes(role.name));

    if (hasRestrictedRole) {
      return NextResponse.json(
        { error: 'You do not have permission to assign these roles' },
        { status: 403 }
      );
    }
  }

  // Check if email already exists
  const existingUser = await User.findOne({ email: userData.email, $or: [{ deletedAt: null }, { deletedAt: { $exists: false } }] });
  if (existingUser) {
    return NextResponse.json(
      { error: 'Email already exists' },
      { status: 400 }
    );
  }

  console.log('Creating user with data:', JSON.stringify(userData, (key, value) => {
    if (value instanceof Map) {
      return Object.fromEntries(value);
    }
    return value;
  }, 2));
  const user = await User.create(userData);
  const userWithoutPassword = await User.findById(user._id)
    .populate('roles')
    .populate('directPermissions')
    .select('-password');

  // Send welcome email with the generated password using the template from EmailTemplate
  try {
    let template= await EmailTemplate.findOne({ type: 'account-created', disabled: { $ne: true } });
    let emailContent = '<p>Hello {{name}},</p><p>Your account has been created. Your password is: <b>{{password}}</b></p><p>Please log in and change your password as soon as possible.</p>';
    let emailSubject = 'Welcome to AMQ Partners';
    if (template) {
      emailContent = template.content;
      if (template.name) emailSubject = template.name;
    }
    await sendBrevoTemplatedEmail({
      to: [{ email: user.email, name: user.name }],
      subject: emailSubject,
      content: emailContent,
      // Dynamically get variable names from the template and map from user fields
      variables: ([...(template?.variables || []), ...(template?.specialVariables || [])]).reduce((vars: Record<string, any>, key: string) => {
        // Special handling for password
        if (key === 'password') {
          vars[key] = randomPassword;
        } else {
          // Use user doc fields, fallback to empty string if not present
          vars[key] = user[key] ?? '';
        }
        return vars;
      }, {}),
    });
  } catch (emailError) {
    console.error('Failed to send welcome email:', emailError);
    // Optionally: return error or continue
  }

  // Determine the roles of the new user
  const newUserRoles = await Role.find({ _id: { $in: userData.roles } });
  const newUserRoleNames = newUserRoles.map(role => role.name);
  
  // Check for specific roles that require branch updates
  const isAdmin = newUserRoleNames.includes('BranchesAdmin');
  const isAgent = newUserRoleNames.includes('BranchesAgent');
  const isSeller = newUserRoleNames.includes('Seller');
  const isPAP= newUserRoleNames.includes('PAP');
  // Only add users to specific branches if branchIds is provided
  // This prevents automatically adding users to all branches
  if ((isAdmin || isAgent || isSeller || isPAP) && body.branchIds && Array.isArray(body.branchIds) && body.branchIds.length > 0) {
    console.log('Adding new user to specified branches:', {
      isAdmin,
      isAgent,
      isSeller,
      isPAP,
      userId: user._id,
      branchIds: body.branchIds
    });
    
    // Only update specified branches
    const branches = await Branch.find({ _id: { $in: body.branchIds } });
    let updatedBranches = 0;
    
    for (const branch of branches) {
      let branchUpdated = false;
      
      // Add user to appropriate arrays based on roles
      if (isAdmin) {
        branch.responsible.push(user._id);
        branchUpdated = true;
      }
      
      if (isAgent) {
        branch.agents.push(user._id);
        branchUpdated = true;
      }
      
      if (isSeller) {
        branch.sellers.push(user._id);
        branchUpdated = true;
      }
      if(isPAP) {
        branch.pap.push(user._id);
        branchUpdated = true;
      }
      // Save the branch if changes were made
      if (branchUpdated) {
        await branch.save();
        updatedBranches++;
      }
    }
    
    console.log(`Updated ${updatedBranches} branches for new user ${user._id}`);
  }

  return NextResponse.json(userWithoutPassword);
}; 