'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAppSelector } from '@/lib/redux/hooks';
import { useLanguage } from '@/lib/contexts/language-context';
import { canUserCreateEvents } from '@/lib/utils/permissions-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, FileText, ArrowLeft, Save, AlertTriangle, Phone, Calendar, Clock, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { SimplePersonnelManager } from '../components/SimplePersonnelManager';
import { DateTimePicker } from '../components/DateTimePicker';
import PhoneField from '../components/form/PhoneField';
import UserSelect from '../components/form/UserSelect';
import { normalizePhoneNumber } from '../components/form/phone-utils';
import RecurringFields, { RecurringRule } from '../calendar/components/RecurringFields';
import { createLocalDateTime, convertTo24HourFormat } from '@/lib/utils/date-utils';

interface Branch {
  _id: string;
  name: string;
}

interface Partner {
  _id: string;
  name: string;
}

interface EventType {
  _id: string;
  name: string;
  code: string;
}

interface User {
  _id: string;
  name: string;
  email: string;
  roles: any[];
  directPermissions: any[];
  isActive: boolean;
}

interface EventFormData {
  name: string;
  branchId: string;
  partnerId: string;
  eventTypeId: string;
  startDate: Date;
  startTime: string;
  endTime: string;
  location: string;
  clientGoal: number;
  notes: string;
  supervisors: string[];
  cooks: string[];
  resId: string;
  contactInfo: {
    name: string;
    role: string;
    phone: string;
  };
  recurringRule: RecurringRule;
}

interface Personnel {
  supervisors: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
  paps: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
  cooks: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
}

export default function NewEventPage() {
  const router = useRouter();
  const permissions = useAppSelector(state => state.permissions);
  const canCreateEvents = canUserCreateEvents(permissions);
  const { t } = useLanguage();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [eventTypes, setEventTypes] = useState<EventType[]>([]);
  const [availableSupervisors, setAvailableSupervisors] = useState<User[]>([]);
  const [availableCooks, setAvailableCooks] = useState<User[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  
  const [formData, setFormData] = useState<EventFormData>({
    name: '',
    branchId: '',
    partnerId: '',
    eventTypeId: '',
    startDate: new Date(),
    startTime: '09:00',
    endTime: '17:00',
    location: '',
    clientGoal: 0,
    notes: '',
    supervisors: [],
    cooks: [],
    resId: '',
    contactInfo: {
      name: '',
      role: '',
      phone: '',
    },
    recurringRule: {
      enabled: false,
      frequency: 'day',
      endDate: undefined,
      mainDate: new Date(),
    } as RecurringRule,
  });

  const [personnel, setPersonnel] = useState<Personnel>({
    supervisors: [],
    paps: [],
    cooks: []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Check permissions
  useEffect(() => {
    if (!canCreateEvents) {
      router.push('/access-denied');
      return;
    }
  }, [canCreateEvents, router]);

  // Load form data
  useEffect(() => {
    if (canCreateEvents) {
      loadFormData();
    }
  }, [canCreateEvents]);

  // Function to fetch users by role using the new events/users route
  const fetchUsersByRole = async (role: string): Promise<User[]> => {
    const response = await fetch(`/api/events/users?role=${role}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch ${role} users: ${response.statusText}`);
    }

    const data = await response.json();
    return data.users || [];
  };

  const loadFormData = async () => {
    try {
      setLoading(true);

      const [branchesRes, partnersRes, eventTypesRes, supervisors, cooks, allUsers] = await Promise.all([
        fetch('/api/events/branches'),
        fetch('/api/events/partners'),
        fetch('/api/events/types'),
        fetchUsersByRole('supervisor'),
        fetchUsersByRole('cook'),
        fetchUsersByRole('pap') // For the responsible user dropdown, we'll use PAPs as general users
      ]);

      if (!branchesRes.ok || !partnersRes.ok || !eventTypesRes.ok) {
        throw new Error('Failed to load form data');
      }

      const [branchesData, partnersData, eventTypesData] = await Promise.all([
        branchesRes.json(),
        partnersRes.json(),
        eventTypesRes.json()
      ]);

      setBranches(Array.isArray(branchesData) ? branchesData : branchesData.data || []);
      setPartners(Array.isArray(partnersData) ? partnersData : partnersData.data || []);
      setEventTypes(Array.isArray(eventTypesData) ? eventTypesData : eventTypesData.data || []);
      setAvailableSupervisors(supervisors);
      setAvailableCooks(cooks);
      setAllUsers(allUsers); // Using PAPs for general user selection
    } catch (error) {
      console.error('Error loading form data:', error);
      toast.error(t('events.errors.failedToLoadFormData'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof EventFormData, value: string | number | string[] | Date | { name: string; role: string; phone: string } | RecurringRule) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('events.validation.eventNameRequired');
    }

    if (!formData.branchId) {
      newErrors.branchId = t('events.validation.branchRequired');
    }

    if (!formData.partnerId) {
      newErrors.partnerId = t('events.validation.partnerRequired');
    }

    if (!formData.eventTypeId) {
      newErrors.eventTypeId = t('events.validation.eventTypeRequired');
    }

    if (!formData.startDate) {
      newErrors.startDate = t('events.validation.startDateRequired');
    }

    if (!formData.startTime) {
      newErrors.startTime = 'Start time is required';
    }

    if (!formData.endTime) {
      newErrors.endTime = 'End time is required';
    }

    // Validate that end time is after start time (similar to calendar form)
    if (formData.startTime && formData.endTime) {
      try {
        const startTime24h = convertTo24HourFormat(formData.startTime);
        const endTime24h = convertTo24HourFormat(formData.endTime);

        const [startHours, startMinutes] = startTime24h.split(':').map(Number);
        const [endHours, endMinutes] = endTime24h.split(':').map(Number);

        const startTotalMinutes = startHours * 60 + startMinutes;
        const endTotalMinutes = endHours * 60 + endMinutes;

        if (endTotalMinutes <= startTotalMinutes) {
          newErrors.endTime = 'End time must be after start time';
        }
      } catch (error) {
        // If time conversion fails, add validation error
        if (!newErrors.startTime && formData.startTime) {
          newErrors.startTime = 'Invalid time format. Use HH:MM or HH:MM AM/PM';
        }
        if (!newErrors.endTime && formData.endTime) {
          newErrors.endTime = 'Invalid time format. Use HH:MM or HH:MM AM/PM';
        }
      }
    }

    if (!formData.location.trim()) {
      newErrors.location = t('events.validation.locationRequired');
    }

    if (personnel.supervisors.length === 0) {
      newErrors.supervisors = 'Exactly one supervisor is required';
    }

    // Validate recurring rule if enabled
    if (formData.recurringRule && formData.recurringRule.enabled) {
      if (!formData.recurringRule.frequency) {
        newErrors.recurringRule = 'Frequency is required for recurring events';
      }
      if (!formData.recurringRule.endDate) {
        newErrors.recurringRule = 'End date is required for recurring events';
      }
      if (formData.recurringRule.frequency === 'week' && (!Array.isArray(formData.recurringRule.dayOfWeek) || formData.recurringRule.dayOfWeek.length === 0)) {
        newErrors.recurringRule = 'At least one day of the week must be selected for weekly recurrence';
      }
      if (formData.recurringRule.frequency === 'month' && (typeof formData.recurringRule.dayOfMonth !== 'number' || formData.recurringRule.dayOfMonth < 1 || formData.recurringRule.dayOfMonth > 31)) {
        newErrors.recurringRule = 'Day of month must be between 1 and 31 for monthly recurrence';
      }
      if (formData.recurringRule.frequency === 'year' && (!formData.recurringRule.monthDay || typeof formData.recurringRule.monthDay.month !== 'number' || typeof formData.recurringRule.monthDay.day !== 'number')) {
        newErrors.recurringRule = 'Month and day must be specified for yearly recurrence';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error(t('events.validation.fixErrorsBeforeSubmit'));
      return;
    }

    setSaving(true);
    try {
      // Create UTC dates from date and time values (similar to calendar form)
      // Convert time formats to 24-hour format first, then create UTC dates
      const startTime24h = convertTo24HourFormat(formData.startTime);
      const endTime24h = convertTo24HourFormat(formData.endTime);

      const startDate = createLocalDateTime(formData.startDate, startTime24h);
      const endDate = createLocalDateTime(formData.startDate, endTime24h); // Same date for both start and end

      // Prepare event data
      const eventData = {
        ...formData,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        supervisors: personnel.supervisors.map(s => s.userId),
        cooks: personnel.cooks.map(c => c.userId),
        agents: {
          assignedAgents: personnel.paps.map(p => p.userId)
        },
        contactInfo: formData.contactInfo ? {
          ...formData.contactInfo,
          phone: formData.contactInfo.phone ? normalizePhoneNumber(formData.contactInfo.phone) : undefined,
        } : undefined,
        resId: formData.resId || undefined
      };

      // If recurrence is enabled, use the recurring route
      if (formData.recurringRule && formData.recurringRule.enabled) {
        const response = await fetch('/api/events/recurring', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ eventTemplate: eventData, recurringRule: formData.recurringRule }),
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create recurring events');
        }
        const data = await response.json();
        toast.success(`Created ${data.created} events`);
        router.push('/events');
        return;
      }

      const response = await fetch('/api/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create event');
      }

      const newEvent = await response.json();
      toast.success(t('events.createSuccess'));
      router.push('/events');
    } catch (error) {
      console.error('Error creating event:', error);
      toast.error(error instanceof Error ? error.message : t('events.errors.failedToCreate'));
    } finally {
      setSaving(false);
    }
  };

  const handlePersonnelChange = (newPersonnel: Personnel) => {
    setPersonnel(newPersonnel);

    // Clear supervisor error if exactly one supervisor is assigned
    if (newPersonnel.supervisors.length === 1 && errors.supervisors) {
      setErrors(prev => ({
        ...prev,
        supervisors: ''
      }));
    }
  };

  // Update recurring rule mainDate when start date changes
  useEffect(() => {
    if (formData.startDate) {
      setFormData(prev => ({
        ...prev,
        recurringRule: {
          ...prev.recurringRule,
          mainDate: formData.startDate
        }
      }));
    }
  }, [formData.startDate]);

  if (!canCreateEvents) {
    return null;
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">{t('events.loadingFormData')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('events.createNewEvent')}</h1>
            <p className="text-muted-foreground">{t('events.createNewEventDescription')}</p>
          </div>
        </div>
      </div>

      {/* Event Details Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t('events.form.eventDetails')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('events.form.eventName')} *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder={t('events.form.enterEventName')}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">{t('events.form.location')} *</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder={t('events.form.enterEventLocation')}
                className={errors.location ? 'border-red-500' : ''}
              />
              {errors.location && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.location}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="branch">{t('events.form.branch')} *</Label>
              <Select
                value={formData.branchId}
                onValueChange={(value) => handleInputChange('branchId', value)}
              >
                <SelectTrigger className={errors.branchId ? 'border-red-500' : ''}>
                  <SelectValue placeholder={t('events.form.selectBranch')} />
                </SelectTrigger>
                <SelectContent>
                  {branches && branches.length > 0 ? branches.map((branch) => (
                    <SelectItem key={branch._id} value={branch._id}>
                      {branch.name}
                    </SelectItem>
                  )) : (
                    <SelectItem value="no-branches" disabled>{t('events.form.noBranchesAvailable')}</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.branchId && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.branchId}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="partner">{t('events.partner')} *</Label>
              <Select
                value={formData.partnerId}
                onValueChange={(value) => handleInputChange('partnerId', value)}
              >
                <SelectTrigger className={errors.partnerId ? 'border-red-500' : ''}>
                  <SelectValue placeholder={t('events.selectPartner')} />
                </SelectTrigger>
                <SelectContent>
                  {partners && partners.length > 0 ? partners.map((partner) => (
                    <SelectItem key={partner._id} value={partner._id}>
                      {partner.name}
                    </SelectItem>
                  )) : (
                    <SelectItem value="no-partners" disabled>{t('events.form.noPartnersAvailable')}</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.partnerId && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.partnerId}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="eventType">{t('events.type')} *</Label>
              <Select
                value={formData.eventTypeId}
                onValueChange={(value) => handleInputChange('eventTypeId', value)}
              >
                <SelectTrigger className={errors.eventTypeId ? 'border-red-500' : ''}>
                  <SelectValue placeholder={t('events.selectEventType')} />
                </SelectTrigger>
                <SelectContent>
                  {eventTypes && eventTypes.length > 0 ? eventTypes.map((eventType) => (
                    <SelectItem key={eventType._id} value={eventType._id}>
                      {eventType.name} ({eventType.code})
                    </SelectItem>
                  )) : (
                    <SelectItem value="no-event-types" disabled>{t('events.form.noEventTypesAvailable')}</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.eventTypeId && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.eventTypeId}
                </p>
              )}
            </div>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">{t('events.date')} *</Label>
              <DateTimePicker
                type="date"
                value={formData.startDate ? formData.startDate.toISOString().split('T')[0] : ''}
                onChange={(value) => {
                  if (value) {
                    handleInputChange('startDate', new Date(value));
                  }
                }}
                placeholder={t('events.pickDate')}
                error={!!errors.startDate}
              />
              {errors.startDate && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.startDate}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="startTime">{t('events.startTime')} *</Label>
              <Input
                id="startTime"
                value={formData.startTime}
                onChange={(e) => handleInputChange('startTime', e.target.value)}
                placeholder="HH:MM or HH:MM AM/PM"
                className={errors.startTime ? 'border-red-500' : ''}
              />
              {errors.startTime && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.startTime}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endTime">{t('events.endTime')} *</Label>
              <Input
                id="endTime"
                value={formData.endTime}
                onChange={(e) => handleInputChange('endTime', e.target.value)}
                placeholder="HH:MM or HH:MM AM/PM"
                className={errors.endTime ? 'border-red-500' : ''}
              />
              {errors.endTime && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.endTime}
                </p>
              )}
            </div>
          </div>

          {/* Recurring Fields */}
          <RecurringFields
            value={{
              ...formData.recurringRule,
              mainDate: formData.startDate,
            }}
            onChange={val => handleInputChange('recurringRule', { ...val, mainDate: formData.startDate })}
            disabled={saving}
          />

        </CardContent>
      </Card>

      {/* Personnel Assignment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('events.form.personnelAssignment')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <SimplePersonnelManager
            initialPersonnel={personnel}
            onPersonnelChange={handlePersonnelChange}
            availableSupervisors={availableSupervisors}
            availableCooks={availableCooks}
            readOnly={false}
            maxSupervisors={1}
          />
          {errors.supervisors && (
            <p className="text-sm text-red-500 flex items-center gap-1 mt-2">
              <AlertTriangle className="h-3 w-3" />
              {errors.supervisors}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Additional Details and Contact Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Additional Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t('events.form.additionalDetails')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="clientGoal">{t('events.clientGoal')}</Label>
              <Input
                id="clientGoal"
                type="number"
                value={formData.clientGoal || ''}
                onChange={(e) => handleInputChange('clientGoal', parseInt(e.target.value) || 0)}
                placeholder={t('events.form.expectedClients')}
                min="0"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">{t('events.notes')}</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder={t('events.form.additionalNotes')}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              {t('events.contactInfo')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="resId">{t('events.responsibleUser')}</Label>
              <UserSelect
                users={allUsers}
                value={formData.resId}
                onValueChange={(value) => handleInputChange('resId', value)}
                disabled={loading}
              />
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="contactName">{t('events.contactName')}</Label>
                <Input
                  id="contactName"
                  value={formData.contactInfo.name}
                  onChange={(e) => handleInputChange('contactInfo', { ...formData.contactInfo, name: e.target.value })}
                  placeholder={t('events.form.enterContactName')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactRole">{t('events.contactRole')}</Label>
                <Input
                  id="contactRole"
                  value={formData.contactInfo.role}
                  onChange={(e) => handleInputChange('contactInfo', { ...formData.contactInfo, role: e.target.value })}
                  placeholder={t('events.form.enterContactRole')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactPhone">{t('events.contactPhone')}</Label>
                <PhoneField
                  value={formData.contactInfo.phone}
                  onChange={(value: string) => handleInputChange('contactInfo', { ...formData.contactInfo, phone: value })}
                  placeholder={t('events.contactPhonePlaceholder')}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Submit Button */}
      <div className="flex justify-end pt-6">
        <Button
          type="submit"
          size="lg"
          disabled={loading}
          onClick={handleSubmit}
          className="min-w-[200px]"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {t('events.creating')}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {t('events.createEvent')}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
