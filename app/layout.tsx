import './globals.css';
import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import Script from 'next/script';
import { Toaster } from "@/components/ui/toaster";
import { AppProviders } from './providers';
import { Providers } from '@/components/providers';
import { LayoutWrapper } from '@/components/layout-wrapper';
import { Toaster as HotToaster } from 'react-hot-toast';
import { LanguageProvider } from '@/lib/contexts/language-context';
import { NotificationProvider } from '@/lib/contexts/notification-context';
import { TooltipProvider } from "@/components/ui/tooltip";
import { initSentry } from '../lib/utils/sentry';
import { ReduxProvider } from '@/lib/redux/provider';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from '@/components/theme-provider';
import { SubMenuProvider } from '@/components/dynamic-sub-menu/sub-menu-provider';

// Import service initializers on the server side only
if (typeof window === 'undefined') {
  // Invoice automation initializer
  import('@/lib/init')
    .catch(err => console.error('Failed to load invoice automation initializer:', err));
}

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  preload: true,
  adjustFontFallback: false
});
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: 'AMQ Partners Platform',
  description: 'Comprehensive partner management platform for AMQ, featuring reservation scheduling, branch management, and client relationship tools. Streamline your operations with our intuitive dashboard.',
};

if (process.env.NODE_ENV === 'production') {
  initSentry();
} else if (process.env.NODE_ENV === 'development') {
  console.debug('Sentry not initialized in development');
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <Script
        src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GEOMAP_API_KEY}&libraries=places`}
        strategy="afterInteractive"
      />

      {process.env.NODE_ENV === 'production' && (
        <Script >
          {`
     (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:6413905,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
`}
        </Script>
      )}
      <body className={inter.className} suppressHydrationWarning>
        <AppProviders>
          <Providers>
            <LayoutWrapper>
              {children}
            </LayoutWrapper>
          </Providers>
        </AppProviders>
        <Toaster />
        <HotToaster position="top-right" />
      </body>
    </html>
  );
}