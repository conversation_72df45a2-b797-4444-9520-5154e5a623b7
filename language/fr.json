{"accessDenied": {"title": "<PERSON><PERSON>ès refusé", "description": "Vous n'avez pas la permission d'accéder à cette page."}, "dateCalendar": {"selectDate": "Sélectionner une date", "year": "<PERSON><PERSON>", "month": "<PERSON><PERSON>", "cancel": "Annuler", "clear": "<PERSON><PERSON><PERSON><PERSON>"}, "sidebar": {"searchPlaceholder": "Rechercher dans le menu...", "invoiceItemTypes": "Types d'éléments facturés", "contactRequests": "Demandes de contact", "allContactRequests": "Toutes les demandes de contact", "contactRequestsSources": "Sources de demandes de contact", "conversationsNew": "Conversations (Nouveau)", "billing": "Facturation", "auditLogs": "Journaux d'audit", "notifications": "Notifications", "eventReportValidation": "Validation des Rapports d'Événements", "signedOn": "<PERSON><PERSON> le", "adminDashboard": "Tableau de bord Admin", "dashboard": "Tableau de bord", "home": "Accueil", "users": "Utilisateurs (Ancien)", "categories": "Catégories", "products": "Produits", "orders": "Commandes", "permissions": "Permissions", "roles": "<PERSON><PERSON><PERSON>", "settings": "Paramètres", "foods": "Aliments", "branches": "Succursales", "events": "Événements", "calendar": "<PERSON><PERSON><PERSON>", "eventsList": "Liste des événements", "eventsDashboard": "Événements", "eventsReports": "Rapports d'événements", "reservations": "Réservations", "allReservations": "Toutes les Réservations", "recontactReservations": "À Relancer", "recontactStatuses": "Statuts de Recontact", "attendance": "Présences", "partners": "Entreprises", "regions": "Régions", "generalSettings": "Paramètres généraux", "appointmentsDisponibilities": "Plannings", "reservationStatuses": "Statuts de Réservation", "allergies": "Allergies", "translations": "Traductions", "disponibilities": "Disponibilités", "appointments": "<PERSON><PERSON><PERSON>vous", "calendars": "Calendriers", "myCalendar": "<PERSON>", "selectUser": "Sélectionner un utilisateur pour gérer son calendrier", "affectations": "Affectations", "affectationsDev": "Affectations (Nouveau)", "affectationsList": "Liste des affectations", "conversations": "Conversations", "sms": "SMS", "serviceTypes": "Types de Service", "sellerDashboard": "Tableau de bord vendeur", "commissionTypes": "Types de Commissions", "commissions": "Commissions", "commissionsList": "Liste des commissions", "userCommissions": "Commissions par utilisateur", "weeklyCommissions": "Commissions Hebdo.", "papDashboard": "Tableau de bord PAP", "emailDemo": "Intégration Email", "requests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestsPage": "Gestion des Requêtes", "scheduledSms": "SMS Programmés", "brevoTemplates": "<PERSON><PERSON><PERSON><PERSON>", "devUsers": "Utilisateurs", "contractManagement": "Gestion des Contrats", "eventsCalendar": "<PERSON><PERSON><PERSON>", "eventTypes": "Types d'événements", "facturation": "Facturation", "contacts": "Contacts", "deliveryAvailability": "Disponibilité des livraisons", "invitations": "Invitations", "reservationsAccess": "Accès aux Réservations", "reservationsExperimental": "Réservations (Expérimental)", "collectionHistory": "Historique des Collections"}, "sms": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Personnalisez les messages envoyés aux utilisateurs. Utilisez des variables comme {{variable}} pour insérer du contenu dynamique.", "reservation_assignment": "Affectation de réservation", "reservation_confirmation": "Confirmation de réservation", "reservation_reminder": "Rappel de réservation", "thank_client": "Message de remerciement", "reservation_assignment_description": "Envoy<PERSON> lorsqu'un vendeur est affecté à une réservation", "reservation_confirmation_description": "Envoyé pour confirmer une nouvelle réservation", "reservation_reminder_description": "Envoyé comme un rappel avant un rendez-vous", "thank_client_description": "Envoyé pour remercier les clients après leur visite", "reservation_assignment_template": "Bonjour {{sellerName}}, vous avez été affecté(e) à une réservation pour {{customerName}} le {{appointmentDate}}. Merci de vous connecter à l'application pour plus de détails.", "reservation_confirmation_template": "Bonjour {{customerName}}, votre réservation pour le {{appointmentDate}} a été confirmée. Merci pour votre confiance.", "reservation_reminder_template": "Rappel: vous avez un rendez-vous prévu le {{appointmentDate}}. En cas d'empêchement, merci de nous contacter.", "thank_client_template": "Bonjour {{customerName}}, nous vous remercions pour votre visite à {{branchName}}. Nous espérons que vous avez apprécié l'expérience et nous serions ravis de vous accueillir à nouveau prochainement!", "sendTestSMS": "Envoyer un SMS de test", "saveTemplates": "Enregistrer les modèles", "alert": "Attention, les messages SMS sont limités à 160 caractères par segment. Les messages plus longs seront divisés en plusieurs segments (153 caractères chacun). Les variables seront remplacées par les données réelles, donc gardez vos modèles concis.", "newTemplateName": "Nouveau nom de modèle", "addTemplate": "Ajouter un modèle", "triggerStatus": "Statut de déclenchement", "selectStatus": "Sélectionner un statut", "delayHours": "<PERSON><PERSON><PERSON> (heures)", "disabled": "Désactivé", "availableVariables": "Variables disponibles", "noVariables": "Aucune variable pour l'instant. Ajoutez-en depuis la liste ci-dessous.", "deleteTemplate": "Supp<PERSON><PERSON> le modèle", "saveChanges": "Enregistrer les modifications", "saveSuccess": "Enregistré !", "testSMSPrompt": "Entrez le numéro de téléphone pour envoyer un SMS de test :", "testSMSSuccess": "SMS de test envoyé avec succès", "testSMSError": "Échec de l'envoi du SMS de test", "addVariable": "Ajouter une variable", "noVariablesLeft": "Plus de variables disponibles", "previewWithSample": "Aperçu avec des données d'exemple", "templateWithVariables": "Modèle avec variables", "loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON> modèle trouvé.", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce modèle ?", "fetchError": "Échec du chargement des modèles SMS", "updateError": "Échec de la mise à jour du modèle", "addError": "Échec de l'ajout du modèle", "deleteError": "Échec de la suppression du modèle", "statusFetchError": "Échec du chargement des statuts", "insertVariable": "Insérer la variable", "schedulingMode": "Mode de planification", "exactHour": "<PERSON><PERSON> exacte (0-23)", "dayDelay": "<PERSON><PERSON><PERSON> en jours", "addBranchVariable": "Ajouter Variable Succursale", "branchVariables": "Variables Succursale", "noVariablesAvailable": "Aucune variable disponible", "scheduled": {"title": "Messages SMS programmés", "description": "G<PERSON>rer vos messages SMS programmés", "refresh": "Actualiser", "searchPlaceholder": "Rechercher des messages...", "customer": "Client", "phone": "Téléphone", "template": "<PERSON><PERSON><PERSON><PERSON>", "message": "Message", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "scheduledAt": "Programmé pour", "actions": "Actions", "reschedule": "Reprogrammer", "cancel": "Annuler", "canceled": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "sent": "<PERSON><PERSON><PERSON>", "pending": "En attente", "rescheduleTitle": "Reprogrammer le message", "rescheduleDescription": "Choisissez une nouvelle date et heure pour l'envoi de ce message.", "scheduledDate": "Date programmée", "pickDate": "Choisir une date", "confirmReschedule": "Confirmer", "noMessagesFound": "Aucun message programmé trouvé", "messageRescheduled": "Message reprogrammé avec succès", "messageCanceled": "Message annulé avec succès", "rescheduleError": "Échec de la reprogrammation du message", "cancelError": "Échec de l'annulation du message", "cannotReschedule": "Vous ne pouvez reprogrammer que les messages en attente ou échoués", "cannotCancel": "Vous ne pouvez pas annuler un message déjà envoyé", "messagesRefreshed": "Messages actualisés"}}, "notifications": {"description": "Description", "allTime": "Tout le temps", "filtersAndActions": "Filtres et actions", "allNotifications": "Toutes les notifications", "manageAllNotifications": "<PERSON><PERSON><PERSON> toutes les notifications", "successfulDeliveries": "Livraisons réussies", "failedDeliveries": "Livraisons échouées", "create": "<PERSON><PERSON><PERSON>", "statusBreakdown": "Détail du statut", "targetScreen": "<PERSON><PERSON>ran cible", "body": "Corps du message", "users": "Utilisateurs", "customData": "Donn<PERSON>", "totalNotifications": "Nombre total de notifications", "totalUsers": "Total Utilisateurs", "delivered": "Notifications livrées", "failed": "<PERSON><PERSON><PERSON>", "title": "Notifications", "clearAll": "Tout effacer", "noNotifications": "Aucune notification"}, "table": {"actions": "Actions", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "noResults": "Aucun résultat trouvé", "loading": "Chargement...", "previous": "Précédent", "next": "Suivant", "reset": "Réinitialiser", "filters": "Filtres", "resetAllFilters": "Réinitialiser tous les filtres", "columnToggle": "Afficher/Masquer les colonnes", "columnSettings": "Les paramètres de colonnes sont automatiquement enregistrés", "columns": {"customerInfo_client1Name": "Nom du client", "guests": "Invités", "type": "Type", "branchId": "Succursale", "status": "Statut", "customerInfo_city": "Ville", "customerInfo_address": "<PERSON><PERSON><PERSON>", "customerInfo_phone": "Téléphone", "customerInfo_email": "Email", "partnerId": "De la part de", "assigned_user_id": "<PERSON><PERSON><PERSON>", "preferences_visitDate": "Date de visite", "preferences_visitTime": "<PERSON><PERSON> de visite", "createdAt": "<PERSON><PERSON><PERSON>", "notes": "Notes", "messages": "Messages", "serviceTypes": "Types de Service"}, "totalFilteredRows": "Nombre total de réservations filtrées"}, "common": {"processing": "Traitement...", "city": "Ville", "address": "<PERSON><PERSON><PERSON>", "clearFilters": "Effacer les Filtres", "lastMonth": "Le mois dernier", "lastWeek": "La semaine dernière", "updating": "Mise à jour...", "optional": "Facultatif", "info": "Information", "generating": "Génération...", "retry": "<PERSON><PERSON><PERSON><PERSON>", "exporting": "Exportation en cours...", "exportExcel": "Exporter Excel", "createdAt": "<PERSON><PERSON><PERSON>", "pickDate": "Choisir une date", "saved": "Enregistré", "first": "Premier", "page": "Page", "last": "<PERSON><PERSON>", "filters": "Filtres", "prev": "Précédent", "subtotal": "Sous-total", "tax": "Taxes", "more": "Plus", "redirecting": "Redirection en cours...", "enabled": "Activé", "refresh": "Actualiser", "disabled": "Désactivé", "access": "Accéder", "thisMonth": "<PERSON> mois", "thisWeek": "<PERSON><PERSON> se<PERSON>", "thisYear": "<PERSON><PERSON> an<PERSON>", "search": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "save": "Enregistrer", "cancel": "Annuler", "confirm": "Confirmer", "saving": "Enregistrement...", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "published": "<PERSON><PERSON><PERSON>", "draft": "Brouillon", "featured": "En vedette", "all": "Tous", "none": "Aucun", "yes": "O<PERSON>", "no": "Non", "notProvided": "Non fourni", "translateAll": "<PERSON><PERSON><PERSON><PERSON> tout", "accessDenied": "<PERSON><PERSON>ès refusé", "accessDeniedMessage": "Vous n'avez pas la permission d'accéder à cette page. Veuillez contacter votre administrateur si vous pensez qu'il s'agit d'une erreur.", "goBack": "Retour", "returnHome": "Retour à l'accueil", "apply": "Appliquer", "actions": "Actions", "showing": "Affichage de", "of": "sur", "perPage": "par page", "columns": "Colonnes", "hide": "Masquer", "show": "<PERSON><PERSON><PERSON><PERSON>", "saveChanges": "Enregistrer les modifications", "reset": "Réinitialiser", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "warning": "Avertissement", "id": "ID", "capacity": "Capacité", "unknown": "inconnu", "na": "n/d", "created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "deleted": "supprimé", "next": "Suivant", "previous": "Précédent", "back": "Retour", "openMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "somethingWentWrong": "<PERSON><PERSON><PERSON> chose s'est mal passé", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "loading": "Chargement...", "reloading": "Rechargement...", "showAllDates": "Affiche<PERSON> toutes les dates", "dateRange": "Plage de dates", "selectDateRange": "Sélectionner une plage de dates", "from": "<PERSON>", "to": "Au", "today": "<PERSON><PERSON><PERSON>'hui", "branch": "Succursale", "selectBranch": "Sélectionner une succursale", "name": "Nom", "email": "Email", "phone": "Téléphone", "phoneNumber": "Numéro de téléphone", "password": "Mot de passe", "permissions": "Permissions", "newPasswordOptional": "Nouveau mot de passe (Optionnel)", "pressShiftEnterToSend": "Appuyez sur Maj+Entrée pour envoyer", "panLeft": "Défiler à gauche", "panRight": "Défiler à droite", "enterFullscreen": "Passer en plein écran", "exitFullscreen": "<PERSON><PERSON><PERSON> le plein écran", "comingSoon": "Bientôt disponible...", "total": "Total", "linkCopied": "<PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "selectEndDate": "Sélectionner date de fin", "pickEndDate": "<PERSON>euillez sélectionner une date de fin", "showingPage": "Page", "restore": "<PERSON><PERSON><PERSON>", "selected": "Sélectionné", "noResults": "Aucun résultat", "errorFetchingData": "Erreur lors de la récupération des données", "close": "<PERSON><PERSON><PERSON>", "items": "éléments", "dashboard": "Tableau de bord", "users": "Utilisateurs", "logout": "Déconnexion", "login": "Connexion", "branches": "Succursales", "reservations": "Réservations", "settings": "Paramètres", "information": "Information", "view": "Voir", "tomorrow": "<PERSON><PERSON><PERSON>", "yesterday": "<PERSON>er", "day": "Jour", "days": {"short": {"sun": "<PERSON><PERSON>", "mon": "<PERSON>n", "tue": "Mar", "wed": "<PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "Ven", "sat": "Sam"}, "full": {"sunday": "<PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>"}}, "months": {"full": {"january": "<PERSON><PERSON>", "february": "<PERSON><PERSON><PERSON><PERSON>", "march": "Mars", "april": "Avril", "may": "<PERSON>", "june": "Juin", "july": "<PERSON><PERSON><PERSON>", "august": "Août", "september": "Septembre", "october": "Octobre", "november": "Novembre", "december": "Décembre"}, "short": {"jan": "Jan", "feb": "Fév", "mar": "Mar", "apr": "Avr", "may": "<PERSON>", "jun": "Juin", "jul": "<PERSON><PERSON>", "aug": "Août", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Déc"}}, "status": "Statut", "type": "Type", "date": "Date", "time": "<PERSON><PERSON>", "minute": "Minute", "debugInfo": "Informations de débogage", "noPermission": "Vous n'avez pas la permission d'accéder à cette page.", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "clickToSort": "Cliquez pour trier", "createdFrom": "Créé à partir du", "createdTo": "<PERSON><PERSON><PERSON> jusqu'au", "home": "Accueil", "admin": "Administration", "pickADate": "Choisir une date"}, "errors": {"duplicatePhone": "Une réservation avec ce numéro de téléphone existe déjà.", "failedToFetchUsers": "Échec du chargement des utilisateurs"}, "login": {"title": "Connexion", "email": "Email", "password": "Mot de passe", "signIn": "Se connecter", "signingIn": "Connexion en cours...", "partnerPortalMessage": "Vous êtes un Partenaire ou un Agent? Veuillez utiliser le Portail Partenaire dédié pour accéder à votre compte.", "partnerErrorTitle": "Compte Partenaire Détecté", "partnerErrorMessage": "Les comptes partenaires doivent utiliser le Portail Partenaire dédié pour accéder au système. Veuillez contacter votre gestionnaire de compte pour plus d'informations.", "visitPartnerPortal": "Accéder au Portail Partenaire"}, "support": {"title": "Support", "subtitle": "Obtenez de l'aide avec la Plateforme AMQ Partners", "contactInfo": "Informations de Contact", "email": "Support par Email", "emailAddress": "<EMAIL>", "phone": "Support Téléphonique", "phoneNumber": "**************", "businessHours": "Heures d'Ouverture", "businessHoursText": "<PERSON><PERSON> au Vendredi, 9h00 - 17h00 EST", "faq": "Questions Fréquemment Po<PERSON>ées", "faqQuestion1": "Comment réinitialiser mon mot de passe?", "faqAnswer1": "Vous pouvez réinitialiser votre mot de passe en cliquant sur le lien 'Mot de passe oublié' sur la page de connexion et en suivant les instructions envoyées à votre email.", "faqQuestion2": "Comment planifier un rendez-vous?", "faqAnswer2": "Naviguez vers la section Rendez-vous dans votre tableau de bord et cliquez sur 'Nouveau Rendez-vous' pour planifier un nouveau rendez-vous avec les créneaux disponibles.", "faqQuestion3": "Comment consulter mes commissions?", "faqAnswer3": "Allez dans la section Commissions de votre tableau de bord pour voir l'historique de vos commissions et le statut actuel.", "faqQuestion4": "Qui puis-je contacter pour les problèmes techniques?", "faqAnswer4": "Pour les problèmes techniques, veuillez contacter notre équipe de support à <EMAIL> ou appeler notre ligne de support pendant les heures d'ouverture.", "troubleshooting": "Dépannage", "troubleshootingText": "Si vous rencontrez des problèmes avec la plateforme, essayez les étapes suivantes:", "troubleshootingStep1": "V<PERSON><PERSON> le cache et les cookies de votre navigateur", "troubleshootingStep2": "Assurez-vous d'utiliser un navigateur supporté (Chrome, Firefox, Safari, Edge)", "troubleshootingStep3": "Vérifiez votre connexion internet", "troubleshootingStep4": "Essayez de vous déconnecter et de vous reconnecter", "troubleshootingStep5": "Contactez le support si le problème persiste", "additionalResources": "Ressources Supplémentaires", "userGuide": "Guide Utilisateur", "userGuideText": "Accédez à notre guide utilisateur complet pour des instructions détaillées sur l'utilisation de la plateforme.", "systemStatus": "État du Système", "systemStatusText": "Vérifiez l'état actuel de nos services et toute maintenance en cours.", "feedback": "Commentaires", "feedbackText": "Nous apprécions vos commentaires! Veuillez nous faire savoir comment nous pouvons améliorer votre expérience avec la Plateforme AMQ Partners."}, "privacyPolicy": {"title": "Politique de Confidentialité", "lastUpdated": "Dernière mise à jour: Décembre 2024", "introduction": "Introduction", "introductionText": "La Plateforme AMQ Partners ('nous', 'notre', ou 'nos') s'engage à protéger votre vie privée. Cette Politique de Confidentialité explique comment nous collectons, utilisons, divulguons et protégeons vos informations lorsque vous utilisez notre plateforme.", "informationWeCollect": "Informations que Nous Collectons", "personalInformation": "Informations Personnelles", "personalInformationText": "Nous pouvons collecter des informations personnelles que vous nous fournissez directement, incluant mais sans s'y limiter:", "personalInfoItem1": "Nom et informations de contact (email, numéro de téléphone, adresse)", "personalInfoItem2": "Identifiants de compte et informations d'authentification", "personalInfoItem3": "Informations professionnelles (titre de poste, entreprise, affiliation de succursale)", "personalInfoItem4": "Préférences de communication et historique", "usageInformation": "Informations d'Utilisation", "usageInformationText": "Nous collectons automatiquement certaines informations sur votre utilisation de notre plateforme:", "usageInfoItem1": "Informations sur l'appareil (adresse IP, type de navigateur, système d'exploitation)", "usageInfoItem2": "Modèles d'utilisation et données d'interaction", "usageInfoItem3": "Fichiers de log et activité système", "usageInfoItem4": "Cookies et technologies de suivi similaires", "howWeUseInformation": "Comment Nous Utilisons Vos Informations", "useInformationText": "Nous utilisons les informations collectées aux fins suivantes:", "useInfoItem1": "Fournir et maintenir nos services de plateforme", "useInfoItem2": "Traiter les transactions et gérer les rendez-vous", "useInfoItem3": "Communiquer avec vous concernant votre compte et nos services", "useInfoItem4": "Améliorer notre plateforme et développer de nouvelles fonctionnalités", "useInfoItem5": "Assurer la sécurité et prévenir la fraude", "useInfoItem6": "Respecter les obligations légales", "informationSharing": "Partage et Divulgation d'Informations", "sharingText": "Nous ne vendons, n'échangeons ou ne transférons pas vos informations personnelles à des tiers, sauf dans les circonstances suivantes:", "sharingItem1": "Avec votre consentement explicite", "sharingItem2": "Aux fournisseurs de services qui nous aident dans nos opérations", "sharingItem3": "Lorsque requis par la loi ou un processus légal", "sharingItem4": "Pour protéger nos droits, propriété ou sécurité", "dataSecurity": "Sécurité des Données", "dataSecurityText": "Nous mettons en œuvre des mesures techniques et organisationnelles appropriées pour protéger vos informations personnelles contre l'accès non autorisé, l'altération, la divulgation ou la destruction. Cependant, aucune méthode de transmission sur internet n'est sécurisée à 100%.", "dataRetention": "Conservation des Données", "dataRetentionText": "Nous conservons vos informations personnelles seulement aussi longtemps que nécessaire pour accomplir les objectifs décrits dans cette Politique de Confidentialité, à moins qu'une période de conservation plus longue ne soit requise ou permise par la loi.", "yourRights": "<PERSON><PERSON>", "rightsText": "Selon votre localisation, vous pourriez avoir les droits suivants concernant vos informations personnelles:", "rightsItem1": "Accès à vos informations personnelles", "rightsItem2": "Correction d'informations inexactes", "rightsItem3": "Suppression de vos informations personnelles", "rightsItem4": "Restriction du traitement", "rightsItem5": "Portabilité des données", "rightsItem6": "Objection au traitement", "cookies": "Cookies et Technologies de Suivi", "cookiesText": "Nous utilisons des cookies et des technologies similaires pour améliorer votre expérience, analyser les modèles d'utilisation et fournir du contenu personnalisé. Vous pouvez contrôler les paramètres des cookies via les préférences de votre navigateur.", "thirdPartyServices": "Services Tiers", "thirdPartyText": "Notre plateforme peut contenir des liens vers des sites web tiers ou s'intégrer avec des services tiers. Nous ne sommes pas responsables des pratiques de confidentialité de ces tiers.", "childrenPrivacy": "Confidentialité des Enfants", "childrenPrivacyText": "Notre plateforme n'est pas destinée aux enfants de moins de 13 ans. Nous ne collectons pas sciemment d'informations personnelles d'enfants de moins de 13 ans.", "changes": "Modifications de cette Politique de Confidentialité", "changesText": "Nous pouvons mettre à jour cette Politique de Confidentialité de temps à autre. Nous vous informerons de tout changement en publiant la nouvelle Politique de Confidentialité sur cette page et en mettant à jour la date de 'Dernière mise à jour'.", "contact": "<PERSON><PERSON>", "contactText": "Si vous avez des questions concernant cette Politique de Confidentialité, veuillez nous contacter à:", "contactEmail": "<EMAIL>", "contactAddress": "Équipe de Confidentialité de la Plateforme AMQ Partners"}, "products": {"title": "Produits", "addProduct": "Ajouter un produit", "editProduct": "Modifier le produit", "deleteProduct": "Supprimer le produit", "name": "Nom", "price": "Prix", "quantity": "Quantité", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "image": "Image", "description": "Description", "selectCategory": "Sélectionner une catégorie", "makeVisible": "Rendre ce produit visible aux clients", "showFeatured": "Afficher ce produit dans les sections en vedette"}, "users": {"assignedBranches": "Succursales assignées", "beneficiary": "Bénéficiaire", "branches": "Succursales", "deleted": "Supprimé", "title": "Utilisateurs", "addUser": "Ajouter un utilisateur", "editUser": "Modifier l'utilisateur", "deleteUser": "Supprimer l'utilisateur", "changeRole": "<PERSON><PERSON> <PERSON>", "changeRoleTitle": "Changer le rôle de l'utilisateur", "currentRole": "Rôle actuel", "noRole": "Aucun r<PERSON><PERSON>", "selectNewRole": "Sélectionner un nouveau rôle", "changeRoleWarning": "Le changement de rôle d'un utilisateur peut nécessiter des informations supplémentaires. L'utilisateur conservera ses informations standard (nom, e-mail, etc.) mais vous devrez fournir des détails spécifiques au nouveau rôle.", "fillRoleInfo": "Remplissez les informations supplémentaires pour ce rôle", "branchAdminRoleWarning": "En tant qu'Administrateur de Succursale, vous ne pouvez modifier les rôles que pour les succursales auxquelles vous avez accès. Les changements de rôle ne s'appliqueront qu'à vos succursales.", "branchRolesRequired": "Sélectionnez un rôle lié à une succursale (Administrateur, Agent ou Vendeur) pour attribuer des succursales", "name": "Nom", "email": "Email", "role": "R<PERSON><PERSON>", "status": "Statut", "lastLogin": "Dernière connexion", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "Actions", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "selectRole": "Sélectionner un rôle", "active": "Actif", "inactive": "Inactif", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cet utilisateur ?", "deleteWarning": "Êtes-vous sûr de vouloir supprimer cet utilisateur ?", "search": "Rechercher un utilisateur...", "filter": "Filtrer les utilisateurs", "filterByRole": "Filtrer par rôle", "filterByStatus": "Filtrer par statut", "allRoles": "To<PERSON> les rôles", "allStatuses": "Tous les statuts", "noUsers": "Aucun utilisateur trouvé", "userCreated": "Utilisateur c<PERSON>é avec succès", "userUpdated": "Utilisateur mis à jour avec succès", "userDeleted": "Utilisateur supprimé avec succès", "errorCreating": "<PERSON><PERSON>ur lors de la création de l'utilisateur", "errorUpdating": "E<PERSON>ur lors de la mise à jour de l'utilisateur", "errorDeleting": "<PERSON><PERSON><PERSON> lors de la suppression de l'utilisateur", "roles": "<PERSON><PERSON><PERSON>", "directPermissions": "Permissions directes", "description": "<PERSON><PERSON>rer les utilisateurs de l'application, créer de nouveaux utilisateurs et modifier les existants.", "statusDescription": "L'utilisateur pourra accéder au système lorsqu'il est actif", "addUserDescription": "<PERSON><PERSON>er un nouveau compte utilisateur", "editUserDescription": "Modifier les détails de l'utilisateur", "phone": "Téléphone", "noUsersIncludingDeleted": "Aucun utilisateur trouvé (y compris supprimés)", "userRestored": "Utilisateur restauré avec succès", "confirmDelete": "Confirmer la <PERSON>", "basicInfo": "Informations de base", "showDeleted": "Afficher les supprimés", "branchRolesInfo": "Informations sur les rôles de succursale", "roleSpecificInfo": "Informations spécifiques au rôle", "createUser": "C<PERSON>er un utilisateur", "inviteUser": "Inviter un utilisateur", "inviteUserDescription": "Envoyer un email d'invitation pour créer un compte", "firstName": "Prénom", "lastName": "Nom", "permissions": "Permissions", "createRole": "<PERSON><PERSON>er un rôle", "rolesManagement": "Gestion des rôles", "passwordOptional": "Mot de passe (optionnel pour l'invitation)", "permissionsManagement": "Gestion des permissions", "assignPermissions": "Attribuer des permissions", "assignRoles": "Attribuer des rôles", "personal": "Informations personnelles", "changePassword": "Changer le mot de passe", "error": {"emailExists": "Un utilisateur avec cet email existe déjà", "invalidEmail": "<PERSON><PERSON><PERSON> email invalide", "requiredField": "Ce champ est obligatoire", "createFailed": "Échec de la création de l'utilisateur", "updateFailed": "Échec de la mise à jour de l'utilisateur", "deleteFailed": "Échec de la suppression de l'utilisateur", "inviteFailed": "Échec de l'envoi de l'invitation"}, "invitationLinkCopied": "Lien d'invitation copié dans le presse-papiers", "copyLink": "<PERSON><PERSON><PERSON>", "partnerCompany": "Entreprise partenaire", "selectPartnerCompany": "Sélectionnez une entreprise partenaire", "partnerCompanyHelp": "Sélectionnez l'entreprise partenaire à laquelle cet utilisateur sera associé", "checkSpecimenUpload": "Téléchargement de spécimen de chèque", "checkSpecimenUploadHelp": "<PERSON><PERSON><PERSON>z télécharger une image claire ou un PDF d'un chèque annulé pour le traitement des paiements", "additionalDocumentsUpload": "Téléchargement de documents supplémentaires", "additionalDocumentsUploadHelp": "Téléchargez tous les documents supplémentaires requis tels que contrats, certifications ou pièces d'identité", "missingRequiredFields": "Champs obligatoires manquants", "birthDate": "Date de naissance", "address": "<PERSON><PERSON><PERSON>", "city": "Ville", "postalCode": "Code postal", "socialAssurance": "Numéro d'assurance sociale", "emergencyContactName": "Nom de la personne à joindre", "emergencyContactPhone": "Téléphone de la personne à joindre", "taxInfo": {"isTaxable": "Statut fiscal", "taxType": "Type de taxe"}, "partnerId": "Entreprise partenaire"}, "settings": {"translations": "Traductions"}, "branches": {"title": "Succursales", "viewPlan": "Voir le plan", "plan": "Plan", "addBranch": "Ajouter une succursale", "editBranch": "Modifier la succursale", "deleteBranch": "Supprimer la succursale", "name": "Nom", "postalCode": "Code postal", "address": "<PERSON><PERSON><PERSON>", "phone": "Numéro de téléphone", "automatedPhone": "Téléphone automatisé", "primaryRequestPhone": "Téléphone principal pour les demandes", "backupRequestPhone": "Téléphone secondaire pour les demandes", "responsible": "Responsable", "noPhone": "Aucun numéro de téléphone", "actions": "Actions", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cette succursale?", "deleteWarning": "Cette action ne peut pas être annulée.", "search": "Rechercher des succursales...", "noBranches": "Aucune succursale disponible", "branchCreated": "Succursale créée avec succès", "branchUpdated": "Succursale mise à jour avec succès", "branchDeleted": "Succursale supprimée avec succès", "errorCreating": "Erreur lors de la création de la succursale", "errorUpdating": "Erreur lors de la mise à jour de la succursale", "errorDeleting": "E<PERSON>ur lors de la suppression de la succursale", "errorFetching": "Erreur lors de la récupération des succursales", "city": "Ville", "email": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "region": "Région", "selectRegion": "Sélectionner une région", "province": "Province", "users": "Utilisateurs", "selectBranch": "Sélectionner une succursale", "searchBranches": "Rechercher des succursales...", "branchCount": "{{count}} succursales", "noBranchesFound": "Aucune succursale correspondant à votre recherche", "restrictions": "Restrictions", "usersFilteredByBranch": "Les utilisateurs sont filtrés par succursales sélectionnées", "noUsersInSelectedBranches": "Aucun utilisateur trouvé dans les succursales sélectionnées", "showDeleted": "Afficher les succursales supprimées", "deleted": "Supprimée", "setAvailability": "Définir la limite de disponibilité", "setAvailabilityLimit": "Définir la limite de disponibilité de la succursale", "availabilityUpdated": "Limite de disponibilité mise à jour avec succès", "availabilityUpdateFailed": "Échec de la mise à jour de la limite de disponibilité", "availabilityRemoved": "Limite de disponibilité supprimée avec succès", "availabilityRemoveFailed": "Échec de la suppression de la limite de disponibilité", "limitedAvailability": "Limitée", "availableUntil": "Disponible jusqu'au", "reservationLimit": "Limite de réservation jusqu'au", "selectedDate": "Date sélectionnée", "removeLimit": "Supprimer la limite", "generalInfo": "Informations générales", "contactInfo": "Coordonnées", "staffInfo": "Personnel", "branchAdmins": "Administrateurs de succursale", "branchAgents": "Agents de succursale", "noAdminsAvailable": "Aucun administrateur disponible", "noAgentsAvailable": "Aucun agent disponible", "userRole": {"responsible": "Responsable", "agent": "Agent", "seller": "<PERSON><PERSON><PERSON>"}, "requestConfig": "Configuration des demandes", "requestPrompt": "Message de demande", "requestPromptPlaceholder": "Entrez le message qui sera affiché aux clients lors d'une demande...", "requestTags": "Tags de demande", "addTag": "Ajouter un tag"}, "events": {"accessDenied": {"title": "<PERSON><PERSON>ès refusé", "description": "Vous n'avez pas la permission d'accéder aux fonctionnalités de gestion d'événements."}, "title": "Événements", "viewAndManage": "Voir et gérer les événements", "createEvent": "C<PERSON>er un événement", "createNewEvent": "Créer un nouvel événement", "createNewEventDescription": "Configurer un nouvel événement avec des affectations de personnel", "creating": "Création en cours...", "createSuccess": "Événement créé avec succès", "filtersAndSearch": "Filtres et Recherche", "searchPlaceholder": "Rechercher des événements...", "filterByStatus": "Filtrer par statut", "filterByDate": "Filtrer par date", "sortBy": "Trier par", "allStatuses": "Tous les Statuts", "allDates": "Toutes les dates", "upcoming": "À venir", "thisMonth": "Ce mois-ci", "nextMonth": "Le mois prochain", "pastEvents": "Événements passés", "noEventsFound": "Aucun événement trouvé", "noEventsMatchFilters": "Aucun événement ne correspond à vos filtres actuels.", "noEventsFoundFor": "Aucun événement trouvé pour", "eventsFound": "événements trouvés.", "clearFilters": "Effacer les filtres", "loadingFormData": "Chargement des données du formulaire...", "startTime": "<PERSON><PERSON> d<PERSON>", "contactPhone": "Téléphone du Contact", "selectEventType": "Sélectionner un type d'événement", "contactInfo": "Informations de Contact", "contactName": "Nom du <PERSON>", "contactPhonePlaceholder": "Entrez le numéro de téléphone du contact", "contactRole": "<PERSON><PERSON><PERSON> du <PERSON>", "agents": "Agents", "fillEventDetails": "Remplis<PERSON>z les détails de l'événement ci-dessous.", "generalInfo": "Informations générales", "responsibleUser": "Utilisateur Responsable", "requiredAgents": "Agents requis", "assignedAgents": "Agents assignés", "clientGoalDescription": "Objectif client à servir", "addEvent": "Ajouter un événement", "editEvent": "Modifier l'événement", "deleteEvent": "Supprimer l'événement", "name": "Nom", "startDate": "Date de début", "endDate": "Date de fin", "location": "<PERSON><PERSON>", "description": "Description", "calendar": {"noPermissionAccess": "Vous n'avez pas la permission d'accéder au calendrier des événements", "noPermissionCreate": "Vous n'avez pas la permission de créer des événements", "searchEvents": "Rechercher des événements...", "clearAll": "<PERSON><PERSON>"}, "selectCalendar": "Sélectionner un calendrier", "selectLocation": "Sélectionner un lieu", "selectStartDate": "Sélectionner la date de début", "selectEndDate": "Sélectionner la date de fin", "type": "Type", "selectType": "Sélectionner un type", "partner": "Partenaire", "selectPartner": "Sélectionner un partenaire", "clientGoal": "Objectif <PERSON>lient", "colorCodeByBranch": "Code couleur par succursale", "today": "<PERSON><PERSON><PERSON>'hui", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "selectBranch": "Sélectionner une succursale", "selectBranchPlaceholder": "Sélectionner une succursale", "dashboard": "Tableau de Bord des Événements", "list": "Liste des Événements", "branch": "Succursale", "date": "Date", "time": "<PERSON><PERSON>", "capacity": "Capacité", "actions": "Actions", "noEvents": "Aucun événement trouvé", "noEventsForDay": "Aucun événement prévu pour cette journée", "loading": "Chargement des événements...", "error": "Erreur lors du chargement des événements", "pleaseSelectBranch": "Veuillez sélectionner une succursale pour voir les événements", "updateSuccess": "Événement mis à jour avec succès", "deleteSuccess": "Événement supprimé avec succès", "saveError": "Échec de l'enregistrement de l'événement", "deleteError": "Échec de la suppression de l'événement.", "deleteWarning": "Êtes-vous sûr de vouloir supprimer cet événement ? Cette action ne peut pas être annulée.", "endTime": "Heure de fin", "pickDate": "Choisir une date", "allStatus": "Tous les statuts", "free": "Libre", "assigned": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>", "availableToAssign": "Disponible à assigner", "max": "max", "maxSupervisorsReached": "Nombre maximum de superviseurs atteint", "searchSupervisors": "Rechercher des superviseurs...", "searchPapAgents": "Rechercher des agents PAP...", "searchCooks": "Rechercher des cuisiniers...", "noSupervisorsFound": "Aucun superviseur trouvé", "noAvailableSupervisors": "Aucun superviseur disponible", "noPapAgentsFound": "Aucun agent PAP trouvé", "noAvailablePapAgents": "Aucun agent PAP disponible", "noCooksFound": "<PERSON><PERSON>n cuisinier trouvé", "noAvailableCooks": "Aucun cuisinier disponible", "createdAt": "<PERSON><PERSON><PERSON>", "selectDateRange": "Sélectionner une période", "allBranches": "Toutes les succursales", "allPartners": "Tous les partenaires", "allEventTypes": "Tous les types d'événements", "previousMonth": "<PERSON><PERSON>", "online": "En ligne", "home": "À domicile", "family": "<PERSON><PERSON><PERSON>", "currentView": "Vue actuelle", "currentDate": "Date actuelle", "selectedBranch": "Succursale sélectionnée", "selectedPartner": "Partenaire sélectionné", "totalEvents": "Total des événements", "branchesCount": "Nombre de succursales", "eventTypesCount": "Nombre de types d'événements", "view": "<PERSON><PERSON>", "eventType": "Type d'événement", "add": "Ajouter", "notes": "Notes", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet événement ? Cette action ne peut pas être annulée.", "deleted": "Événement supprimé avec succès.", "statusUpdatedSuccessfully": "Statut de l'événement mis à jour avec succès", "status": {"label": "Statut", "new": "Nouveau", "inProgress": "En cours", "processingReport": "Traitement du rapport", "awaitingValidation": "En attente de validation", "completed": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "overdue": "En retard", "pending": "En attente", "processing": "En traitement", "submitted": "<PERSON><PERSON><PERSON>", "validated": "<PERSON><PERSON><PERSON>"}, "sort": {"createdNewestFirst": "<PERSON><PERSON><PERSON> (Plus récent en premier)", "createdOldestFirst": "Créé (Plus ancien en premier)", "dateEarliestFirst": "Date (Plus ancien en premier)", "dateLatestFirst": "Date (Plus récent en premier)", "nameAZ": "Nom (A-Z)", "nameZA": "Nom (Z-A)", "status": "Statut", "location": "<PERSON><PERSON>"}, "tabs": {"all": "Tous", "active": "Actifs", "reporting": "Rapport", "completed": "Te<PERSON>in<PERSON>", "overdue": "En retard"}, "pagination": {"showing": "Affichage de", "to": "à", "of": "sur", "events": "événements"}, "summary": {"title": "Résumé", "activeEvents": "Événements actifs", "inReporting": "En rapport", "completed": "Te<PERSON>in<PERSON>", "overdue": "En retard"}, "card": {"papAgents": "Agents PAP", "ended": "<PERSON><PERSON><PERSON><PERSON>", "ago": "il y a", "inProgress": "En cours", "starts": "Commence dans", "branch": "Succursale", "partner": "Partenaire", "type": "Type", "goal": "Objectif", "clients": "clients", "personnel": "Personnel", "supervisors": "Superviseurs", "cooks": "Cuisiniers", "noneAssigned": "Aucun <PERSON>", "report": "Rapport", "notes": "Notes", "viewDetails": "Voir les détails", "editEvent": "Modifier l'événement", "viewReport": "Voir le rapport", "createReport": "<PERSON><PERSON><PERSON> un rapport", "editReport": "Modifier le rapport"}, "validation": {"viewDetails": "Voir les détails", "eventNameRequired": "Le nom de l'événement est requis", "branchRequired": "La succursale est requise", "partnerRequired": "Le partenaire est requis", "eventTypeRequired": "Le type d'événement est requis", "startDateRequired": "La date de début est requise", "endDateRequired": "La date de fin est requise", "endDateAfterStart": "La date de fin doit être après la date de début", "startTimeRequired": "L'heure de début est requise", "endTimeRequired": "L'heure de fin est requise", "endTimeAfterStart": "L'heure de fin doit être après l'heure de début", "invalidTimeFormat": "Format d'heure invalide. Utilisez HH:MM ou HH:MM AM/PM", "locationRequired": "Le lieu est requis", "supervisorRequired": "Au moins un superviseur est requis", "exactlyOneSupervisorRequired": "Exactement un superviseur est requis", "fixErrorsBeforeSubmit": "<PERSON><PERSON><PERSON><PERSON> corriger les erreurs du formulaire avant de soumettre", "title": "Validation des Rapports d'Événements", "description": "Examiner et valider les rapports d'événements soumis", "bulkActions": "Actions en lot", "totalPending": "Total en attente", "urgent": "<PERSON><PERSON>", "normal": "Normal", "high": "<PERSON><PERSON><PERSON>", "avgWaitTime": "Temps d'attente moyen", "searchPlaceholder": "Rechercher des rapports...", "filterByPriority": "Filtrer par priorité", "allPriorities": "Toutes les priorités", "submissionOldest": "Soumission (Plus ancien en premier)", "submissionNewest": "Soumission (Plus récent en premier)", "priorityHighToLow": "Priorité (Élevée à faible)", "branchAZ": "Succursale (A-Z)", "selectAll": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON>", "noReportsFound": "Aucun rapport trouvé", "noReportsMatchFilters": "Aucun rapport ne correspond à vos filtres actuels.", "noReportsPendingValidation": "Aucun rapport n'est actuellement en attente de validation.", "supervisor": "Superviseur", "duration": "<PERSON><PERSON><PERSON>", "review": "Examiner", "eventDataUnavailable": "Données d'événement indisponibles", "missingEventData": "Données d'Événement Manquantes", "missingEventDataMessage": "Ce rapport a des données d'événement manquantes. Veuillez contacter un administrateur.", "eventDataMissing": "Les données d'événement sont manquantes", "cannotValidateWithoutEvent": "Impossible de valider le rapport sans informations d'événement"}, "errors": {"noPermissionViewAll": "Vous n'avez pas la permission de voir tous les événements", "failedToLoad": "Échec du chargement des événements", "failedToUpdateStatus": "Échec de la mise à jour du statut de l'événement", "failedToLoadFormData": "Échec du chargement des données du formulaire", "failedToCreate": "Échec de la création de l'événement"}, "editPage": {"title": "Modifier l'Événement", "description": "Modifier les détails de l'événement et les affectations de personnel", "back": "Retour", "saving": "Enregistrement...", "saveChanges": "Enregistrer les Modifications", "eventDetails": "Détails de l'Événement", "eventName": "Nom de l'Événement", "eventNameRequired": "Nom de l'Événement *", "enterEventName": "Entrer le nom de l'événement", "branch": "Succursale", "branchRequired": "Succursale *", "selectBranch": "Sélectionner une succursale", "partner": "Partenaire", "partnerRequired": "Partenaire *", "selectPartner": "Sélectionner un partenaire", "eventType": "Type d'Événement", "eventTypeRequired": "Type d'Événement *", "selectEventType": "Sélectionner un type d'événement", "startDate": "Date de Début", "startDateRequired": "Date de Début *", "endDate": "Date de Fin", "endDateRequired": "Date de Fin *", "location": "<PERSON><PERSON>", "locationRequired": "Lieu *", "enterLocation": "Entrer le lieu", "clientServingGoal": "Objectif <PERSON> Servir", "enterClientServingGoal": "Entrer l'objectif client à servir", "notes": "Notes", "addNotesPlaceholder": "Ajouter des notes sur l'événement...", "status": "Statut", "personnelAssignment": "Affectation du Personnel", "personnelAssignmentDescription": "<PERSON><PERSON><PERSON> les superviseurs, PAPs et cuisiniers assignés à cet événement.", "cancel": "Annuler", "pleaseFixFormErrors": "<PERSON><PERSON><PERSON><PERSON> corriger les erreurs du formulaire avant de soumettre", "failedToUpdateEvent": "Échec de la mise à jour de l'événement", "failedToLoadEventDetails": "Échec du chargement des détails de l'événement", "failedToLoadFormData": "Échec du chargement des données du formulaire"}, "reports": {"title": "Rapports d'Événements", "description": "Gérer les rapports d'événements et les calculs de commissions", "searchPlaceholder": "Rechercher des rapports...", "recentlyUpdated": "Récemment mis à jour", "oldestUpdated": "Plus ancien mis à jour", "eventDateLatest": "Date d'événement (Plus récent)", "eventDateEarliest": "Date d'événement (Plus ancien)", "reports": "rapports", "report": "rapport", "noReportsFound": "Aucun rapport trouvé", "noReportsMatchFilters": "Aucun rapport ne correspond à vos filtres actuels.", "reportsFound": "rapports trouvés.", "edit": {"title": "Modifier le Rapport d'Événement", "description": "Modifier le personnel de l'événement, les horaires et soumettre le rapport pour validation", "backToReport": "Retour au Rapport", "eventSummary": "Résumé de l'Événement", "location": "<PERSON><PERSON>", "branch": "Succursale", "partner": "Partenaire", "reportStatus": "Statut du Rapport", "reportNotFound": "Rapport non trouvé.", "noPermissionEdit": "Vous n'avez pas la permission de modifier ce rapport.", "cannotEditValidated": "Ce rapport ne peut pas être modifié car il a été validé.", "noPermissionEditReports": "Vous n'avez pas la permission de modifier les rapports d'événements", "noReportFound": "Aucun rapport trouvé pour cet événement", "failedLoadEventData": "Échec du chargement des données de l'événement", "noReportIdAvailable": "Aucun ID de rapport disponible", "failedLoadReportDetails": "Échec du chargement des détails du rapport", "reportSavedSuccessfully": "Rapport enregistré avec succès", "failedSaveReport": "Échec de l'enregistrement du rapport", "saveFailed": "Échec de l'enregistrement", "reportSubmittedValidation": "Rapport soumis pour validation", "failedSubmitReport": "Échec de la soumission du rapport", "validationFailed": "Échec de la validation"}, "details": {"title": "Rapport d'Événement", "description": "G<PERSON>rer le personnel de l'événement et soumettre le rapport pour validation", "back": "Retour", "editReport": "Modifier le Rapport", "submitReport": "Soumettre le Rapport", "retry": "<PERSON><PERSON><PERSON><PERSON>", "reportNotFound": "Rapport non trouvé ou vous n'avez pas la permission de le voir.", "noPermissionAccess": "Vous n'avez pas la permission d'accéder aux rapports d'événements", "failedLoadEventData": "Échec du chargement des données de l'événement", "failedLoadReport": "Échec du chargement des détails du rapport", "reportSubmittedSuccess": "Rapport soumis pour validation", "reportStatusUpdatedSuccess": "Statut du rapport mis à jour avec succès", "failedSubmitReport": "Échec de la soumission du rapport", "failedUpdateStatus": "Échec de la mise à jour du statut du rapport", "validationFailed": "Échec de la validation", "actualEventDate": "Date Réelle de l'Événement", "scheduledEventDate": "Date Prévue de l'Événement", "reported": "✓ Rapporté", "branch": "Succursale", "partner": "Partenaire", "reportStatus": "Statut du Rapport", "timesShownIn": "Heures affichées en", "actualEventTimes": "Heures Réelles de l'Événement", "actualEventTimesDescription": "Les heures réelles de début et de fin rapportées par les superviseurs", "startTime": "<PERSON><PERSON>but", "endTime": "<PERSON>ure de Fin", "actualEventTimesNotReported": "Les heures réelles de l'événement n'ont pas encore été rapportées", "timesAvailableAfterEdit": "Les heures seront disponibles après modification et sauvegarde du rapport", "eventInformation": "Informations de l'Événement", "eventName": "Nom de l'Événement", "actualEventTimesReported": "Heures Réelles de l'Événement (Rapportées)", "notYetReported": "Pas encore rapporté", "originalScheduledTimes": "Heures Prévues Originales", "location": "<PERSON><PERSON>", "commissionSummary": "Résumé des Commissions", "totalCommissions": "Total des Commissions", "supervisors": "Superviseurs", "paps": "PAPs", "cooks": "Cuisiniers", "totalReservations": "Total des Réservations", "timeRangesShownIn": "Plages horaires affichées en", "hoursWorked": "h travaillées", "percentageShare": "% de part", "commissionEarned": "Commission gagnée", "perReservation": "/réservation", "linkedReservations": "Réservations Liées", "guests": "invités", "noReservationsLinked": "Aucune réservation liée à cet événement.", "papCommission": "Commission PAP", "reportHistory": "Historique du Rapport", "reportCreated": "Rapport Créé", "reportProcessingStarted": "Traitement du Rapport Commencé", "reportSubmittedForValidation": "Rapport Soumis pour Validation", "reportValidated": "Rapport Validé", "notes": "Notes", "validationNotes": "Notes de Validation"}, "tabs": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "personnel": "Personnel", "personnelCommissions": "Personnel et Commissions", "reservations": "Réservations", "history": "Historique"}, "form": {"eventTimes": "Horaires de l'Événement", "eventTimesDescription": "Ajuster les heures réelles de début et de fin de l'événement (affichées en {timezone})", "startDateTime": "Date et Heure de Début", "endDateTime": "Date et Heure de Fin", "date": "Date", "time": "<PERSON><PERSON>", "selectDate": "Sélectionner une date", "selectTime": "Sélectionner une heure", "startTimeInvalid": "L'heure de début est invalide", "endTimeInvalid": "L'heure de fin est invalide", "timezoneNote": "Les heures sont affichées dans votre fuseau horaire local ({timezone})", "supervisors": "Superviseurs", "supervisorsDescription": "Superviseurs de l'événement (lecture seule)", "personnelTimeRanges": "Personnel et Plages Horaires", "personnelDescription": "Gérer les affectations PAP et Cuisiniers avec leurs plages horaires de travail", "papAgents": "Agents PAP", "assigned": "<PERSON><PERSON><PERSON>", "available": "Disponibles", "noPapsAssigned": "Aucun PAP assigné pour le moment", "searchPaps": "Rechercher des PAPs...", "noPapsFound": "Aucun PAP trouvé", "addPap": "Ajouter un PAP", "workingHours": "Heures de Travail", "from": "De", "to": "À", "cooks": "Cuisiniers", "noCooksAssigned": "Aucun cuisinier assigné pour le moment", "searchCooks": "Rechercher des cuisiniers...", "noCooksFound": "<PERSON><PERSON>n cuisinier trouvé", "addCook": "A<PERSON>ter un Cuisinier", "percentage": "Pourcentage", "notes": "Notes", "notesPlaceholder": "Ajouter des notes sur l'événement...", "save": "Enregistrer", "saving": "Enregistrement...", "submitForValidation": "Soumettre pour Validation", "submitting": "Soumission...", "fixValidationErrors": "<PERSON><PERSON><PERSON><PERSON> corriger les erreurs de validation avant de soumettre", "validationFailedDetails": "Échec de la validation : {details}", "papRemoved": "PAP retiré du rapport", "cookRemoved": "Cuisinier retiré du rapport", "papAdded": "PAP ajouté au rapport", "cookAdded": "Cuisinier ajouté au rapport", "invalidTimeData": "Données de temps invalides fournies au curseur", "invalidEventDuration": "Durée d'événement invalide", "timeRange": "<PERSON><PERSON>", "fullEvent": "Événement Complet", "firstHalf": "Première Moitié", "secondHalf": "Seconde Moitié"}}, "form": {"formCompletion": "Progression du formulaire", "supervisorRequired": "Exactement un superviseur est requis", "savedSuccessfully": "Enregistré avec succès !", "validationErrors": "Erreurs de Validation", "formValues": "Valeurs du Formulaire", "timePlaceholder": "HH:MM ou HH:MM AM/PM", "noEventTypesFound": "Aucun type d'événement trouvé", "noBranchesFound": "Aucune succursale trouvée", "noPartnersFound": "Aucun partenaire trouvé", "noUsersFound": "Aucun utilisateur trouvé", "recurrent": "<PERSON><PERSON><PERSON>", "repeatEvery": "<PERSON><PERSON><PERSON><PERSON><PERSON> chaque", "frequency": "<PERSON><PERSON><PERSON>", "frequencies": {"day": "Jour", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "year": "<PERSON><PERSON>"}, "daysOfWeek": "<PERSON><PERSON> de la semaine", "dayOfMonth": "<PERSON>ur du mois", "endDateInclusive": "Date de fin (incluse)", "eventDetails": "Détails de l'événement", "eventName": "Nom de l'événement", "enterEventName": "Entrez le nom de l'événement", "location": "<PERSON><PERSON>", "enterEventLocation": "Entrez le lieu de l'événement", "branch": "Succursale", "selectBranch": "Sélectionner une succursale", "noBranchesAvailable": "Aucune succursale disponible", "personnelAssignment": "Affectation du personnel", "startDateTime": "Date et Heure de Début", "endDateTime": "Date et Heure de Fin", "selectTime": "Sélectionner une heure", "additionalDetails": "Détails Supplémentaires", "expectedClients": "Nombre de clients attendus", "additionalNotes": "Notes supplémentaires ou instructions pour l'événement", "enterContactName": "Entrez le nom du contact", "enterContactRole": "Entrez le rôle du contact", "noPartnersAvailable": "Aucun partenaire disponible", "noEventTypesAvailable": "Aucun type d'événement disponible", "supervisors": "Superviseurs", "papAgents": "Agents PAP", "cooks": "Cuisiniers", "noSupervisorsAssigned": "Aucun superviseur assign<PERSON>", "noPapAgentsAssigned": "Aucun agent PAP assigné", "noCooksAssigned": "Aucun cuisinier assigné"}, "selectUser": "Sélectionner un utilisateur", "details": {"title": "Détails de l'Événement", "eventInformation": "Informations de l'Événement", "dateTime": "Date et Heure", "statusReport": "Statut et Rapport", "eventStatus": "Statut de l'Événement", "reportStatus": "Statut du Rapport", "reportValidationRequired": "Validation du Rapport Requise", "reportValidationMessage": "Ce rapport d'événement a été soumis et attend une validation.", "validateReport": "Valider le Rapport", "viewAllPendingReports": "Voir Tous les Rapports en Attente", "metadata": "Métadonnées", "backToEvents": "Retour aux Événements", "eventNotFound": "Événement non trouvé"}, "navigation": {"title": "Événements", "expandSidebar": "Développer la barre latérale", "sections": {"events": "Événements", "validation": "Validation"}, "allEvents": "Tous les Événements", "calendarView": "<PERSON><PERSON>", "createEvent": "Créer un Événement", "myReports": "Mes Rapports", "pendingReports": "Rapports en Attente", "eventReports": "Rapports d'Événements", "validateReport": "Valider le Rapport"}, "workflow": {"title": "Statut du Workflow", "currentStatus": "Statut Actuel :", "progress": "Progrès", "reportStatusLabel": "Statut du Rapport :", "nextActions": "Prochaines Actions :", "status": {"new": "Nouveau", "inProgress": "En Cours", "cancelled": "<PERSON><PERSON><PERSON>", "processingReport": "Traitement du Rapport", "awaitingValidation": "En Attente de Validation", "completed": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"startEvent": "Démarrer l'Événement", "beginReport": "Commencer le Rapport", "submitReport": "Soumettre le Rapport", "validateReport": "Valider le Rapport"}, "messages": {"statusUpdateSuccess": "Statut de l'événement mis à jour avec succès", "statusUpdateFailed": "Échec de la mise à jour du statut de l'événement", "reportSubmitSuccess": "Rapport soumis avec succès", "reportSubmitFailed": "Échec de la soumission du rapport", "validationFailed": "Échec de la validation", "waitingValidation": "En attente de validation par le personnel autorisé", "eventCancelled": "Cet événement a été annulé"}, "reportStatus": {"pending": "En Attente", "processing": "En Traitement", "submitted": "<PERSON><PERSON><PERSON>", "validated": "<PERSON><PERSON><PERSON>"}}, "supervisors": "Superviseurs", "overdue": "En Retard", "copyOf": "<PERSON><PERSON> de", "allSupervisors": "Tous les Superviseurs", "reportStatus": "Statut du Rapport", "allReports": "Tous les Rapports", "statuses": {"new": "Nouveau", "in_progress": "En Cours", "cancelled": "<PERSON><PERSON><PERSON>", "processing_report": "Traitement du Rapport", "awaiting_validation": "En Attente de Validation", "done": "<PERSON><PERSON><PERSON><PERSON>"}, "reportStatuses": {"pending": "En Attente", "processing": "En Traitement", "submitted": "<PERSON><PERSON><PERSON>", "validated": "<PERSON><PERSON><PERSON>", "overdue": "En Retard"}}, "reservations": {"notesAndMessages": {"title": "Notes et Messages"}, "allDates": "Toutes les dates", "title": "Réservations", "addReservation": "Ajouter une réservation", "editReservation": "Modifier la Réservation", "deleteReservation": "Supprimer la Réservation", "fetchError": "Erreur lors de la récupération des réservations", "sortByCreation": "Trier par date de création", "description": "<PERSON><PERSON>rer et consulter toutes les réservations", "showAllDates": "Affiche<PERSON> toutes les dates", "today": "Réservations créées aujourd'hui", "viewingToday": "Réservations créées aujourd'hui", "todayFetchError": "Erreur lors de la récupération des réservations d'aujourd'hui", "showDeleted": "Afficher les supprimés", "new": "Nouvelle Réservation", "selectStatus": "Sélectionner un Statut", "name": "Nom", "event": "Événement", "createdAt": "<PERSON><PERSON><PERSON>", "messages": {"title": "Messages", "empty": "Pas encore de messages. A<PERSON><PERSON><PERSON> le premier !", "addMessage": "Ajouter un message...", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer ce message ?"}, "confirmation": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Veuillez sélectionner comment le client a été contacté pour confirmation", "types": {"sms": "SMS", "call": "Appel téléphonique", "email": "Email"}}, "firstName": "Prénom", "lastName": "Nom", "branch": {"all": "Toutes les succursales", "select": "Sélectionner une succursale"}, "restaurant": "Restaurant", "eventDateTime": "Date et Heure de l'Événement", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "postalCode": "Code Postal", "deleteWarning": "Êtes-vous sûr de vouloir supprimer cette réservation ?", "reservationDeleted": "Réservation supprimée avec succès", "reservationUpdated": "Réservation mise à jour avec succès", "reservationCreated": "Réservation créée avec succès", "errorDeleting": "Échec de la suppression de la réservation", "errorSaving": "Échec de l'enregistrement de la réservation", "errorFetching": "Échec de la récupération des réservations", "status": {"label": "Statut", "pending": "En attente", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>"}, "newReservation": {"branchAndTime": {"timeSlot": "Créneau horaire", "selectDate": "Sélectionner une date", "selectTime": "Selectionner un créneau"}, "title": "Nouvelle Réservation", "selectBranch": "Sélectionner une Succursale", "selectRestaurant": "Sélectionner un Restaurant", "selectEvent": "Sélectionner un Événement", "selectBranchFirst": "Sélectionnez d'abord une succursale", "personalInfo": "Informations Personnelles", "firstName": "Prénom", "lastName": "Nom", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "postalCode": "Code Postal", "creating": "Création en cours...", "create": "Créer la Réservation", "cancel": "Annuler", "accompNumber": "Nombre d'accompagnateurs", "selectServeur": "Sélectionner un Serveur", "selectRestaurantFirst": "Sélectionnez d'abord un restaurant"}, "accompaniments": "Accompagnateurs", "notes": {"title": "Notes", "empty": "Pas encore de notes. A<PERSON><PERSON>z la première !", "addNote": "Ajouter une note...", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cette note ?", "deleteSuccess": "Note supprimée avec succès", "editSuccess": "Note mise à jour avec succès"}, "sell": {"title": "<PERSON><PERSON>", "description": "Veuillez saisir le montant de vente en CAD", "amountLabel": "<PERSON><PERSON> (CAD)", "invalidAmount": "Veuillez saisir un montant valide", "userLabel": "Assigner un Utilisateur", "noUserSelected": "Veuillez sélectionner un utilisateur"}, "confirmUser": {"title": "Assigner un Utilisateur pour Confirmation", "description": "Veuillez assigner un utilisateur avant de changer le statut à confirmé", "userLabel": "Assigner un Utilisateur", "noUserSelected": "Veuillez sélectionner un utilisateur"}, "userAssignedAndStatusChanged": "Utilisateur assigné et statut changé avec succès", "confirmUserRequired": "Un utilisateur doit être assigné avant de changer le statut à Confirmé", "filters": {"visitTime": "<PERSON><PERSON> de visite", "creationDate": "Date de création"}, "reset": "Réinitialiser", "resetAllFilters": "Réinitialiser tous les filtres", "stats": {"noUserData": "<PERSON><PERSON><PERSON> donn<PERSON> utilisateur", "noTimeSlotData": "<PERSON><PERSON>ne donn<PERSON> de créneau horaire"}, "reservationsDayTimeslot": "Réservations / Jour / Créneau", "totalReservations": "Nombre total de réservations", "statusChangedByOther": "Le statut de la réservation a été modifié par un autre utilisateur.", "initialStatus": "Statut Initial", "assignedUser": "U<PERSON><PERSON><PERSON><PERSON>", "selectUser": "Sélectionner un Utilisateur", "statsTooltip": {"title": "Statistiques détaillées", "totalSales": "Total ventes", "rendezvousAssis": "RDV assis", "totalConfirmed": "Total confirmé", "closingRate": "Taux de <PERSON>", "salesBadge": "<PERSON><PERSON><PERSON>", "presenceBadge": "RDV Assis", "confirmedBadge": "<PERSON><PERSON><PERSON><PERSON>", "closingRateBadge": "Closing", "salesExplanation": "Réservations avec statuts de vente", "presenceExplanation": "Réservations avec statuts de présence", "confirmedExplanation": "Réservations avec statuts de présence + présent + absent", "closingRateExplanation": "Taux de vente par rapport aux RDV assis (Ventes ÷ RDV Assis)"}}, "commissions": {"searchPlaceholder": "Rechercher par utilisateur, client...", "selectAll": "<PERSON>ut", "customer": "Client", "resetFilter": "Réinitialiser", "from": "<PERSON>", "to": "au", "title": "Commissions", "subtitle": "Gérer et approuver les paiements de commissions", "filters": "Filtres", "filtersDescription": "Filtrer les commissions par statut et utilisateur", "status": "Statut", "allStatuses": "Tous les Statuts", "approved": "A<PERSON><PERSON><PERSON><PERSON>", "pending": "En Attente", "applyFilters": "Appliquer les Filtres", "apply": "Appliquer", "reset": "Réinitialiser", "listCaption": "Liste des commissions", "user": "Utilisa<PERSON>ur", "reservation": "Réservation", "commissionType": "Type de Commission", "type": "Type de Commission", "amount": "<PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "actions": "Actions", "noCommissions": "Aucune commission trouvée", "noCommissionsFound": "Aucune commission trouvée", "approve": "Approuver", "approveTitle": "Approuver la Commission", "showing": "Affichage", "of": "de", "commissions": "commissions", "totalCount": "Total", "commissionsFound": "commissions trouvées", "perPage": "par page", "totalCommissions": "Total Commissions", "approveDescription": "Êtes-vous sûr de vouloir approuver cette commission de {{amount}} pour {{user}}?", "approveSuccess": "Commission approuvée avec succès", "rejectSuccess": "Commission rejetée avec succès", "error": "Échec de la mise à jour de la commission", "recipient": "Bénéficiaire", "allRecipients": "Tous les Bénéficiaires", "allTypes": "Tous les Types", "createdAt": "<PERSON><PERSON><PERSON>", "selectDate": "Sélectionner une date", "amountRange": "<PERSON><PERSON><PERSON>", "min": "Min", "max": "Max", "exactAmount": "Montant Exact", "enterAmount": "Entrer un montant", "exact": "Exact", "activeFilters": "Filtres Actifs", "viewReservation": "Voir la Réservation", "startDate": "Date de début", "endDate": "Date de fin", "totalUsers": "Total Utilisateurs", "activeUsers": "Utilisateurs actifs avec commissions", "allCommissions": "Toutes les commissions de la période", "totalAmount": "Montant Total", "allPayments": "Tous les paiements de la période", "userName": "Nom d'utilisateur", "email": "<PERSON><PERSON><PERSON>", "commissionCount": "Commissions", "noUsers": "Aucun utilisateur avec commissions trouvé", "view": "Voir les Commissions", "exportExcel": "Exporter Excel", "generatingExcel": "Génération du fichier Excel...", "excelGenerated": "Fichier Excel généré avec succès", "excelError": "Échec de génération du fichier Excel", "approvedCommissions": "Commissions Approuvées", "pendingCommissions": "Commissions en Attente", "partner": "Partenaire", "papBranch": "Succursale PAP", "recipientDescription": "Sélectionnez qui recevra cette commission", "date": "Date", "client": "Client", "topCommissionUser": "Utilisateur principal par montant de commission", "unknownUser": "Utilisateur inconnu", "count": "Nombre", "bonus": "Bonus", "syncEdits": "Synchronisé", "approveAll": "Tout approuver", "confirmApproveAllTitle": "Approuver toutes les commissions", "confirmApproveAllDesc": "Êtes-vous sûr de vouloir approuver les {{count}} commissions non approuvées actuellement chargées ? Cette action est irréversible.", "allApproved": "Toutes les commissions ont été approuvées avec succès !", "failedApproveAll": "Échec de l'approbation de toutes les commissions.", "approvingAll": "Approbation de toutes les commissions en cours...", "bulkApproveUser": "Tout approuver pour l'utilisateur", "confirmBulkApproveUserTitle": "Approuver toutes les commissions en attente pour l'utilisateur", "confirmBulkApproveUserDesc": "Êtes-vous sûr de vouloir approuver les {{count}} commissions en attente pour cet utilisateur ? Cette action est irréversible.", "fetchingCommissions": "Récupération des commissions...", "fetchingBonuses": "Récupération des bonus...", "fetchingUsers": "Récupération des détails utilisateur...", "idle": "En attente de démarrage...", "invoiceGenerationFailed": "Échec de la génération des factures", "allUsers": "Tous les utilisateurs", "usersSelected": "Utilisateurs", "searchUsers": "Rechercher des utilisateurs...", "noUsersFound": "Aucun utilisateur trouvé", "selectDay": "Sélectionner le jour", "selectColumn": "Sélectionner la colonne", "selectRow": "Sélectionner la ligne", "unsyncEdits": "Désynchroniser les modifications"}, "partners": {"createdAt": "<PERSON><PERSON><PERSON>", "eventsCount": "Événements", "title": "Entreprises", "addPartner": "Ajouter un entreprise", "editPartner": "Modifier le entreprise", "deletePartner": "Supprimer le entreprise", "deleteWarning": "Êtes-vous sûr de vouloir supprimer cette entreprise ? Cette action ne peut pas être annulée.", "partnerDeleted": "Entreprise upprimée avec succès", "partnerUpdated": "Entreprise mise à jour avec succès", "partnerCreated": "Entreprise créée avec succès", "errorDeleting": "Échec de la suppression de l'entreprise", "errorSaving": "Échec de l'enregistrement de l'entreprise", "errorFetching": "Échec de la récupération des entreprises", "errorLoadingBranches": "Échec du chargement des succursales", "name": "Nom de l'entreprise", "namePlaceholder": "Entrez le nom de l'entreprise", "abbreviatedName": "Nom a<PERSON>", "abbreviatedNamePlaceholder": "Entrez le nom abrégé", "address": "<PERSON><PERSON><PERSON>", "addressPlaceholder": "Entrez l'adresse", "local": "Local", "localPlaceholder": "Entrez le local (optionnel)", "city": "Ville", "cityPlaceholder": "Entrez la ville", "province": "Province", "selectProvince": "Sélectionnez une province", "country": "Pays", "countryPlaceholder": "Entrez le pays", "postalCode": "Code postal", "postalCodePlaceholder": "Entrez le code postal", "phone": "Téléphone", "phonePlaceholder": "Entrez le numéro de téléphone", "fax": "Fax", "faxPlaceholder": "Entrez le numéro de fax (optionnel)", "internalPhone": "Téléphone interne", "internalPhonePlaceholder": "Entrez le téléphone interne (optionnel)", "internationalFax": "Fax international", "internationalFaxPlaceholder": "Entrez le fax international (optionnel)", "email": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "Entrez l'adresse courriel", "website": "Site web", "websitePlaceholder": "Entrez l'URL du site web (optionnel)", "branch": "Succursale", "selectBranch": "Sélectionnez une succursale", "selectPartner": "Sélectionner un partenaire", "divisionId": "ID de division", "divisionIdPlaceholder": "Entrez l'ID de division", "language": "<PERSON><PERSON>", "selectLanguage": "Sélectionnez une langue", "french": "Français", "english": "<PERSON><PERSON><PERSON>", "federalTax": "Taxe fédérale", "federalTaxPlaceholder": "Entrez le taux de taxe fédérale", "provincialTax": "Taxe provinciale", "provincialTaxPlaceholder": "Entrez le taux de taxe provinciale", "lastModified": "Dernière modification", "notModifiedYet": "Pas encore modifié", "companyLogo": "Logo de l'entreprise", "uploadLogo": "Téléverser le logo", "uploading": "Téléversement en cours...", "uploadSuccess": "Logo téléversé avec succès", "uploadError": "Échec du téléversement du logo", "tabs": {"general": "Informations générales", "contact": "Coordonnées", "fiscal": "Informations fiscales", "additional": "Informations supplémentaires"}, "owners": "<PERSON>p<PERSON><PERSON><PERSON><PERSON>", "agents": "Agents", "errorLoadingUsers": "Échec du chargement des utilisateurs", "errorUpdatingUsers": "Échec de la mise à jour des utilisateurs du partenaire", "usersUpdated": "Utilisateurs du partenaire mis à jour avec succès", "users": "Utilisateurs", "searchPlaceholder": "Rechercher par nom, adresse, téléphone, email...", "allBranches": "Toutes les succursales", "noPartners": "Aucun partenaire trouvé", "generalTab": "Général", "contactTab": "Contact", "brandingTab": "Image de marque", "logoUploading": "<PERSON><PERSON><PERSON><PERSON>, le logo est en cours de téléchargement", "urlSlug": "Identifiant URL", "urlSlugPlaceholder": "ex: mon-entreprise-partenaire", "urlSlugHelp": "Ceci sera utilisé dans les URLs comme .../login/[identifiant] pour les pages de connexion spécifiques aux partenaires. Seules les lettres minuscules, les chiffres et les tirets sont autorisés.", "loginHtml": "HTML de Connexion Personnalisé", "loginHtmlPlaceholder": "Entrez le HTML personnalisé pour la page de connexion...", "loginHtmlHelp": "Contenu HTML personnalisé pour personnaliser l'apparence de la page /login/[identifiant]. Utilisez les variables de template pour positionner les éléments de connexion.", "templateVariables": "Variables de Template", "templateVariablesDescription": "Utilisez ces variables pour positionner et personnaliser les éléments de connexion dans votre template HTML :", "requiredVariables": "Variables Obligatoires", "optionalVariables": "Variables Optionnelles", "emailInputDescription": "Champ de saisie email (obligatoire)", "passwordInputDescription": "Champ de saisie mot de passe (obligatoire)", "submitButtonDescription": "Bouton de soumission du formulaire (obligatoire)", "partnerNameDescription": "Nom du partenaire", "partnerLogoDescription": "Logo du partenaire (si configuré)", "errorMessageDescription": "Zone d'affichage des erreurs de connexion", "stylingOptions": "Options de Style", "stylingOptionsDescription": "Vous pouvez utiliser du CSS inline ou des classes CSS. Les variables seront remplacées par les éléments fonctionnels appropriés.", "exampleTemplate": "Exemple de Template", "invalidFileType": "Type de fichier invalide. Seules les images JPEG, PNG et WebP sont autorisées.", "fileTooLarge": "Fichier trop volumineux. La taille maximale est de 5 Mo.", "eventsFor": "Événements pour"}, "regions": {"title": "Régions", "addRegion": "Ajouter une région", "editRegion": "Modifier la région", "deleteRegion": "Supprimer la région", "name": "Nom", "code": "Code", "actions": "Actions", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cette région ?", "deleteWarning": "Cette action ne peut pas être annulée.", "search": "Rechercher des régions...", "noRegions": "Aucune région trouvée", "regionCreated": "Région créée avec succès", "regionUpdated": "Région mise à jour avec succès", "regionDeleted": "Région supprimée avec succès", "errorCreating": "Erreur lors de la création de la région", "errorUpdating": "Erreur lors de la mise à jour de la région", "errorDeleting": "Erreur lors de la suppression de la région", "namePlaceholder": "Nom de la région", "codePlaceholder": "Code de la région (ex. QC)", "codeHelp": "Doit être exactement 2 lettres majuscules"}, "appointmentsDisponibilities": {"title": "Disponibilités des Rendez-vous", "users": "Utilisateurs", "selectUser": "Sélectionner un utilisateur", "previousWeek": "<PERSON><PERSON><PERSON>", "nextWeek": "<PERSON><PERSON><PERSON>", "selectAllWeek": "Sélectionner toute la semaine", "deselectAllWeek": "Désélectionner toute la semaine", "saveAll": "Enregistrer toutes les disponibilités", "saving": "Enregistrement...", "selectAll": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON>", "noAppointments": "Pas de rendez-vous", "max": "Max", "userBranches": "Succursales de l'utilisateur"}, "allergies": {"title": "Allergies", "list": "Liste des allergies", "addAllergy": "Ajouter une allergie", "editAllergy": "Modifier l'allergie", "deleteAllergy": "Supprimer l'allergie", "name": "Nom", "name_en": "<PERSON><PERSON>", "actions": "Actions", "noAllergies": "Aucune allergie trouvée", "loading": "Chargement des allergies...", "error": "Erreur lors du chargement des allergies", "createSuccess": "Allergie créée avec succès", "updateSuccess": "Allergie mise à jour avec succès", "deleteSuccess": "Allergie supprimée avec succès", "saveError": "Échec de l'enregistrement de l'allergie", "deleteError": "Échec de la suppression de l'allergie", "deleteWarning": "Êtes-vous sûr de vouloir supprimer cette allergie ? Cette action ne peut pas être annulée.", "errorLoading": "Erreur lors du chargement des allergies", "confirmDelete": "Confirmer la <PERSON>", "confirmDeleteText": "Êtes-vous sûr de vouloir supprimer cette allergie ? ", "update": "Mettre à jour", "add": "Ajouter", "retry": "<PERSON><PERSON><PERSON><PERSON>"}, "reservationStatuses": {"isSalesStatus": "Statut de Vente", "isPresenceStatus": "Statut de Présence", "isRecontactStatus": "Statut de Recontact", "title": "Statuts de Réservation", "addStatus": "Ajouter un Statut", "editStatus": "Modifier le Statut", "deleteStatus": "Supprimer le Statut", "name": "Nom", "name_en": "<PERSON><PERSON>", "code": "Code", "color": "<PERSON><PERSON><PERSON>", "order": "Ordre d'Affichage", "excludeFromAffectations": "Exclure des Affectations/Rendez-vous", "excludeFromAffectationsDescription": "Lorsque activé, les réservations avec ce statut n'apparaîtront pas dans les pages d'affectations et de rendez-vous", "affectationsColumn": "Exclu des Affectations", "actions": "Actions", "noStatuses": "Aucun statut trouvé", "loading": "Chargement des statuts...", "error": "Erreur lors du chargement des statuts", "createSuccess": "Statut créé avec succès", "updateSuccess": "Statut mis à jour avec succès", "deleteSuccess": "Statut supprimé avec succès", "saveError": "Échec de l'enregistrement du statut", "deleteError": "Échec de la suppression du statut", "deleteWarning": "Êtes-vous sûr de vouloir supprimer ce statut? Ce<PERSON> pourrait affecter les réservations existantes."}, "foods": {"title": "Aliments", "list": "Liste des aliments", "addFood": "Ajouter un aliment", "editFood": "Modifier l'aliment", "deleteFood": "Supprimer l'aliment", "name": "Nom", "name_en": "Nom anglais", "actions": "Actions", "noFoods": "Aucun aliment trouvé", "loading": "Chargement des aliments...", "error": "Erreur lors du chargement des aliments", "createSuccess": "<PERSON><PERSON> c<PERSON> avec succès", "updateSuccess": "Aliment mis à jour avec succès", "deleteSuccess": "Aliment supprimé avec succès", "saveError": "Échec de l'enregistrement de l'aliment", "deleteError": "Échec de la suppression de l'aliment", "deleteWarning": "Êtes-vous sûr de vouloir supprimer cet aliment ? Cette action ne peut pas être annulée."}, "invoice-signing": {"title": "Signature de Facture", "loading": "Chargement...", "loadingInvoice": "Chargement de la facture...", "loadingInvoiceData": "Chargement des données de la facture...", "error": "<PERSON><PERSON><PERSON>", "invalidLink": "Lien Invalide", "invalidLinkMessage": "Ce lien de signature de facture est invalide ou a expiré.", "linkExpired": "Ce lien a expiré et n'est plus valide pour la signature.", "linkExpiryNotice": "Ce lien a été ouvert pour la première fois le {date} et expirera dans {time}.", "signedSuccessfully": "Facture Signée avec Succès", "signedSuccessfullyMessage": "Merci d'avoir signé la facture. Cette facture a été signée le {date}.", "yourSignature": "Votre Signature :", "downloadSignedInvoice": "Télécharger la Facture Signée", "downloading": "Téléchargement...", "signYourInvoice": "<PERSON><PERSON> V<PERSON>re F<PERSON>", "reviewAndSignMessage": "Veuillez examiner la facture et signer dans la zone désignée ci-dessous. Une fois signée, vous pourrez télécharger une copie de la facture signée.", "cancel": "Annuler", "signAndSubmit": "Signer et Soumettre", "signing": "Signature en cours...", "downloadSignedPdf": "Télécharger le PDF Signé", "confirmSignature": "Confirmer la Signature", "confirmSignatureMessage": "Êtes-vous sûr de vouloir signer cette facture ? Cette action ne peut pas être annulée.", "processing": "Traitement en cours...", "drawSignatureHere": "Dessinez votre signature ici", "clear": "<PERSON><PERSON><PERSON><PERSON>", "noInvoiceData": "Aucune donnée de facture disponible", "noInvoiceDataMessage": "Impossible de charger les détails de la facture. Veuillez réessayer plus tard.", "missingSignature": "Signature Manquante", "missingSignatureMessage": "Veuillez dessiner votre signature avant de soumettre", "success": "Su<PERSON>ès", "signatureSubmittedSuccessfully": "Signature soumise avec succès", "pdfDownloadedSuccessfully": "PDF téléchargé avec succès", "errorSubmittingSignature": "Une erreur s'est produite lors de la soumission de la signature", "errorGeneratingPdf": "Une erreur s'est produite lors de la génération du PDF", "errorFetchingInvoiceData": "Une erreur s'est produite lors de la récupération des données de la facture", "errorValidatingToken": "Une erreur s'est produite lors de la validation du token", "invoiceSignedSuccessfully": "Facture signée avec succès", "failedToValidateToken": "Échec de la validation du token", "failedToSubmitSignature": "Échec de la soumission de la signature", "failedToGeneratePdf": "Échec de la génération du PDF", "failedToFetchInvoiceData": "Échec de la récupération des données de la facture", "invoiceDataNotAvailable": "Données de facture non disponibles", "debugInfo": "Informations de Débogage", "invoice": "FACTURE", "issuer": "<PERSON><PERSON><PERSON>", "client": "Client", "billingDetails": "Détails de facturation", "description": "Description", "date": "Date", "quantity": "Qté", "unitPrice": "Prix Uni.", "price": "Prix", "subtotal": "Sous-total", "total": "Total", "signature": "Signature:", "signHere": "<PERSON><PERSON> ici", "tel": "Tél:"}, "general": {"create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "save": "Enregistrer", "cancel": "Annuler", "confirm": "Confirmer", "saving": "Enregistrement...", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "published": "<PERSON><PERSON><PERSON>", "draft": "Brouillon", "featured": "En vedette", "all": "Tous", "none": "Aucun", "yes": "O<PERSON>", "no": "Non", "noPermission": "Vous n'avez pas la permission d'accéder à cette ressource", "translateAll": "Tout traduire", "accessDenied": "<PERSON><PERSON>ès refusé", "accessDeniedMessage": "Vous n'avez pas la permission d'accéder à cette page. Veuillez contacter votre administrateur si vous pensez qu'il s'agit d'une erreur.", "goBack": "Retour", "returnHome": "Retour à l'accueil", "actions": "Actions", "showing": "Affichage", "of": "de", "perPage": "par page", "columns": "Colonnes", "pickADate": "Choisir une plage de dates", "hide": "Masquer", "show": "<PERSON><PERSON><PERSON><PERSON>", "saveChanges": "Enregistrer les modifications", "reset": "Réinitialiser la semaine", "copy": "Copier sur 3 semaines", "copyTo3Weeks": "Enregistrer Actuel & Copier sur 3 semaines", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "warning": "Avertissement", "id": "ID", "capacity": "Capacité", "unknown": "inconnu", "na": "n/d", "created": "c<PERSON><PERSON>", "updated": "mis à jour", "deleted": "supprimé", "next": "Suivant", "previous": "Précédent", "back": "Retour", "openMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "somethingWentWrong": "Une erreur est survenue", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "loading": "Chargement...", "reloading": "Rechargement...", "showAllDates": "Affiche<PERSON> toutes les dates", "dateRange": "Plage de dates", "today": "<PERSON><PERSON><PERSON>'hui", "branch": "Succursale", "selectBranch": "Sélectionner une succursale", "name": "Nom", "email": "Email", "phone": "Téléphone", "phoneNumber": "Numéro de téléphone", "password": "Mot de passe", "permissions": "Permissions", "newPasswordOptional": "Nouveau mot de passe (Optionnel)"}, "calendar": {"days": {"mon": "<PERSON>n", "tue": "Mar", "wed": "<PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "Ven", "sat": "Sam", "sun": "<PERSON><PERSON>", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>"}, "months": {"jan": "Jan", "feb": "Fév", "mar": "Mar", "apr": "Avr", "may": "<PERSON>", "jun": "Juin", "jul": "<PERSON><PERSON>", "aug": "Août", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Déc", "january": "<PERSON><PERSON>", "february": "<PERSON><PERSON><PERSON><PERSON>", "march": "Mars", "april": "Avril", "june": "Juin", "july": "<PERSON><PERSON><PERSON>", "august": "Août", "september": "Septembre", "october": "Octobre", "november": "Novembre", "december": "Décembre"}, "selectDate": "Sélectionner une date", "availabilityWarning": {"title": "Exigence de Disponibilité Mensuelle Non Satisfaite", "description": "Vous devez avoir des disponibilités pour au moins 3 semaines dans le mois en cours. Veuillez ajouter plus de plages horaires."}, "monthlyRequirement": "Exigence de Disponibilité Mensuelle", "weeksWithAvailability": "semaines avec disponibilité", "editingDisabled": "Modification Désactivée", "monthlyRequirementSaved": "L'exigence de disponibilité mensuelle a été satisfaite et enregistrée. Seuls les administrateurs peuvent modifier.", "filled": "<PERSON><PERSON><PERSON><PERSON> rempli", "canEditFutureDays": "Vous pouvez modifier les jours futurs qui n'ont pas encore été remplis", "dateAlreadyFilled": "Cette date a déjà une disponibilité et ne peut pas être modifiée", "weeksRequirementNotMet": "V<PERSON> devez fournir une disponibilité pour la semaine en cours et les 2 semaines à venir", "weeksRequired": "3 semaines requises", "updated": "Calendrier Mis à Jour", "availabilitySaved": "Votre disponibilité a été enregistrée", "weekReset": "<PERSON><PERSON><PERSON>", "weekSlotsCleared": "Les créneaux de la semaine en cours ont été effacés. Cliquez sur Enregistrer les modifications pour appliquer.", "saveFailed": "Échec de l'enregistrement de votre disponibilité", "resetFailed": "Échec de la réinitialisation des créneaux de la semaine en cours", "past": "Date passée"}, "appointments": {"modal": {"title": "<PERSON><PERSON><PERSON> les créneaux de rendez-vous", "loadingSlots": "Chargement des créneaux de rendez-vous...", "quickTemplates": "Mod<PERSON>les rapides", "addNewSlot": "Ajouter un nouveau créneau", "startTime": "<PERSON><PERSON> d<PERSON>", "selectStartTime": "Sélectionner l'heure de début", "duration": "<PERSON><PERSON><PERSON>", "selectDuration": "Sélectionner la durée", "oneHour": "1 heure", "twoHours": "2 heures", "addSlot": "Ajouter un créneau", "defaultValues": "Valeurs par défaut", "defaultCapacity": "Capacité par défaut", "defaultOnline": "En ligne par défaut", "defaultHome": "À domicile par défaut", "maxFamilySize": "Taille de famille maximale", "applyToAllSlots": "Appliquer à tous les créneaux", "timeline": "Chronologie", "clearAll": "Tout effacer"}, "templates": {"morning1h": "Créneaux matinaux 1h (11h-12h)", "afternoon1h": "Créneaux d'après-midi 1h (13h-17h)", "evening1h": "Créneaux du soir 1h (17h-21h)", "fullDay1h": "Créneaux journée complète 1h (11h-21h)", "fullDay2hEven": "<PERSON><PERSON><PERSON> complète 2h pairs (12h-14h, 14h-16h...)", "fullDay2hOdd": "<PERSON>urn<PERSON> complète 2h impairs (11h-13h, 13h-15h...)", "pairHours": "<PERSON><PERSON><PERSON> complète 2h pairs", "oddHours": "<PERSON><PERSON><PERSON> complète 2h impairs"}, "deleteDialog": {"confirmDeletion": "Confirmer la <PERSON>", "deleteWarningSingular": "Cette action supprimerait 1 créneau de rendez-vous existant", "deleteWarningPlural": "Cette action supprimerait des créneaux de rendez-vous existants", "affectExisting": "La suppression de créneaux peut affecter les rendez-vous existants. Cette action ne peut pas être annulée.", "slotsToDelete": "Créneaux qui seraient supprimés", "timeRange": "<PERSON>lage horaire", "noDetailedInfo": "Aucune information détaillée disponible sur les créneaux", "differentTimeRanges": "Les créneaux avec des plages horaires différentes seraient supprimés. Cela peut affecter les réservations existantes pour ces créneaux.", "confirmDelete": "<PERSON><PERSON>, supprimer les créneaux", "blockedDueToReservations": "Impossible de supprimer ces créneaux car des réservations existent pour eux. Veuillez d'abord gérer ou déplacer les réservations concernées.", "safeToDelete": "Ces créneaux n'ont aucune réservation et peuvent être supprimés en toute sécurité.", "hasReservations": "Réservations existantes", "capacityBelowReservationsWarning": "Certains créneaux ont une nouvelle capacité inférieure au nombre de réservations existantes. Cela peut entraîner une surréservation. Veuillez vérifier.", "oldCapacity": "Ancienne capacité", "newCapacity": "Nouvelle capacité", "reservationCount": "Réservations"}, "errors": {"failedToCheckDeletedSlots": "Échec de la vérification des créneaux supprimés", "checkingDeletedSlots": "Erreur lors de la vérification des créneaux supprimés", "noDaySelected": "Aucun jour sélectionné", "inHandleSubmit": "Erreur dans handleSubmit", "occurred": "Une erreur s'est produite", "failedToUpdate": "Échec de la mise à jour des créneaux", "updatingSlots": "E<PERSON>ur lors de la mise à jour des créneaux", "invalidStartTimeOrDuration": "Veuillez entrer une heure de début et une durée valides", "invalidTimeFormat": "Veuillez entrer une heure valide au format HH:MM", "overlappingSlot": "Ce créneau chevauche un créneau existant", "slotBeforeElevenAM": "La création de créneaux n'est pas autorisée avant 11h00", "slotAfterNinePM": "La création de créneaux n'est pas autorisée après 21h00"}, "logs": {"slotsWillBeDeleted": "Les créneaux seront supprimés si vous continuez", "handleSubmitResult": "Résultat de la soumission", "submittingChanges": "Soumission des modifications...", "updatingSlots": "Mise à jour des créneaux pour", "operationComplete": "Opération terminée", "submissionResult": "Résultat de la soumission"}, "success": {"slotsUpdated": "Créneaux mis à jour avec succès"}, "sellers": {"present": "Présents", "partiallyAvailable": "Partiellement disponibles", "absent": "Absents"}}, "newreservation": {"timeslotFull": "Complet", "timeslotFullWarning": "Ce créneau est complet. V<PERSON> pouvez continuer, mais la capacité a été atteinte.", "title": "Nouvelle Réservation", "description": "C<PERSON>ez une nouvelle réservation en remplissant les détails ci-dessous.", "buttons": {"create": "Créer la Réservation", "creating": "Création en cours...", "cancel": "Annuler"}, "clientInformation": {"title": "Informations Client", "description": "Entrez les informations personnelles du client.", "firstName": "Invité 1", "secondName": "Invité 2", "secondNameOptional": "Invité 2", "email": "Email", "phone": "Téléphone", "alternativePhone": "Téléphone Alternatif", "postalCode": "Code Postal", "invalidPostalCode": "Code postal invalide", "city": "Ville", "address": "<PERSON><PERSON><PERSON>", "mainClient": "Client Principal"}, "preferences": {"title": "Préférences", "description": "Préférences du client pour sa visite.", "preferredLanguage": "<PERSON>ue Préféré<PERSON>", "serviceTypes": {"title": "Sélection du Type de Service", "description": "Sélectionnez le type de service pour chaque personne présente (optionnel)."}, "adultServiceType": "Type de Service Adulte", "childServiceType": "Type de Service Enfant", "selectServiceType": "Sélectionner un type de service", "allergies": {"title": "Allergies", "searchPlaceholder": "Rechercher des allergies...", "placeholder": "Entrez vos allergies ici...", "description": "Décrivez les allergies du client."}, "children": {"title": "<PERSON><PERSON><PERSON>", "description": "Des enfants seront-ils présents lors de la visite?", "agesTitle": "Groupes d'âge", "age0to5": "Ages 0-5", "age6to12": "Ages 6-12", "age13to17": "Ages 13-17", "childNumber": "Enfant {0}", "teenNumber": "Adolescent {0}", "years": "ans"}}, "notes": {"title": "Notes", "description": "Ajoutez des notes internes sur cette réservation.", "placeholder": "Entrez vos notes ici..."}, "branchAndTime": {"timeSlot": "Créneau horaire", "noBranches": "Aucune succursale trouvée", "pickDate": "Choisir une date", "selectDate": "Sélectionner une date", "selectTime": "Selectionner un créneau", "title": "Succursale et Heure de Visite", "description": "Sélectionnez la succursale et l'heure de visite préférées.", "branch": "Succursale", "date": "Date", "time": "<PERSON><PERSON>", "searchBranches": "Rechercher des succursales...", "distance": "{0} km", "selectBranch": "Sélectionner la succursale"}, "partnerAssignment": {"title": "Agent", "description": "Attribuez cette réservation à un agent.", "assignedUser": "Agent", "selectUser": "Sélectionner un Agent"}}, "editReservation": {"title": "Modifier la réservation", "description": "<PERSON><PERSON>z à jour les détails de cette réservation.", "buttons": {"cancel": "Annuler", "save": "Enregistrer les modifications", "saving": "Enregistrement..."}}, "branchesAdmin": {"title": "Administrateur de succursale", "form": {"steps": {"generalInfo": "Informations générales", "taxInfo": "Informations fiscales", "documents": "Documents", "permissions": "Permissions"}, "fields": {"name": "Nom", "nameTooltip": "Entrez le nom complet de l'administrateur de succursale", "email": "Email", "password": "Mot de passe", "newPassword": "Nouveau mot de passe (laissez vide pour conserver l'actuel)", "passwordHelp": "Laissez le champ de mot de passe vide pour conserver le mot de passe actuel.", "branch": "Succursale", "phone": "Téléphone", "taxable": "Taxable", "taxableTooltip": "Indiquez si cette succursale est assujettie à la taxation", "tvq": "TVQ", "tvqTooltip": "Entrez le pourcentage de TVQ (0-100)", "tps": "TPS", "taxationType": "Type de taxation", "checkSpecimen": "Spécimen de chèque", "checkSpecimenTooltip": "Téléchargez un spécimen de chèque pour les paiements", "otherDocuments": "Autres documents justificatifs", "otherDocumentsTooltip": "Ajoutez d'autres documents pertinents"}, "validation": {"nameRequired": "Le nom doit comporter au moins 2 caractères", "emailInvalid": "<PERSON><PERSON><PERSON> email invalide", "passwordLength": "Le mot de passe doit comporter au moins 6 caractères", "branchRequired": "La succursale est requise", "phoneRequired": "Le numéro de téléphone doit comporter au moins 8 caractères", "tvqRange": "La TVQ doit être comprise entre 0 et 100", "tpsRange": "La TPS doit être comprise entre 0 et 100", "taxFieldsRequired": "Lorsque taxable est défini sur 'oui', des valeurs valides pour TVQ, TPS et type de taxation sont requises"}, "buttons": {"next": "Suivant", "previous": "Précédent", "create": "Créer l'administrateur de succursale", "update": "Mettre à jour l'administrateur de succursale"}, "messages": {"validationFailed": "Veuillez vérifier tous les champs et réessayer.", "stepValidationFailed": "<PERSON><PERSON><PERSON>z remplir tous les champs obligatoires avant de continuer.", "successCreate": "Administrateur de succursale créé avec succès", "successUpdate": "Administrateur de succursale mis à jour avec succès", "error": "Échec de l'enregistrement de l'administrateur de succursale"}}, "taxationTypes": {"TPS_TVQ": "TPS/TVQ", "TPS_ONLY": "TPS seulement", "TVQ_ONLY": "TVQ seulement", "NO_TAX": "Non taxable"}}, "branchesAgent": {"title": "Agent de succursale", "form": {"fields": {"name": "Nom", "email": "Email", "phoneNumber": "Numéro de téléphone", "password": "Mot de passe", "newPassword": "Nouveau mot de passe (laissez vide pour conserver l'actuel)", "passwordHelp": "Laissez le champ de mot de passe vide pour conserver le mot de passe actuel.", "branch": "Succursale"}, "validation": {"nameRequired": "Le nom doit comporter au moins 2 caractères", "emailInvalid": "<PERSON><PERSON><PERSON> email invalide", "passwordLength": "Le mot de passe doit comporter au moins 6 caractères", "branchRequired": "La succursale est requise", "phoneRequired": "Le numéro de téléphone est requis"}, "buttons": {"create": "Créer l'agent de succursale", "update": "Mettre à jour l'agent de succursale"}, "messages": {"successCreate": "Agent de succursale créé avec succès", "successUpdate": "Agent de succursale mis à jour avec succès", "error": "Échec de l'enregistrement de l'agent de succursale"}}}, "partner": {"title": "Partenaire", "form": {"fields": {"name": "Nom", "email": "Email", "password": "Mot de passe", "newPassword": "Nouveau mot de passe", "passwordHelp": "Laissez vide pour conserver le mot de passe actuel", "companyName": "Nom de l'entreprise", "phone": "Téléphone"}, "validation": {"nameRequired": "Le nom doit comporter au moins 2 caractères", "emailInvalid": "<PERSON><PERSON><PERSON> email invalide", "passwordLength": "Le mot de passe doit comporter au moins 6 caractères", "companyNameRequired": "Le nom de l'entreprise est requis", "phoneRequired": "Le numéro de téléphone doit comporter au moins 8 caractères"}, "buttons": {"create": "Créer un partenaire", "update": "Mettre à jour le partenaire"}, "messages": {"successCreate": "Partenaire créé avec succès", "successUpdate": "Partenaire mis à jour avec succès", "error": "Échec de l'enregistrement du partenaire"}}}, "agentPartner": {"title": "Agent de Partenaire", "form": {"fields": {"name": "Nom", "email": "Email", "password": "Mot de passe", "newPassword": "Nouveau mot de passe", "passwordHelp": "Laissez vide pour conserver le mot de passe actuel", "partner": "Partenaire", "phone": "Téléphone"}, "validation": {"nameRequired": "Le nom doit comporter au moins 2 caractères", "emailInvalid": "<PERSON><PERSON><PERSON> email invalide", "passwordLength": "Le mot de passe doit comporter au moins 6 caractères", "partnerRequired": "Le partenaire est requis", "phoneRequired": "Le numéro de téléphone doit comporter au moins 8 caractères"}, "buttons": {"create": "Créer un agent partenaire", "update": "Mettre à jour l'agent partenaire"}, "messages": {"successCreate": "Agent partenaire c<PERSON><PERSON> avec succès", "successUpdate": "Agent partenaire mis à jour avec succès", "error": "Échec de la gestion de l'agent partenaire"}}}, "seller": {"title": "<PERSON><PERSON><PERSON>", "calendar": {"availabilityStatus": "Statut de Disponibilité", "availabilityComplete": "Disponibilité Complète", "availabilityIncomplete": "Disponibilité Incomplète", "weeksFilledCount": "Semaines remplies", "weeksRequired": "3 semaines requises", "monthlyRequirementDescription": "V<PERSON> devez fournir des disponibilités pour 3 semaines", "requirementMet": "Exigence satisfaite ! Vous avez rempli 3 semaines de disponibilité", "consecutiveWeeksRequired": "<PERSON><PERSON> <PERSON><PERSON> remplir les disponibilités pour la semaine en cours et les 2 semaines à venir"}, "form": {"fields": {"name": "Nom", "email": "Email", "phoneNumber": "Numéro de téléphone", "password": "Mot de passe", "newPassword": "Nouveau mot de passe", "passwordHelp": "Laissez vide pour conserver le mot de passe actuel", "branch": "Succursale", "selectBranch": "Sélectionner une succursale"}, "validation": {"nameRequired": "Le nom doit comporter au moins 2 caractères", "emailInvalid": "<PERSON><PERSON><PERSON> email invalide", "passwordLength": "Le mot de passe doit comporter au moins 6 caractères", "phoneRequired": "Le numéro de téléphone est requis"}, "buttons": {"create": "<PERSON><PERSON><PERSON> un vendeur", "update": "Mettre à jour le vendeur"}, "messages": {"successCreate": "Vendeur c<PERSON><PERSON> avec succès", "successUpdate": "Vendeur mis à jour avec succès", "error": "Échec de la gestion du vendeur"}}}, "roles": {"pap": "PAP", "searchPermissions": "Rechercher des permissions...", "noPermissionsFound": "Aucune permission trouvée.", "title": "<PERSON><PERSON><PERSON>", "addRole": "Ajouter un rôle", "editRole": "Modifier le rôle", "deleteRole": "<PERSON><PERSON><PERSON><PERSON> le rôle", "addNewRole": "Ajouter un nouveau rôle", "name": "Nom", "description": "<PERSON><PERSON><PERSON> les rôles et leurs permissions", "descriptionPlaceholder": "Description du rôle", "namePlaceholder": "Administrateur", "noRoles": "<PERSON><PERSON><PERSON> rôle trouvé", "updateRole": "Mettre à jour le rôle", "createRole": "<PERSON><PERSON>er un rôle", "searchRoles": "Rechercher des rôles...", "deleteConfirm": "Êtes-vous absolument sûr ?", "deleteWarning": "Cette action ne peut pas être annulée. Cela supprimera définitivement le rôle et le retirera de tous les utilisateurs qui l'ont.", "roleCreated": "Rôle c<PERSON>é avec succès", "roleUpdated": "Rôle mis à jour avec succès", "roleDeleted": "Rôle supprimé avec succès", "errorSaving": "Échec de l'enregistrement du rôle", "errorDeleting": "Échec de la suppression du rôle", "errorFetching": "Échec de la récupération des rôles", "errorFetchingPermissions": "Échec de la récupération des permissions", "validation": {"nameRequired": "Le nom est obligatoire", "permissionsRequired": "Sélectionnez au moins une permission"}, "responsible": "Administrateur", "agent": "Agent", "superAdmin": "Super Administrateur", "branchesAdmin": "Directeur <PERSON><PERSON><PERSON><PERSON>", "branchesAgent": "Agent de Succursales", "partner": "Partenaire", "agentPartner": "Agent <PERSON><PERSON><PERSON>", "seller": "<PERSON><PERSON><PERSON>", "telephoniste": "Téléphoniste", "hiddenRole": "<PERSON><PERSON><PERSON> caché"}, "permissions": {"title": "Permissions", "addPermission": "Ajouter une permission", "editPermission": "Modifier la permission", "deletePermission": "Supprimer la permission", "addNewPermission": "Ajouter une nouvelle permission", "name": "Nom", "code": "Code", "description": "Description", "descriptionPlaceholder": "Permet de voir la liste des utilisateurs", "namePlaceholder": "Voir les utilisateurs", "codePlaceholder": "VOIR_UTILISATEURS", "noPermissions": "Aucune <PERSON> trouvée", "updatePermission": "Mettre à jour la permission", "createPermission": "<PERSON><PERSON><PERSON> une permission", "searchPermissions": "Rechercher des permissions...", "deleteConfirm": "Êtes-vous absolument sûr ?", "deleteWarning": "Cette action ne peut pas être annulée. Cela supprimera définitivement la permission et la retirera de tous les rôles qui l'utilisent.", "permissionCreated": "Permission créée avec succès", "permissionUpdated": "Permission mise à jour avec succès", "permissionDeleted": "Permission supprimée avec succès", "errorSaving": "Échec de l'enregistrement de la permission", "errorDeleting": "Échec de la suppression de la permission", "errorFetching": "Échec de la récupération des permissions", "validation": {"nameRequired": "Le nom est obligatoire", "codeRequired": "Le code est obligatoire"}}, "dashboard": {"userCreationStats": "Statistiques de Performance PAP (Date de creation)", "home": "Accueil", "title": "Tableau <PERSON>", "availableDashboards": "Tableaux de Bord Disponibles", "adminDashboard": "Tableau de <PERSON>", "adminDashboardDescription": "<PERSON><PERSON><PERSON> les utilisateurs, les succursales et accéder aux paramètres du système", "sellerDashboard": "<PERSON><PERSON> <PERSON>", "sellerDashboardDescription": "Consultez vos ventes, rendez-vous et indicateurs de performance", "papDashboard": "Tableau de Bord PAP", "papDashboardDescription": "Gérer les fonctions spécifiques PAP et consulter les performances", "reservations": "Réservations", "reservationsDescription": "<PERSON><PERSON><PERSON> les réservations et les rendez-vous clients", "eventReportValidation": "Validation des Rapports d'Événements", "eventReportValidationDescription": "Examiner et valider les rapports d'événements soumis", "emptyDashboard": "Aucun tableau de bord disponible pour votre rôle", "dateRange": "Plage de <PERSON>s", "tabs": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "analytics": "Analytiques", "performance": "Performance", "management": "Gestion"}, "user": "Utilisa<PERSON>ur", "unassigned": "Non assigné", "sales": "<PERSON><PERSON><PERSON>", "salesRate": "<PERSON><PERSON>", "presence": "Présence", "presenceCount": "Nombre de Présences", "presenceRate": "<PERSON><PERSON> Présence", "salesToPresenceRate": "Taux de Vente sur Présence", "conversions": "Conversions", "conversionRate": "Taux de Conversion", "totalReservations": "Total des Réservations", "totalSales": "Total des Ventes", "cancelledSales": "<PERSON><PERSON><PERSON>", "averageSale": "Vente Moyenne", "todayReservations": "Réservations créées aujourd'hui", "userPerformanceStats": "Statistiques de Performance PAP", "assignedUserPerformanceStats": "Performance des Vendeur", "bestPerformingUser": "Meilleur PAP", "bestPerformingAssignedUser": "<PERSON><PERSON><PERSON>", "branchLegend": "Légende des Succursales", "debug": "Débogage", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "dailyReservationsChart": "Réservations Quotidiennes", "reservationsDayTimeslot": "Réservations par Jour et Créneau Horaire", "totalCommissions": "Total des Commissions", "totalCommissionAmount": "Montant Total des Commissions", "topCommissionUser": "Mei<PERSON>ur Utilisateur en Commission", "commissionStats": "Statistiques de Commissions", "weekdayDistribution": "Distribution par Jour de la Semaine", "dailyCreationStats": "Statistiques de Création Quotidienne", "dataType": "Type de Données", "creationDate": "Date de Création", "visitDate": "Date de Visite", "groupBy": "Grouper <PERSON><PERSON>", "groupByDay": "Jour", "groupByWeek": "<PERSON><PERSON><PERSON>", "groupByMonth": "<PERSON><PERSON>", "creationsWithCreationDate": "Réservations par date de création", "creationsWithVisitDate": "Réservations par date de visite", "creationDateTotal": "Total (date de création)", "visitDateTotal": "Total (date de visite)", "matchesSummaryCards": "Correspond aux cartes sommaires", "selectBranchPrompt": "Veuillez sélectionner une succursale pour voir les données", "salesByTimeslot": "Ventes par créneau horaire"}, "charts": {"totalReservationsCreated": "Total des Réservations Créées", "conversionFunnel": "Entonnoir de Conversion", "filteredByVisitDate": "(Filtré par Date de Visite)", "funnelTotalReservations": "Total des Réservations", "funnelConfirmedPresence": "Confirmées (Présence)", "funnelSales": "<PERSON><PERSON><PERSON>", "conversionRates": "Taux de Conversion", "reservationToConfirmed": "Réservation → Confirmée:", "confirmedToSales": "Confirmée → Ventes:", "overallReservationToSales": "Global (Réservation → Ventes):", "conversionPercent": "conversion", "loadingFunnelData": "Chargement des données de l'entonnoir...", "reservationsByMonth": "Réservations Créées par Mois", "filteredByCreationDate": "(Filtré par Date de Création)", "reservationsCreated": "Réservations Créées", "compareReservationsCreated": "Comparer Réservations Créées", "created": "Créées:", "loadingReservationsData": "Chargement des données de réservations...", "noReservationsData": "Aucune donnée de réservation disponible", "salesByMonth": "Ventes par Mois", "filteredBySaleDate": "(Filtré par Date de Vente)", "salesCount": "Nombre de Ventes", "compareSalesCount": "Comparer <PERSON> de Vente<PERSON>", "loadingSalesData": "Chargement des données de ventes...", "noSalesData": "<PERSON><PERSON><PERSON> donnée de vente disponible"}, "validation": {"required": "Ce champ est obligatoire", "invalid": "Ce champ est invalide", "passwordMismatch": "Les mots de passe ne correspondent pas", "invalidEmail": "<PERSON><PERSON><PERSON> email invalide", "minLength": "Doit contenir au moins {{min}} caractères", "maxLength": "Ne peut pas dépasser {{max}} caractères", "number": "Doit être un nombre", "integer": "Doit être un nombre entier", "branchRequired": "Au moins une succursale est requise pour les rôles liés aux succursales", "min": "Doit être au moins {{min}}", "max": "Ne peut pas dépasser {{max}}"}, "rdvs": {"title": "<PERSON><PERSON><PERSON>vous", "addRdv": "Ajouter un rendez-vous", "editRdv": "Modifier le rendez-vous", "deleteRdv": "Supp<PERSON>er le rendez-vous", "restore": "Restaurer le rendez-vous", "showDeleted": "Afficher les supprimés", "deleteWarning": "Êtes-vous sûr de vouloir supprimer ce rendez-vous? Cette action peut être annulée ultérieurement.", "fields": {"firstName": "Prénom", "lastName": "Nom", "address": "<PERSON><PERSON><PERSON>", "postalCode": "Code Postal", "phone": "Téléphone", "date_rdv": "Date du Rendez-vous", "hour": "<PERSON><PERSON> du Rendez-vous", "selectHour": "Sélectionner l'heure", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "deletedAt": "Supprimé le", "selectStatus": "Sélectionner le statut"}, "status": {"pending": "En attente", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>"}, "messages": {"created": "<PERSON><PERSON><PERSON>vous c<PERSON>é avec succès", "updated": "<PERSON><PERSON><PERSON>vous mis à jour avec succès", "deleted": "Rendez-vous supprimé avec succès", "restored": "Rendez-vous restauré avec succès", "fetchError": "Échec de la récupération des rendez-vous", "saveError": "Échec de l'enregistrement du rendez-vous", "deleteError": "Échec de la suppression du rendez-vous", "restoreError": "Échec de la restauration du rendez-vous"}}, "conversations": {"automatic": "Automatique", "linkedReservation": "Allez à la réservation", "openReservation": "Ouvrir la réservation", "goToReservation": "Aller à la réservation", "noConversations": "Aucune conversation trouvée", "selectConversation": "Sélectionnez une conversation pour voir les messages", "typeMessage": "Écrivez un message...", "title": "Conversations", "phoneNumber": "Numéro de téléphone", "lastMessage": "<PERSON><PERSON> message", "unreadCount": "Messages non lus", "sendMessage": "Envoyer un message", "messages": "Messages", "send": "Envoyer", "sending": "Envoi en cours...", "searchContacts": "Rechercher des contacts...", "enterPhoneNumber": "Entrez le numéro de téléphone", "contactList": "Liste des contacts", "fetchFailed": "Échec de la récupération des messages", "noMessages": "Aucun message trouvé", "viewMessages": "Voir les messages", "messageFailed": "Échec de l'envoi du message", "messageResent": "Message renvoy<PERSON> avec succès", "resendFailed": "Échec du renvoi du message", "resend": "<PERSON><PERSON><PERSON>", "resending": "Renvoi en cours...", "newMessage": "Nouveau message", "cancel": "Annuler", "viewReservation": "Voir la réservation", "archive": "Archiver", "unarchive": "Désarchiver", "archived": "Archivé", "showArchived": "Afficher les archives", "hideArchived": "Masquer les archives", "archiveSuccess": "Conversation archivée avec succès", "unarchiveSuccess": "Conversation désarchivée avec succès", "archiveError": "Échec de l'archivage de la conversation", "loginRequired": "Vous devez être connecté pour archiver les conversations", "refreshSuccess": "Conversations actualisées", "refreshFailed": "Échec de l'actualisation des conversations", "markReadFailed": "Échec du marquage de la conversation comme lue", "loading": "Chargement...", "dev": {"archivedTitle": "Conversation archivée", "archivedDesc": "a archivé cette conversation", "archiveSelected": "Archiver la sélection", "unarchiveSelected": "Désarchiver la sélection", "newMessage": "Nouveau message", "showArchives": "Afficher les archives", "hideArchives": "Masquer les archives", "archived": "Archivé", "archiving": "Archivage...", "archiveBeforeDate": "Archiver avant cette date", "searchPlaceholder": "Rechercher des contacts..."}, "authorLabel": "par {author}", "selectBranch": "Sélectionner une succursale", "columns": {"id": "ID", "token": "<PERSON><PERSON>", "partner": "Partenaire", "reservation": "Réservation", "created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "actions": "Actions"}}, "telephoniste": {"title": "Téléphoniste", "createdSuccess": "Téléphoniste créé avec succès", "updatedSuccess": "Téléphoniste mis à jour avec succès", "saveError": "Échec de l'enregistrement du téléphoniste", "fetchError": "Échec de la récupération des permissions", "serviceTypes": "Types de service", "assignedUser": "Utilisateur <PERSON>", "selectUser": "Sélectionner un utilisateur"}, "serviceTypes": {"title": "Types de services", "description": "Description", "listTitle": "Liste des types de services", "listDescription": "Tous les types de services disponibles pour les réservations", "addNew": "Ajouter un nouveau type de service", "addNewDescription": "Créer un nouveau type de service pour les réservations", "code": "Code", "codePlaceholder": "Entrez un code unique", "name": "Nom", "namePlaceholder": "Entrez un nom descriptif", "descriptionPlaceholder": "Entrez une description", "forAdults": "Pour adultes", "forChildren": "Pour enfants", "isActive": "Actif", "status": "Statut", "active": "Actif", "inactive": "Inactif", "showInactive": "Afficher inactifs", "search": "Rechercher des types de services...", "noResults": "Aucun type de service trouvé"}, "sellerDashboard": {"welcome": "Bienvenue", "totalAssignments": "Mes attributions totales", "todayBranchReservations": "Réservations du jour à la succursale", "loading": "Chargement du tableau de bord", "comingSoon": "Bientôt disponible", "branch": "Succursale: {{branch}}"}, "commissionTypes": {"title": "Types de Commissions", "listTitle": "Liste des Types de Commissions", "listDescription": "Tous les types de commissions disponibles dans le système", "addNew": "Ajouter un Nouveau Type de Commission", "editTitle": "Modifier le Type de Commission", "createTitle": "Créer un Type de Commission", "createDescription": "Configurez un nouveau type de commission avec des conditions et montants spécifiques", "editDescription": "Modifier les détails de ce type de commission", "name": "Nom", "code": "Code", "codeTooltip": "Le code est utilisé pour identifier programmatiquement ce type de commission. Par exemple, 'commission-vente' permet au système de générer automatiquement des factures spécialisées pour les commissions de vente. Ce code doit être unique et descriptif.", "statusTrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON> Statut", "amount": "<PERSON><PERSON>", "create": "Créer le Type de Commission", "creating": "Création en cours...", "update": "Mettre à jour le Type de Commission", "updating": "Mise à jour en cours...", "save": "Enregistrer", "saving": "Enregistrement en cours...", "recipient": "Bénéficiaire", "recipientDescription": "Sélectionnez qui recevra cette commission", "partner": "Partenaire", "papBranch": "Succursale PAP", "assignedUser": "U<PERSON><PERSON><PERSON><PERSON>", "selectStatusTrigger": "Sélectionner un déclencheur de statut", "selectStatus": "Sélectionnez le statut qui déclenche cette commission", "timeRestriction": "Restriction de Temps", "activeRange": "Plage de <PERSON>s", "startDate": "Date de Début", "endDate": "Date de Fin", "startTime": "<PERSON><PERSON>but", "endTime": "<PERSON>ure de Fin", "dayRestriction": "Restriction de Jour", "daysOfWeek": "<PERSON><PERSON> <PERSON> la Semaine", "selectDays": "Sélectionner les Jours", "selectDaysDescription": "Choisissez les jours de la semaine auxquels cette commission s'applique", "userRestriction": "Restriction d'Utilisateur", "branchRestriction": "Restriction de Succursale", "selectUsers": "Sélectionner des Utilisateurs", "selectBranches": "Sélectionner des Succursales", "isAlwaysActive": "Toujours Actif", "alwaysActive": "Toujours Actif", "alwaysActiveDescription": "Cette commission sera toujours active sans restriction de temps ou de jour", "timeRestricted": "Restriction Temporelle", "timeRestrictedDescription": "Cette commission ne sera active qu'à des heures spécifiques de la journée", "dayRestricted": "Restriction par Jour", "dayRestrictedDescription": "Cette commission ne sera active que certains jours de la semaine", "branchRestricted": "Restriction par Succursale", "branchRestrictedDescription": "Cette commission ne sera applicable que pour des succursales spécifiques", "userRestricted": "Restriction par Utilisateur", "userRestrictedDescription": "Cette commission ne sera applicable que pour des utilisateurs spécifiques", "timeSettings": "Paramètres de Temps", "eligibleBranches": "Succursales Éligibles", "branchSelectionInfo": "Sélectionnez les succursales auxquelles cette commission s'applique", "searchBranches": "Rechercher des succursales...", "eligibleUsers": "Utilisateurs Éligibles", "dailyTimeframe": "Restreindre à une Plage Horaire Quotidienne", "specificDays": "Restreindre à des Jours Spécifiques", "specificUsers": "Restreindre à des Utilisateurs Spécifiques", "specificBranches": "Restreindre à des Succursales Spécifiques", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer ce type de commission?", "deleteDescription": "Cette action ne peut pas être annulée.", "noCommissionTypes": "Aucun type de commission trouvé", "restrictions": "Restrictions", "actions": "Actions", "invalidDateRange": "Plage de dates invalide", "timeRange": "<PERSON>lage horaire", "daysOnly": "Jours seulement", "userCount": "{{count}} Utilisateurs", "branchCount": "{{count}} Succursales", "dateRange": "Plage de <PERSON>s", "deleteTitle": "Supprimer le Type de Commission", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression en cours...", "cancel": "Annuler", "loading": "Chargement des types de commissions...", "searchUsers": "Rechercher des utilisateurs...", "isAutoApproved": "Approbation automatique des commissions", "isAutoApprovedDescription": "Si activé, les commissions de ce type sont automatiquement approuvées lors de leur création.", "fetchingCommissions": "Récupération des commissions...", "fetchingBonuses": "Récupération des bonus...", "fetchingUsers": "Récupération des détails utilisateur...", "idle": "En attente de démarrage...", "invoiceGenerationFailed": "Échec de la génération des factures"}, "messages": {"saveAsTemplate": "Enregistrer comme modèle", "templateName": "Nom du modèle", "enterTemplateName": "Entrez un nom pour ce modèle", "templateContent": "Contenu du modèle", "templateSaved": "<PERSON><PERSON><PERSON><PERSON> enregistré avec succès", "templateSaveFailed": "Échec de l'enregistrement du modèle", "templateLoadFailed": "Échec du chargement des modèles", "templates": "<PERSON><PERSON> mod<PERSON>"}, "pagination": {"showing": "Affichage de", "of": "sur", "perPage": "par page", "previous": "Précédent", "next": "Suivant", "first": "Premier", "last": "<PERSON><PERSON>", "page": "Page"}, "deleteConfirmation": {"title": "Êtes-vous sûr ?", "description": "Cette action est irréversible.", "confirm": "Confirmer", "cancel": "Annuler"}, "bugReport": {"buttonAriaLabel": "Signaler un bug", "dialogTitle": "Signaler un Bug", "dialogDescription": "Décrivez le problème que vous avez rencontré. Soyez aussi détaillé que possible.", "form": {"titleLabel": "Titre", "titlePlaceholder": "ex., Bouton ne fonctionne pas sur la page de profil", "descriptionLabel": "Description", "descriptionPlaceholder": "Décrivez les étapes pour reproduire le bug et ce que vous attendiez qu'il se passe.", "cancelButton": "Annuler", "submitButton": "Soumettre le Rapport", "submittingButton": "Soumission..."}, "toast": {"successTitle": "<PERSON><PERSON>", "successDescription": "Merci pour votre retour !", "errorTitle": "Échec de la Soumission", "errorDescriptionDefault": "Une erreur inattendue s'est produite."}, "validation": {"titleMin": "Le titre doit contenir au moins 5 caractères.", "descriptionMin": "La description doit contenir au moins 10 caractères."}}, "requestsPage": {"title": "Gestion des Requêtes"}, "requests": {"archive": "Archiver", "unarchive": "Désarchiver", "customerMessages": "Messages Clients", "kanbanView": "<PERSON><PERSON>", "listView": "<PERSON><PERSON>", "filters": {"all": "Tous", "urgent": "<PERSON><PERSON>", "withBooking": "Avec Réservation", "reschedule": "Reporter", "cancel": "Annuler"}, "urgent": "<PERSON><PERSON>", "from": "De", "reservation": "Réservation", "atTime": "à", "handle": "Traiter", "ignore": "<PERSON><PERSON><PERSON>", "viewBooking": "Voir la réservation", "tags": {"replanifier": "<PERSON><PERSON><PERSON> de report", "annuler": "Annulation", "information": "De<PERSON>e d'information", "autre": "<PERSON><PERSON>"}, "debug": {"info": "Infos de débogage :", "updating": "Mise à jour", "new": "Nouveau", "handled": "Traités", "ignored": "Ignorés", "total": "Total", "refreshData": "Actualiser les données", "processing": "En traitement"}, "loadingMessages": "Chargement des messages...", "errorMessage": {"title": "Impossible de charger les messages :", "refreshInstructions": "Veuillez actualiser la page ou contacter le support si le problème persiste."}, "emptyState": {"noMessages": "Aucun message trouvé", "filterInstructions": "Essayez de modifier votre filtre ou revenez plus tard."}, "kanban": {"noMessages": "Aucun message dans cette catégorie"}, "startProcessing": "Commencer le traitement", "viewFullMessage": "Voir le message complet", "fullMessageTitle": "Message complet", "archiveAll": "Tout archiver", "unarchiveAll": "<PERSON>ut d<PERSON>chi<PERSON>", "showArchived": "Afficher archivés", "hideArchived": "Masquer archivés"}, "kanban": {"new": "Nouveaux Messages", "processing": "En cours de traitement", "processed": "Traités", "ignored": "Ignorés"}, "userPerformanceStats": "Statistiques de performance des PAP", "user": "Utilisa<PERSON>ur", "total": "Total", "prev": "Précédent", "next": "Suivant", "page": "Page", "commissionStats": "Statistiques des commissions", "totalCommissions": "Nombre total de commissions", "totalCommissionAmount": "Montant total des commissions", "topCommissionUser": "Utilisateur principal par montant de commission", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "affectations": {"selectTimeSlot": "Sélectionner un créneau", "allTimeSlots": "To<PERSON> les cré<PERSON>ux"}, "You cannot cancel a message that has already been sent": "Vous ne pouvez pas annuler un message déjà envoyé", "Message canceled successfully": "Message annulé avec succès", "Failed to cancel message": "Échec de l'annulation du message", "You can only reschedule pending or failed messages": "Vous ne pouvez reprogrammer que les messages en attente ou échoués", "Message rescheduled successfully": "Message reprogrammé avec succès", "Failed to reschedule message": "Échec de la reprogrammation du message", "Sent": "<PERSON><PERSON><PERSON>", "Pending": "En attente", "Failed": "<PERSON><PERSON><PERSON>", "Canceled": "<PERSON><PERSON><PERSON>", "Customer": "Client", "Phone": "Téléphone", "Template": "<PERSON><PERSON><PERSON><PERSON>", "Message": "Message", "Status": "Statut", "Created At": "<PERSON><PERSON><PERSON>", "Scheduled At": "Programmé pour", "Actions": "Actions", "No scheduled messages found": "Aucun message programmé trouvé", "Unknown": "Inconnu", "Unknown Template": "<PERSON><PERSON><PERSON><PERSON> inconnu", "Reschedule": "Reprogrammer", "Cancel": "Annuler", "Reschedule Message": "Reprogrammer le message", "Choose a new date and time for this message to be sent.": "Choisissez une nouvelle date et heure d'envoi pour ce message.", "Scheduled Date": "Date programmée", "Pick a date": "Sélectionner une date", "Time": "<PERSON><PERSON>", "Rescheduling...": "Reprogrammation...", "Search messages...": "Rechercher des messages...", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "First page": "Première page", "Previous page": "<PERSON> p<PERSON>", "Page": "Page", "Next page": "<PERSON> suivante", "Last page": "Dernière page", "Scheduled SMS Messages": "Messages SMS programmés", "Manage your scheduled SMS messages": "G<PERSON>rer vos messages SMS programmés", "Refresh": "Actualiser", "Messages refreshed": "Messages actualisés", "Failed to load messages": "Échec du chargement des messages", "Error": "<PERSON><PERSON><PERSON>", "openReservation": "Ouvrir la réservation", "redirecting": "Redirection en cours...", "noPermission": "Vous n'avez pas la permission d'accéder à cette page.", "action": {"copy": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "more": "Plus d'actions", "exportExcel": "Exporter en Excel", "save": "Enregistrer", "linkCells": "<PERSON><PERSON> les Cellules"}, "brevoTemplates": {"addNew": "Ajouter un modèle", "editTemplate": "Modifier le modèle", "name": "Nom du modèle", "description": "Description", "content": "Contenu (HTML supporté)", "variables": "Variables", "noVariables": "Aucune variable pour l'instant. Ajoutez-en depuis la liste ci-dessous.", "disabled": "Désactivé", "livePreview": "Aperçu en direct", "noTemplates": "<PERSON><PERSON><PERSON> modè<PERSON> email trouvé.", "noTemplatesDesc": "Vous n'avez pas encore créé de modèles email.", "saveSuccess": "<PERSON><PERSON><PERSON><PERSON> enregistré !", "deleteTitle": "Supprimer le modèle ?", "deleteDesc": "Êtes-vous sûr de vouloir supprimer ce modèle ? Cette action est irréversible.", "type": "Type", "typeGeneric": "Générique", "typeAccountCreated": "Création de compte (unique)", "typePasswordReset": "Réinitialisation du mot de passe (unique)", "specialVariable": "Variable spéciale", "specialVariableHint": "Cette variable est requise pour ce type de modèle.", "typeUniqueError": "Un modèle de ce type spécial existe déjà. Un seul est autorisé."}, "eventTypes": {"title": "Types d'événements", "addNew": "Ajouter un nouveau type", "edit": "Modifier le type d'événement", "code": "Code", "name": "Nom", "codePlaceholder": "Entrez le code du type", "namePlaceholder": "Entrez le nom du type", "codeDescription": "Un code unique pour ce type d'événement", "nameDescription": "Le nom qui sera affiché", "createSuccess": "Type d'événement créé avec succès", "updateSuccess": "Type d'événement mis à jour avec succès", "deleteSuccess": "Type d'événement supprimé avec succès", "deleteError": "Échec de la suppression du type d'événement", "saveError": "Échec de l'enregistrement du type d'événement", "notFound": "Type d'événement non trouvé", "noEventTypes": "Aucun type d'événement trouvé. Créez votre premier!", "deleteConfirmTitle": "Supprimer le type d'événement", "deleteConfirmDescription": "Êtes-vous sûr de vouloir supprimer le type d'événement '{name}'? Cette action ne peut pas être annulée."}, "breadcrumb": {"home": "Accueil"}, "loading": "Chargement...", "billing": {"cannotBeUndone": "Cette action est irréversible.", "selectUser": "Sélectionner un utilisateur", "archiveSuccess": "Factures archivées", "unarchiveSuccess": "Factures désarchivées", "confirmMassArchiveDescription": "Êtes-vous sûr de vouloir archiver ces factures ?", "confirmMassUnarchiveDescription": "Êtes-vous sûr de vouloir désarchiver ces factures ?", "confirmMassDeleteDescription": "Êtes-vous sûr de vouloir supprimer ces factures ?", "sendContreFacturesTooltip": "Envoyer des contre-factures", "confirmMassDownloadTitle": "Confirmer le téléchargement", "confirmMassDownloadDescription": "Êtes-vous sûr de vouloir télécharger", "pdfs": "PDFs", "confirmMassSendContreFacturesTitle": "Confirmer l'envoi des contre-factures", "confirmMassSendContreFacturesDescription": "Êtes-vous sûr de voulo<PERSON> envoyer", "contreFactures": "contre factures", "confirmDeleteTitle": "Confirmer la <PERSON>", "confirmDeleteDescription": "Êtes-vous sûr de vouloir supprimer ces factures ?", "confirmSingleDeleteTitle": "Confirmer la <PERSON>", "confirmSingleDeleteDescription": "Êtes-vous sûr de vouloir supprimer cette facture ?", "confirmArchiveTitle": "Confirmer l'archivage", "confirmUnarchiveTitle": "Con<PERSON>rm<PERSON> le désarchivage", "confirmArchiveDescription": "Êtes-vous sûr de vouloir archiver cette facture ?", "confirmUnarchiveDescription": "Êtes-vous sûr de vouloir désarchiver cette facture ?", "massVerify": "Vérifier", "verifying": "Vérification...", "massVerifySuccess": "Factures vérifiées", "massVerifyPartial": "Certaines factures vérifiées", "massVerifyFailed": "Aucune facture vérifiée", "verified": "vérifiées", "failed": "échouées", "signatureDate": "<PERSON><PERSON> le", "updateInvoice": "Mettre à jour la facture...", "currentStatus": "Statut actuel", "newStatus": "Nouveau statut", "reason": "<PERSON>son", "reasonPlaceholder": "Expliquez pourquoi vous changez ce statut...", "archiving": "Archivage...", "delete": "<PERSON><PERSON><PERSON><PERSON>", "current": "courant", "taxType": "Type de taxe", "selectTaxType": "Sélectionner un type de taxe", "downloading": "Téléchargement...", "downloadPDFs": "Télécharger les PDFs", "sendEmails": "Envoyer les courriels", "applyFilter": "App<PERSON><PERSON> le filtre", "unarchiving": "Désarchivage...", "signedOn": "<PERSON><PERSON> le", "sendingEmails": "Envoi des courriels...", "refreshTaxInfo": "Rafraîchir les informations fiscales", "sendingEmail": "Envoi du courriel...", "regenerateContreFacture": "Régénérer la contre-facture", "dateRangeOverridden": "Cette plage de dates a été manuellement remplacée.", "status": {"new": "Nouveau", "processing": "En cours", "paid": "<PERSON><PERSON>", "nouveau": "Nouveau", "verifie": "Vérifié", "envoye": "<PERSON><PERSON><PERSON>", "signe": "<PERSON><PERSON>", "en_traitement": "En traitement", "paye": "<PERSON><PERSON>", "en_retard": "En Retard"}, "statusDescriptions": {"nouveau": "Facture créée, en attente de vérification", "verifie": "Facture vérifiée par la direction AMQ", "envoye": "Facture envoyée par email au client", "signe": "Facture signée par le client", "en_traitement": "Facture en cours de traitement pour paiement", "paye": "Facture payée", "en_retard": "Facture en retard de signature ou paiement"}, "actions": {"changeStatus": "Changer le statut", "viewHistory": "Voir l'historique", "markAsVerified": "Marquer comme vérifié", "sendEmail": "Envoyer par email", "markAsSigned": "<PERSON>quer comme signé", "markAsProcessing": "Marquer en traitement", "markAsPaid": "Marquer comme payé", "markAsLate": "Marquer en retard"}, "automation": {"thursdayRule": "Automatique: <PERSON><PERSON> 12h - Sign<PERSON> → En traitement", "fridayRule": "Automatique: Vendredi 12h - En traitement → Payé", "lateRule": "Automatique: 48h après envoi - Envoy<PERSON> → En retard", "automatedChange": "Changement automatique", "manualChange": "Changement manuel"}, "kanban": {"noInvoices": "Aucune facture dans cette colonne"}, "statusHistory": {"title": "Historique des statuts", "changedBy": "Modifié par", "changedAt": "<PERSON><PERSON><PERSON><PERSON> le", "reason": "<PERSON>son", "automatic": "Automatique", "manual": "<PERSON>", "noHistory": "Aucun historique disponible"}, "modals": {"changeStatus": {"title": "Changer le statut", "currentStatus": "Statut actuel", "newStatus": "Nouveau statut", "reason": "<PERSON><PERSON> (optionnel)", "reasonPlaceholder": "Expliquez pourquoi vous changez ce statut...", "cancel": "Annuler", "update": "Mettre à jour", "updating": "Mise à jour...", "invalidTransition": "Transition de statut invalide", "success": "Statut mis à jour avec succès", "error": "Erreur lors de la mise à jour du statut"}}, "itemType": "Type d'élément", "selectType": "Sélectionner un type", "itemTitleHint": "Le titre se mettra automatiquement à jour lorsque vous sélectionnez un type d'élément", "importBonuses": "Importer les bonus", "selectBonuses": "Sélectionner les bonus à importer", "noBonusesSelected": "Aucun bonus sélectionné", "noBonusesFound": "Aucun bonus trouvé", "bonusesImported": "Bonus importés", "bonusesImportedDesc": "Ligne de bonus ajoutée à la facture.", "bonus": "Bonus", "tpsRate": "Taux de TPS", "tvqRate": "Taux de TVQ", "taxRates": "Taux de taxe", "tpsRateDescription": "Le taux de taxe pour la TPS (Taxes sur les ventes).", "tvqRateDescription": "Le taux de taxe pour la TVQ (Taxes sur la valeur des biens et services).", "beneficiary": "Bénéficiaire", "invoiceTitle": "Facture", "billing": "Facturation", "exportPDF": "Exporter en PDF", "title": "Titre", "invoice": "Facture", "invoices": "Factures", "addInvoice": "<PERSON><PERSON>er une facture", "createInvoice": "<PERSON><PERSON>er une facture", "invoiceNumber": "Numéro de facture", "invoiceDetails": "<PERSON>é<PERSON> de la facture", "invoiceItems": "Articles de la facture", "editInvoiceDetails": "Modifier les détails", "noItems": "Aucun article", "addItem": "Ajouter un article", "editItem": "Modifier l'article", "itemTitle": "Titre", "date": "Date", "quantity": "Quantité", "unitPrice": "Prix unitaire", "tax": "Taxe", "tps": "TPS", "tvq": "TVQ", "subtotal": "Sous-total", "total": "Total", "itemAdded": "Article ajouté", "itemUpdated": "Article mis à jour", "itemDeleted": "Article supprimé avec succès", "itemDeletedMessage": "L'article de la facture a été supprimé", "exportPdf": "Exporter en PDF", "isSigned": "Signé ?", "isSignedTooltip": "Indique si la facture est signée", "signed": "<PERSON><PERSON>", "notSigned": "Non signé", "kanbanView": "<PERSON><PERSON>", "listView": "<PERSON><PERSON>", "invoiceStatusUpdated": "Statut de la facture mis à jour", "errorUpdatingStatus": "Erreur lors de la mise à jour du statut de la facture", "importCommissions": "Importer les commissions", "importSelected": "Importer la sélection", "selectCommissions": "Sélectionner les commissions à importer", "noCommissionsFound": "Aucune commission trouvée", "noCommissionsSelected": "Aucune commission sélectionnée", "commissionsImported": "Commissions importées", "commissionsImportedDesc": "Ligne de commission ajoutée à la facture.", "commission": "commission", "amount": "<PERSON><PERSON>", "generateInvoices": "Générer les factures", "confirmGenerateInvoices": "Confirmer la génération de factures", "generateInvoicesConfirmText": "<PERSON>ci générera une facture pour chaque utilisateur avec des commissions ou des bonus pour la semaine sélectionnée :", "generateInvoicesNote": "Remarque : Cette action enregistrera tous les changements de bonus en attente avant de générer les factures.", "generate": "<PERSON><PERSON><PERSON><PERSON>", "generationResults": "Résultats", "invoicesGenerated": "Factures générées avec succès", "user": "Utilisa<PERSON>ur", "savingBonuses": "Enregistrement des bonus...", "preparingData": "Préparation des données de commission...", "generatingInvoices": "Génération des factures en cours...", "processingItems": "Traitement des articles de facture...", "finalizingInvoices": "Finalisation des factures...", "completed": "<PERSON><PERSON><PERSON><PERSON>", "generationTimeNote": "Ce processus peut prendre plusieurs minutes selon le nombre de commissions.", "taxable": "Taxable", "clickToAdd": "Cliquez sur le bouton ci-dessus pour ajouter votre premier article", "fetchingCommissions": "Récupération des commissions...", "fetchingBonuses": "Récupération des bonus...", "fetchingUsers": "Récupération des détails utilisateur...", "idle": "En attente de démarrage...", "invoiceGenerationFailed": "Échec de la génération des factures", "sendEmail": "Envoyer par e-mail", "search": "Rechercher des factures...", "dueDate": "Date d'échéance", "customer": "Client", "tpsLabel": "TPS", "tvqLabel": "TVQ", "invoiceCreated": "Facture créée avec succès", "invoiceUpdated": "Facture mise à jour avec succès", "dateRange": "Plage de dates", "startDate": "Date de début", "endDate": "Date de fin", "export": "Exporter", "selectDateRange": "Sélectionner une plage de dates", "generateError": "Erreur lors de la génération des factures", "sendEmailWithInvoices": "Envoyer des emails avec les factures PDF aux utilisateurs", "invoiceDate": "Date de facturation", "invoiceTotal": "Total de la facture", "invoiceSigning": "Signature de facture", "generateSigningLink": "Générer un lien de signature", "signingLink": "<PERSON><PERSON> de <PERSON>", "signingLinkGenerated": "Lien de signature généré avec succès", "signingLinkCopied": "Lien de signature copié dans le presse-papiers", "signingLinkExpiresMessage": "Ce lien sera valide jusqu'à ce que l'utilisateur signe la facture ou 2 heures après sa première visite.", "signInvoice": "Signer la facture", "invoiceSigned": "Facture signée avec succès", "invoiceSignExpired": "Ce lien a expiré et n'est plus valide pour la signature.", "pleaseSignFullName": "Veuillez saisir votre nom complet pour signer cette facture :", "due": "Échéance", "paidDate": "Date de paiement", "client": "Client", "editInvoice": "Modifier la facture", "deleteInvoice": "Supprimer la facture", "deleteItem": "Supprimer l'article", "itemSaved": "Article enregistré avec succès", "itemSaveError": "Erreur lors de l'enregistrement de l'article", "noInvoices": "Aucune facture", "massGenerateInvoices": "Génération en masse de factures", "generateAndSendEmails": "Générer et envoyer des e-mails", "selectClient": "Sélectionner un client", "saveInvoice": "Enregistrer la facture", "selectStatus": "Sélectionner un statut", "description": "Description", "confirmDeleteInvoice": "Êtes-vous sûr de vouloir supprimer cette facture ?", "confirmDeleteItem": "Êtes-vous sûr de vouloir supprimer cet article ?", "name": "Nom", "email": "Email", "clientDetails": "Détails du client", "itemDetails": "Détails de l'article", "itemDate": "Date de l'article", "invoiceDeleted": "Facture supprimée avec succès", "errorCreatingInvoice": "E<PERSON>ur lors de la création de la facture", "errorUpdatingInvoice": "Erreur lors de la mise à jour de la facture", "errorDeletingInvoice": "<PERSON><PERSON>ur lors de la suppression de la facture", "errorFetchingInvoices": "Erreur lors de la récupération des factures", "sending": "Envoi en cours...", "emailSent": "E-mail envoy<PERSON> ave<PERSON> succès", "emailError": "Erreur lors de l'envoi de l'e-mail", "downloadPdf": "Télécharger PDF", "itemsImported": "Articles importés avec succès", "importError": "Erreur lors de l'importation", "invoiceProgress": "Progression de la génération de factures", "searchInvoices": "Rechercher des factures...", "billing.startDate": "Date de début", "billing.endDate": "Date de fin", "chargeAmount": "<PERSON><PERSON> fact<PERSON>", "sendEmailTooltip": "En envoyant ce cour<PERSON>, vous confirmez être satisfait de la facture et souhaitez l'envoyer au client.", "contreFactureTitle": "Contre-Facture", "contreFacturePreview": "Aperçu de la contre-facture", "noContreFacture": "Aucune contre-facture disponible pour cette facture.", "downloadContreFacture": "Télécharger PDF", "viewContreFacture": "Voir la contre-facture", "generateContreFacture": "Générer la contre-facture", "contreFactureGenerated": "Contre-facture générée avec succès.", "contreFacturesSuccess": "Contre-factures envoyées avec succès.", "contreFacturesPartialSuccess": "Certaines contre-factures n'ont pas pu être envoyées.", "contreFacturesFailed": "Échec de l'envoi des contre-factures.", "archive": "Archiver", "unarchive": "Désarchiver", "sendContreFactures": "Envoyer les contre-factures", "archivedOn": "<PERSON><PERSON><PERSON>", "showArchived": "Afficher les factures archivées", "showDeleted": "Afficher les factures supprimées", "deletedOn": "Supprimé le", "confirmMassDelete": "Êtes-vous sûr de vouloir supprimer les factures sélectionnées ? Cette action ne peut pas être annulée.", "deleteSuccess": "Factures supprimées avec succès", "contreFacturesError": "Une erreur est survenue lors de l'envoi des contre-factures.", "deleteFailed": "Échec de la suppression des factures", "deleting": "Suppression...", "invoicesArchived": "Factures archivées avec succès", "invoicesUnarchived": "Factures désarchivées avec succès", "invoicesUpdated": "Factures mises à jour", "archiveOperationFailed": "Échec de la mise à jour du statut d'archivage", "noDateSelected": "Aucune date sélectionnée", "taxValidation": "Validation Fiscale", "taxValidationTooltip": "Statut de validation fiscale", "taxValidationValid": "Informations fiscales TPS/TVQ valides", "taxValidationInvalid": "Informations fiscales TPS/TVQ invalides", "taxValidationNonTpsTvq": "Type de taxe non-TPS/TVQ", "taxValidationUnknown": "Statut de validation fiscale inconnu", "refreshTaxInfoTooltip": "Mettre à jour les informations fiscales et le statut de validation depuis le profil utilisateur"}, "contactRequests": {"reservation": "Réservation", "title": "Demandes de contact", "description": "<PERSON><PERSON><PERSON> et consultez toutes les demandes de contact soumises par les utilisateurs ou les prospects.", "phone": "Téléphone", "postal": "Code postal", "message": "Message", "source": "Source", "noSource": "Aucune source", "sourceDetails": "<PERSON><PERSON><PERSON> de <PERSON>", "clicks": "clics", "empty": "Aucune demande de contact trouvée.", "addReservation": "Ajouter une réservation", "showArchived": "Afficher archivées", "showActive": "Afficher actives", "showingArchived": "Affichage archivées", "createReservation": "C<PERSON>er une réservation", "manageSources": "<PERSON><PERSON><PERSON> les sources", "customerInfo": "Informations client", "viewMessage": "Voir le message", "viewReservation": "Voir la réservation", "duplicateWarning": {"title": "Numéro de téléphone en double", "description": "Ce numéro de téléphone est déjà associé à des réservations existantes :", "viewReservation": "Voir la réservation", "advice": "Veuillez vérifier avant de créer une nouvelle réservation pour éviter les doublons."}, "analytics": {"title": "Analytiques", "show": "Afficher les analytiques", "hide": "Masquer les analytiques", "description": "Métriques de performance pour les demandes de contact et leur conversion en réservations.", "error": "Échec du chargement des analytiques", "totalRequests": "Total des demandes", "totalRequestsDesc": "Nombre total de demandes de contact reçues", "conversionRate": "Taux de conversion", "conversionRateDesc": "Pourcentage de demandes de contact converties en réservations", "converted": "converties", "presenceRate": "<PERSON>x de présence", "presenceRateDesc": "Pourcentage de réservations converties avec présence client", "withPresence": "avec présence", "salesRate": "<PERSON><PERSON> de vente", "salesRateDesc": "Pourcentage de réservations converties qui ont abouti à des ventes", "withSales": "avec ventes"}, "bulkActions": {"noSelection": "Aucune sélection", "selectItems": "Veuillez sélectionner des éléments", "itemsSelected": "éléments sélectionnés", "archive": "Archiver", "unarchive": "Désarchiver", "archiving": "Archivage...", "unarchiving": "Désarchivage..."}, "listView": {"title": "Liste des demandes de contact", "addReservation": "Ajouter une réservation", "filter": "<PERSON><PERSON><PERSON>", "allStatuses": "Tous les statuts", "actions": "Actions"}, "statuses": {"new": "Nouveau", "called": "<PERSON><PERSON><PERSON>", "calledTwice": "Appelé 2x", "calledThreeTimes": "Appelé 3x", "notInterested": "<PERSON><PERSON>", "toReserve": "À réserver", "reserved": "Réservé", "toCallback": "<PERSON> rappeler", "wrongNumber": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "unavailable": "<PERSON>s joignable", "title": "Statuts de demande de contact", "name": "Nom", "name_en": "Nom (EN)", "code": "Code", "color": "<PERSON><PERSON><PERSON>", "order": "Ordre", "addStatus": "Ajouter un statut", "editStatus": "Modifier le statut", "deleteTitle": "Supprimer le statut", "deleteDescription": "Êtes-vous sûr de vouloir supprimer ce statut ? Cette action ne peut pas être annulée.", "noStatuses": "Aucun statut trouvé", "createSuccess": "Statut créé avec succès", "updateSuccess": "Statut mis à jour avec succès", "deleteSuccess": "Statut supprimé avec succès", "saveError": "Erreur lors de l'enregistrement du statut", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression du statut", "loading": "Chargement des statuts...", "error": "Erreur lors du chargement des statuts", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "statusUpdated": "Statut mis à jour avec succès"}, "sources": {"title": "Sources de demandes de contact", "description": "<PERSON><PERSON><PERSON> et suivez les sources de demandes de contact avec des analyses", "createSource": "<PERSON><PERSON><PERSON> une source", "refresh": "Actualiser", "filters": "Filtres", "statistics": "Statistiques", "showStatistics": "Afficher les statistiques", "hideStatistics": "Masquer les statistiques", "statsNote": "Note : Les analyses avancées ne sont pas disponibles avec le compte Bitly gratuit. Passez à un plan payant pour le suivi détaillé des clics et les statistiques.", "searchPlaceholder": "Rechercher par tag, source ou influenceur...", "allSources": "Toutes les sources", "allInfluencers": "Tous les influenceurs", "noSources": "Aucune source trouvée", "createFirst": "<PERSON><PERSON>ez votre première source de demande de contact pour commencer", "totalSources": "Sources totales", "totalClicks": "<PERSON><PERSON><PERSON>", "averageClicks": "<PERSON><PERSON><PERSON> moyens", "topPerformer": "<PERSON><PERSON><PERSON> performeur", "performanceBySource": "Performance par type de source", "performanceByInfluencer": "Performance par influenceur", "topPerformingSources": "Sources les plus performantes", "recentActivity": "Activité récente", "clicks": "clics", "sources": "sources", "created": "<PERSON><PERSON><PERSON>", "copyUrl": "Copier l'URL", "openUrl": "Ouvrir l'URL", "create": {"title": "<PERSON><PERSON><PERSON> une source de demande de contact", "tag": "Tag", "tagDescription": "Un identifiant unique de 6 caractères pour cette source", "generateNew": "Générer un nouveau tag", "source": "Source", "selectSource": "Sélectionner le type de source", "influencer": "Influenceur", "selectInfluencer": "Sélectionner l'influenceur", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>", "creating": "Création...", "success": {"title": "Source créée avec succès !", "description": "Votre source de demande de contact a été créée et une URL raccourcie a été générée.", "urlGenerated": "URL Bitly générée :", "urlCopied": "L'URL a été automatiquement copiée dans votre presse-papiers.", "done": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"tagRequired": "Le tag est requis", "tagFormat": "Le tag doit contenir exactement 6 caractères alphanumériques", "sourceRequired": "La source est requise", "influencerRequired": "L'influenceur est requis", "tagExists": "Ce tag existe déjà. Veuillez en générer un nouveau."}}}}, "invoiceItemTypes": {"chargeAmount": "<PERSON><PERSON> fact<PERSON>", "title": "Types d'éléments de facture", "add": "Ajouter un type d'élément de facture", "edit": "Modifier le type d'élément de facture", "delete": "Supprimer le type d'élément de facture", "name": "Nom", "code": "Code", "branch": "Succursale", "requiresContreFacture": "Nécessite une contre facture", "addSuccess": "Type d'élément de facture ajouté avec succès.", "addError": "Échec de l'ajout du type d'élément de facture.", "editSuccess": "Type d'élément de facture modifié avec succès.", "editError": "Échec de la modification du type d'élément de facture.", "deleteSuccess": "Type d'élément de facture supprimé avec succès.", "deleteError": "Échec de la suppression du type d'élément de facture.", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce type d'élément de facture ?", "loading": "Chargement...", "empty": "Aucun type d'élément de facture trouvé.", "actions": "Actions"}, "taxTypes": {"percentage": "Pourcentage", "name": "Nom", "title": "Types de taxes", "add": "Ajouter un type de taxe", "edit": "Modifier le type de taxe", "delete": "Supprimer le type de taxe", "code": "Code", "nameEn": "Nom (anglais)", "nameFr": "Nom (français)", "percentages": "Pourcentages", "addPercentage": "Ajouter un pourcentage", "addSuccess": "Type de taxe ajouté avec succès.", "addError": "Échec de l'ajout du type de taxe.", "deleteSuccess": "Type de taxe supprimé avec succès.", "deleteError": "Échec de la suppression du type de taxe.", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce type de taxe ?", "loading": "Chargement...", "empty": "Aucun type de taxe trouvé.", "actions": "Actions"}, "deliveryAvailability": {"title": "Disponibilité des livraisons"}, "contacts": {"title": "Contacts", "name": "Nom", "phone": "Téléphone", "postalCode": "Code postal", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "error": "Échec du chargement des contacts", "retry": "<PERSON><PERSON><PERSON><PERSON>", "empty": "Aucun contact trouvé.", "unknown": "Inconnu"}, "invitations": {"noGroup": "Pas de Groupe", "group": "Groupe", "selectPartnerFirst": "Sélectionnez d'abord un partenaire", "progress": {"generating": "Génération des invitations en cours...", "success": "Invitations générées avec succès", "error": "Échec de la génération des invitations", "loading": "Chargement de la progression...", "completed": "Invitations générées", "successTitle": "Succès!", "successDescription": "Toutes les invitations ont été générées avec succès.", "errorTitle": "<PERSON><PERSON><PERSON>", "minimize": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "persistentProgress": "Génération en cours"}, "title": "Invitations", "new": "Nouvelle Invitation", "edit": "Modifier l'Invitation", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette invitation?", "partner": "Partenaire", "reservation": "Réservation", "noReservation": "Pas de Réservation", "searchPlaceholder": "Rechercher par nom ou email du partenaire", "filters": {"partner": "Filtrer par Partenaire", "reservation": "Filtrer par Réservation", "date": "Filtrer par Date"}, "columns": {"noGroup": "Pas de Groupe", "group": "Groupe", "id": "ID", "token": "<PERSON><PERSON>", "partner": "Partenaire", "reservation": "Réservation", "created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "actions": "Actions", "invitationUrl": "URL d'Invitation", "partnerName": "Nom du Partenaire", "createdAt": "<PERSON><PERSON><PERSON>"}, "notFound": "Aucune invitation trouvée", "create": {"success": "Invitation créée avec succès", "error": "Échec de la création de l'invitation"}, "update": {"success": "Invitation mise à jour avec succès", "error": "Échec de la mise à jour de l'invitation"}, "delete": {"title": "Supprimer l'invitation", "success": "Invitation supprimée avec succès", "error": "Échec de la suppression de l'invitation"}, "generate": {"groupNamePlaceholder": "Entrez le nom du groupe", "groupName": "Nom du Groupe", "title": "Générer des Invitations", "description": "<PERSON><PERSON><PERSON> plusieurs invitations pour le partenaire sélectionné", "count": "Nombre d'Invitations", "selectPartner": "Sélectionner un partenaire", "submit": "<PERSON><PERSON><PERSON><PERSON>", "success": "Invitations Générées", "successDescription": "Invitations générées avec succès", "largeSuccessDescription": "Génération d'invitations démarrée avec succès. Vous pouvez suivre la progression.", "error": "Échec de la génération des invitations", "cantCloseWhileGenerating": "Veuillez patienter pendant la génération des invitations. Cette boîte de dialogue ne peut pas être fermée pendant la génération."}, "groups": {"active": "Actif", "inactive": "Inactif", "showInactive": "Afficher les Groupes Inactifs", "markAsActive": "Marquer comme Actif", "markAsInactive": "Marquer comme Inactif", "statusUpdated": "Statut du groupe mis à jour avec succès", "statusUpdateFailed": "Échec de la mise à jour du statut du groupe"}, "tokens": {"totalAllGroups": "Total (Tous les Groupes)", "activeGroupsOnly": "Groupes Actifs Seulement", "inactiveGroupsOnly": "Groupes Inactifs Seulement", "groupBreakdown": "Répartition des Groupes", "tokenStatisticsComparison": "Comparaison des Statistiques de Jetons", "comprehensiveBreakdown": "Répartition détaillée montrant l'impact des groupes actifs vs inactifs"}, "structure": {"partnersAndGroups": "Partenaires et Groupes", "invitationsAndReservations": "Invitations et Réservations", "tokensAndConversion": "Jetons et Conversion", "activeVsInactiveStats": "Statistiques Groupes Actifs vs Inactifs", "comprehensiveBreakdown": "Répartition complète montrant l'impact du statut des groupes sur les métriques système", "totalInvitations": "Total des Invitations", "totalGroups": "groupes au total", "activeGroups": "Groupes Actifs", "inactiveGroups": "Groupes Inactifs"}}, "reservationsAccess": {"title": "Accès aux Réservations", "description": "<PERSON><PERSON><PERSON> les paramètres d'accès aux réservations et les permissions pour les rôles et utilisateurs.", "rolesTab": "Filtres par Rôle", "usersTab": "Filtres par Utilisateur", "roleFiltersTitle": "Filtres de Réservation par Rôle", "roleFiltersDescription": "Configurer les paramètres d'accès aux réservations par défaut pour chaque rôle.", "userFiltersTitle": "Filtres de Réservation Spécifiques à l'Utilisateur", "userFiltersDescription": "Remplacer les paramètres de rôle avec des filtres d'accès aux réservations spécifiques à l'utilisateur.", "errorFetchingStatuses": "Échec du chargement des statuts de réservation", "statusCodesFilter": "Filtre des Codes de Statut", "selectAll": "<PERSON><PERSON>", "clearAll": "<PERSON><PERSON>", "inheritFromRoles": "Hériter des Rôles", "branchAccessFilter": "Filtre d'Accès aux Succursales", "branchAccess": {"title": "Accès aux Succursales", "all": "Toutes les Succursales", "assigned": "Succursales Assignées Seulement", "none": "Aucun <PERSON>"}, "assignmentFilterTitle": "Filtre d'Assignation", "assignmentFilter": {"title": "Filtre d'Assignation", "all": "Toutes les Réservations", "self": "Auto-Assignées Seulement", "none": "Aucune"}, "partnerFilterTitle": "Filtre de Partenaire", "partnerFilter": {"title": "Filtre de Partenaire", "all": "Toutes les Réservations", "self": "Partenaires Personnels Seulement", "none": "Aucune"}, "errorFetchingRoles": "Échec du chargement des rôles", "roleFiltersUpdated": "Filtres de rôle mis à jour avec succès", "errorUpdatingRoleFilters": "Échec de la mise à jour des filtres de rôle", "configureRoleFilters": "Configurer les Filtres de Rôle", "role": "R<PERSON><PERSON>", "noRolesFound": "<PERSON><PERSON><PERSON> rôle trouvé", "configureFilters": "Configurer les Filtres", "statusCodes": "Codes de Statut", "errorFetchingUsers": "Échec du chargement des utilisateurs", "userFiltersUpdated": "Filtres utilisateur mis à jour avec succès", "errorUpdatingUserFilters": "Échec de la mise à jour des filtres utilisateur", "searchUsers": "Rechercher des utilisateurs par nom ou email...", "configureUserFilters": "Configurer les Filtres Utilisateur", "user": "Utilisa<PERSON>ur", "viewEffectiveFilters": "Voir les Filtres Effectifs", "hasCustomFilters": "A des Filtres Personnalisés", "inheritingFromRoles": "Hérite des Rôles", "effectiveFilters": "Filtres Effectifs", "effectiveFiltersDescription": "Voir les filtres d'accès aux réservations combinés pour {{userName}}", "inheritedFromRoles": "Hérités des Rôles", "userSpecificOverrides": "Remplacements Spécifiques à l'Utilisateur", "finalEffectiveFilters": "Filtres Effectifs Finaux", "filterMergingLogic": "Logique de Fusion des Filtres", "mergingRule1": "Codes de statut : Union de tous les codes de statut des rôles", "mergingRule2": "Accès aux succursales : Paramètre le plus permissif des rôles", "mergingRule3": "Filtre d'assignation : Paramètre le plus restrictif des rôles", "mergingRule4": "Filtre de partenaire : Paramètre le plus restrictif des rôles", "mergingRule5": "Les filtres spécifiques à l'utilisateur remplacent les paramètres de rôle lorsqu'ils sont définis", "mergingRule6": "Colonnes disponibles : Union de toutes les colonnes des rôles", "errorFetchingEffectiveFilters": "Échec du chargement des filtres effectifs", "noEffectiveFiltersData": "Aucune donnée de filtres effectifs disponible", "effectiveResult": "Résultat Effectif", "noRoleFilters": "Aucun filtre de rôle défini", "userOverrides": "Remplacements Utilisateur", "allStatuses": "Tous les statuts", "configure": "Configurer", "customFilters": "Filtres Personnalisés", "viewEffective": "Voir Effectif", "showingUsers": "Affichage de {{start}} à {{end}} sur {{total}} utilisateurs", "pageOf": "Page {{page}} sur {{pages}}", "noUsers": "Aucun utilisateur trouvé", "noUsersFound": "Aucun utilisateur trouvé correspondant à votre recherche", "noRoles": "<PERSON><PERSON><PERSON> rôle trouvé", "availableColumns": "Colonnes Disponibles", "availableColumnsFilter": "Filtre des Colonnes Disponibles", "columnsSelected": "Colonnes Sélectionnées", "allColumns": "Toutes les Colonnes", "moreColumns": "colonnes supplémentaires"}, "newDashboard": {"selectDateRange": "Sélectionner une plage de dates", "compareAgentTypeMismatch": "Les paramètres de comparaison doivent utiliser le même type d'agent que les filtres (Agent ou Vendeur)", "loadingTitle": "Chargement du Nouveau Tableau de Bord", "loadingMessage": "Récupération des informations du tableau de bord...", "errorLoading": "Erreur lors du chargement du nouveau tableau de bord :", "filters": "Filtres", "compare": "Comparer", "branches": "Succursales", "chooseBranches": "Choisir les succursales", "allBranchesSelected": "Toutes les succursales sélectionnées", "branchesSelected": "succursale(s) sélectionnée(s)", "searchBranches": "Rechercher des succursales...", "allBranches": "Toutes les Succursales", "years": "<PERSON><PERSON>", "selectYear": "Sélectionner l'Année", "month": "<PERSON><PERSON>", "selectMonth": "Sélectionner le Mois", "agent": "Agent", "chooseUser": "Choisir un utilisateur", "agentPAP": "Agent (PAP)", "seller": "<PERSON><PERSON><PERSON>", "selectUser": "Sélectionner l'Utilisateur", "selectedUser": "Utilisateur Sélectionné", "searchUsers": "Rechercher des utilisateurs...", "allUsers": "Tous les Utilisateurs", "dateRange": "Plage de <PERSON>s", "quickSelect": "Sélection Rapide", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "tomorrow": "<PERSON><PERSON><PERSON>", "thisWeek": "<PERSON><PERSON>", "last7Days": "7 Derniers Jours", "last30Days": "30 Derniers Jours", "thisMonth": "<PERSON>", "lastMonth": "<PERSON><PERSON>", "customRange": "P<PERSON>", "clearFilters": "Effacer les Filtres", "loading": "Chargement...", "applyFilters": "Appliquer les Filtres", "selectCustomDateRange": "Sélectionner une Plage de Dates Personnalisée", "fromDate": "Date de Début", "toDate": "Date de Fin", "apply": "Appliquer", "vsPreviousMonth": "vs. mois précédent", "vsPreviousYear": "vs. an<PERSON><PERSON>", "presenceRate": "TAUX DE PRÉSENCE", "presenceRateTooltip": "Pourcentage de réservations avec statut de présence - calculé comme présence/réservations qui ont eu lieu (filtré par date de visite)", "salesCount": "VENTES (NOMBRE)", "salesCountTooltip": "Nombre de réservations qui ont abouti à une vente - filtré par date de visite (quand les visites ont réellement eu lieu)", "totalSalesAmount": "MONTANT TOTAL DES VENTES", "totalSalesAmountTooltip": "Montant total de toutes les ventes - filtré par date de visite (quand les visites ont réellement eu lieu)", "totalReservationsCreated": "TOTAL RÉSERVATIONS CRÉÉES", "totalReservationsCreatedTooltip": "Nombre total de réservations créées (réservées) dans la période sélectionnée - filtré par date de création", "salesByBranch": "Ventes par Succursale", "reservationsCreatedByBranch": "Réservations Créées par Succursale", "bestPerformingSeller": "<PERSON><PERSON><PERSON>", "topPAPPerformance": "Performance des Meilleurs PAP"}, "recontact": {"selectRecontactDate": "Sélectionner la Date de Recontact", "statusChangeDescription": "Changement de statut vers {{status}} pour {{customer}}. Veuillez sélectionner quand recontacter ce client.", "dateRequired": "La date de recontact est requise", "dateMustBeFuture": "La date de recontact doit être dans le futur", "selectDate": "Sélectionner une date", "processing": "Traitement en cours...", "transferSuccess": "Réservation transférée vers recontact avec succès", "transferError": "Échec du transfert de la réservation vers recontact", "selectStatus": "Sélectionner le Statut de Recontact", "selectStatusPlaceholder": "Choisir un statut...", "statusRequired": "Veuillez sélectionner un statut de recontact", "title": "Réservations à Recontacter", "description": "<PERSON><PERSON><PERSON> les réservations qui nécessitent un suivi", "noPermission": "Vous n'avez pas la permission d'accéder aux réservations à recontacter", "empty": "Aucune réservation à recontacter trouvée", "reservationsList": "Liste des Réservations à Recontacter", "searchPlaceholder": "Rechercher par nom, téléphone ou email...", "fetchError": "Erreur lors du chargement des réservations à recontacter", "statusUpdated": "Statut mis à jour avec succès", "statusUpdateError": "Erreur lors de la mise à jour du statut", "filters": {"status": "Statut", "dateRange": "Plage de Dates de Recontact", "allStatuses": "Tous les Statuts"}, "statuses": {"title": "Gestion des Statuts de Recontact", "description": "Gérer les options de statut pour les réservations à recontacter", "noPermission": "Vous n'avez pas la permission de gérer les statuts de recontact", "create": "C<PERSON>er un Statut", "createTitle": "Créer un Nouveau Statut", "editTitle": "Modifier le Statut", "listTitle": "Liste des Statuts", "empty": "Aucun statut trouvé", "fetchError": "Erreur lors du chargement des statuts", "saveError": "<PERSON><PERSON>ur lors de la sauvegarde du statut", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression du statut", "createSuccess": "Statut créé avec succès", "updateSuccess": "Statut mis à jour avec succès", "deleteSuccess": "Statut supprimé avec succès", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce statut?", "saveOrder": "Sauvegarder l'Ordre", "form": {"name": "Nom", "namePlaceholder": "Entrez le nom du statut", "code": "Code", "codePlaceholder": "Entrez le code du statut", "color": "<PERSON><PERSON><PERSON>", "order": "Ordre"}}, "convertToReservation": "Convertir en Réservation", "conversionSuccess": "Réservation de recontact convertie avec succès", "conversionError": "Échec de la conversion de la réservation de recontact", "table": {"viewOldReservation": "Voir l'ancienne réservation", "customer": "Client", "contact": "Contact", "recontactDate": "Date de Recontact", "status": "Statut", "originalVisit": "Visite Originale", "addedToRecontact": "Ajouté au Recontact", "actions": "Actions", "overdue": "En Retard"}, "actions": {"convert": "Convertir", "convertTooltip": "Convertir en réservation", "view": "Voir", "viewTooltip": "Voir la réservation convertie dans un nouvel onglet", "converted": "<PERSON><PERSON><PERSON>"}, "conversion": {"title": "Convertir en Réservation", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "validationError": "Erreur de Validation", "customerNameRequired": "Le nom du client est requis", "phoneRequired": "Le numéro de téléphone est requis", "branchRequired": "Veuillez sélectionner une succursale", "dateRequired": "<PERSON><PERSON><PERSON>z sélectionner une date", "timeSlotRequired": "Veuillez sélectionner un créneau horaire", "statusRequired": "Veuillez sélectionner un statut initial", "failedToFetchBranches": "Échec du chargement des succursales", "failedToFetchStatuses": "Échec du chargement des statuts", "failedToFetchAvailabilities": "Échec du chargement des disponibilités", "failedToConvert": "Échec de la conversion de la réservation de recontact", "customerNamePlaceholder": "Nom du client", "phoneNumberPlaceholder": "Numéro de téléphone", "emailPlaceholder": "Adresse e-mail", "postalCodePlaceholder": "Code postal", "cityPlaceholder": "Ville", "addressPlaceholder": "<PERSON><PERSON><PERSON>", "noBranchesFound": "Aucune succursale trouvée", "noBranchesAvailable": "Aucune succursale disponible", "loadingAvailability": "Chargement des disponibilités...", "selectDateTime": "Sélectionner Date et Heure", "noTimeSlotsAvailable": "Aucun créneau horaire disponible pour cette date", "spot": "place", "spots": "places", "full": "Complet"}, "oldReservation": {"title": "Détails de la Réservation Originale", "noData": "Aucune donnée de réservation originale disponible", "tabs": {"customerInfo": "Info Client", "appointment": "<PERSON><PERSON><PERSON>vous", "notes": "Notes", "messages": "Messages", "statusHistory": "Historique des Statuts"}, "customerInfo": {"primaryClient": "Client Principal", "companion": "Accompagnateur", "addressInfo": "Informations d'Adresse", "preferences": "Préférences", "name": "Nom", "phone": "Téléphone", "email": "E-mail", "address": "<PERSON><PERSON><PERSON>", "city": "Ville", "postalCode": "Code Postal", "allergies": "Allergies", "foodPreferences": "Préférences Alimentaires", "children": "<PERSON><PERSON><PERSON>", "ages": "Âges 0-5: {age0to5}, Â<PERSON> 6-12: {age6to12}, Âges 13-17: {age13to17}"}, "appointment": {"details": "<PERSON><PERSON><PERSON> Rendez-vous", "reservationInfo": "Info Réservation", "visitDate": "Date de Visite", "visitTime": "Heure de Visite", "type": "Type", "created": "<PERSON><PERSON><PERSON>", "lastUpdated": "Dernière Mise à Jour", "reservationId": "ID Réservation", "types": {"branch": "Succursale", "online": "En Ligne", "home": "<PERSON><PERSON><PERSON>", "family": "<PERSON><PERSON><PERSON>"}}, "notes": {"title": "Historique des Notes", "noNotes": "Aucune note disponible pour cette réservation", "unknownUser": "Utilisa<PERSON><PERSON>"}, "messages": {"title": "Historique des Messages", "noMessages": "Aucun message disponible pour cette réservation"}, "statusHistory": {"title": "Historique des Statuts", "noHistory": "Aucun historique de statut disponible pour cette réservation", "changedBy": "par ID Utilisateur: {userId}"}}, "indicator": {"fromRecontact": "De<PERSON>is Recontact", "originalCustomer": "Client Original:", "originalAppointment": "<PERSON><PERSON>-vous Original:", "previousStatus": "Statut Précédent:", "originalCreated": "Créé Originalement:", "viewDetails": "Voir Détails", "ariaLabel": "Réservation créée depuis un recontact"}}}