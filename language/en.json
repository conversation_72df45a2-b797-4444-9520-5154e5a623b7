{"accessDenied": {"title": "Access Denied", "description": "You do not have permission to access this page."}, "dateCalendar": {"selectDate": "Select Date", "year": "Year", "month": "Month", "cancel": "Cancel", "clear": "Clear"}, "sidebar": {"searchPlaceholder": "Search menu...", "adminDashboard": "Admin Dashboard", "conversationsNew": "Conversations (New)", "billing": "Billing", "delete": "Delete", "deleting": "Deleting...", "deleteSuccess": "Invoice deleted successfully", "confirmMassDelete": "Are you sure you want to delete these invoices? This action cannot be undone.", "auditLogs": "<PERSON><PERSON>", "notifications": "Notifications", "eventReportValidation": "Event Report Validation", "invoiceItemTypes": "Invoice Item Types", "dashboard": "Dashboard", "home": "Home", "users": "Users (old)", "categories": "Categories", "products": "Products", "orders": "Orders", "permissions": "Permissions", "roles": "Roles", "settings": "Settings", "branches": "Branches", "events": "Events", "calendar": "Calendar", "eventsList": "Events List", "eventsCalendar": "Calendar", "eventsDashboard": "Events", "eventsReports": "Event Reports", "eventTypes": "Event Types", "reservations": "Reservations", "allReservations": "All Reservations", "recontactReservations": "Recontact Reservations", "recontactStatuses": "Recontact Statuses", "attendance": "Attendance", "partners": "Enterprises", "regions": "Regions", "generalSettings": "General Settings", "appointmentsDisponibilities": "Plannings", "disponibilities": "Disponibilities", "calendars": "Calendars", "myCalendar": "My Calendar", "overview": "Overview", "allergies": "Allergies", "reservationStatuses": "Reservation Statuses", "translations": "Translations", "monthCalendar": "Month Calendar", "appointments": "Appointments", "foods": "Foods", "selectUser": "Select a user to manage their calendar", "affectations": "Affectations", "affectationsDev": "Affectations (New)", "affectationsList": "Affectations List", "conversations": "Conversations", "sms": "SMS", "serviceTypes": "Service Types", "sellerDashboard": "Seller Dashboard", "commissionTypes": "Commission Types", "commissions": "Commissions", "commissionsList": "Commissions List", "userCommissions": "User Commissions", "weeklyCommissions": "Weekly Commissions", "papDashboard": "PAP Dashboard", "emailDemo": "Email Integration", "requests": "Requests", "requestsPage": "Requests Management", "scheduledSms": "Scheduled SMS", "brevoTemplates": "Email Templates", "devUsers": "Users", "contractManagement": "Contract Management", "facturation": "Billing", "contactRequests": "Contact Requests", "allContactRequests": "All Contact Requests", "contactRequestsSources": "Contact Request Sources", "contacts": "Contacts", "deliveryAvailability": "Delivery Availability", "invitations": "Invitations", "reservationsAccess": "Reservations Access", "reservationsExperimental": "Reservations (Experimental)", "collectionHistory": "Collection History"}, "sms": {"title": "SMS Templates", "description": "Customize SMS notifications sent by the system", "reservation_assignment": "Reservation Assignment", "reservation_confirmation": "Reservation Confirmation", "reservation_reminder": "Reservation Reminder", "thank_client": "Thank You Message", "reservation_assignment_description": "Sent when a seller is assigned to a reservation", "reservation_confirmation_description": "Sent to confirm a new reservation", "reservation_reminder_description": "Sent as a reminder before an appointment", "thank_client_description": "Sent to thank clients after their visit", "reservation_assignment_template": "Hello {{sellerName}}, you have been assigned to a reservation for {{customerName}} on {{appointmentDate}}. Please login to the application for more details.", "reservation_confirmation_template": "Hello {{customerName}}, your reservation for {{appointmentDate}} has been confirmed. Thank you for your trust.", "reservation_reminder_template": "Reminder: you have an appointment scheduled for {{appointmentDate}}. In case of absence, please contact us.", "thank_client_template": "Hello {{customerName}}, thank you for your visit at {{branchName}}. We hope you enjoyed the experience and would be delighted to welcome you again soon!", "sendTestSMS": "Send Test SMS", "saveTemplates": "Save Templates", "alert": "Remember that SMS messages are limited to 160 characters per segment. Longer messages will be split into multiple segments (153 characters each). Variables will expand with actual data, so keep your templates concise.", "messages": {"saveAsTemplate": "Save as Template", "templateName": "Template Name", "enterTemplateName": "Enter a name for this template", "templateContent": "Template Content", "templateSaved": "Template saved successfully", "templateSaveFailed": "Failed to save template", "templateLoadFailed": "Failed to load templates", "templates": "My Templates"}, "newTemplateName": "New template name", "addTemplate": "Add Template", "triggerStatus": "Trigger Status", "selectStatus": "Select status", "delayHours": "Delay (hours)", "disabled": "Disabled", "availableVariables": "Available Variables", "noVariables": "No variables yet. Add from the list below.", "deleteTemplate": "Delete Template", "saveChanges": "Save Changes", "saveSuccess": "Saved!", "testSMSPrompt": "Enter phone number to send test SMS:", "testSMSSuccess": "Test SMS sent successfully", "testSMSError": "Failed to send test SMS", "addVariable": "Add Variable", "noVariablesLeft": "No variables left", "previewWithSample": "Preview with sample data", "templateWithVariables": "Template with Variables", "loading": "Loading...", "error": "Error", "empty": "No templates found.", "confirmDelete": "Are you sure you want to delete this template?", "fetchError": "Failed to fetch SMS templates", "updateError": "Failed to update template", "addError": "Failed to add template", "deleteError": "Failed to delete template", "statusFetchError": "Failed to fetch statuses", "schedulingMode": "Scheduling Mode", "exactHour": "Exact Hour (0-23)", "dayDelay": "Day Delay (days)", "addBranchVariable": "Add Branch Variable", "branchVariables": "Branch Variables", "noVariablesAvailable": "No variables available", "scheduled": {"title": "Scheduled SMS Messages", "description": "Manage your scheduled SMS messages", "refresh": "Refresh", "searchPlaceholder": "Search messages...", "customer": "Customer", "phone": "Phone", "template": "Template", "message": "Message", "status": "Status", "createdAt": "Created At", "scheduledAt": "Scheduled For", "actions": "Actions", "reschedule": "Reschedule", "cancel": "Cancel", "canceled": "Canceled", "failed": "Failed", "sent": "<PERSON><PERSON>", "pending": "Pending", "rescheduleTitle": "Reschedule Message", "rescheduleDescription": "Choose a new date and time for this message to be sent.", "scheduledDate": "Scheduled Date", "pickDate": "Pick a date", "confirmReschedule": "Confirm", "noMessagesFound": "No scheduled messages found", "messageRescheduled": "Message rescheduled successfully", "messageCanceled": "Message canceled successfully", "rescheduleError": "Failed to reschedule message", "cancelError": "Failed to cancel message", "cannotReschedule": "You can only reschedule pending or failed messages", "cannotCancel": "You cannot cancel a message that has already been sent", "messagesRefreshed": "Messages refreshed", "openReservation": "Open reservation"}}, "notifications": {"description": "Description", "allTime": "All Time", "filtersAndActions": "Filters and Actions", "allNotifications": "All Notifications", "manageAllNotifications": "Manage All Notifications", "successfulDeliveries": "Successful Deliveries", "failedDeliveries": "Failed Deliveries", "create": "Create", "statusBreakdown": "Status Breakdown", "targetScreen": "Target Screen", "body": "Message Body", "users": "Users", "customData": "Custom Data", "totalNotifications": "Total Notifications", "totalUsers": "Total Users", "delivered": "Delivered Notifications", "failed": "Failed", "title": "Notifications", "clearAll": "Clear All", "noNotifications": "No Notifications"}, "table": {"actions": "Actions", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "noResults": "No results found", "loading": "Loading...", "previous": "Previous", "next": "Next", "reset": "Reset", "filters": "Filters", "resetAllFilters": "Reset all filters", "columnToggle": "Toggle columns", "columnSettings": "Column settings are automatically saved", "columns": {"customerInfo_client1Name": "Customer Name", "guests": "Guests", "type": "Type", "branchId": "Branch", "status": "Status", "customerInfo_city": "City", "customerInfo_address": "Address", "customerInfo_phone": "Phone", "customerInfo_email": "Email", "partnerId": "From Partner", "assigned_user_id": "Assigned To", "preferences_visitDate": "Visit Date", "preferences_visitTime": "Visit Time", "createdAt": "Created At", "notes": "Notes", "messages": "Messages", "serviceTypes": "Service Types"}, "totalFilteredRows": "Total filtered reservations"}, "common": {"processing": "Processing...", "city": "City", "address": "Address", "clearFilters": "Clear Filters", "lastMonth": "Last Month", "lastWeek": "Last Week", "updating": "Updating...", "exporting": "Exporting ...", "exportExcel": "Export Excel", "pickDate": "Pick date", "saved": "Saved", "first": "First", "last": "Last", "filters": "Filters", "filtersDescription": "Filter the data based on specific criteria", "prev": "Previous", "subtotal": "Subtotal", "tax": "Taxes", "more": "More", "enabled": "Enabled", "disabled": "Disabled", "access": "Access", "thisMonth": "This Month", "thisWeek": "This Week", "thisYear": "This Year", "search": "Search", "create": "Create", "update": "Update", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "saving": "Saving...", "generating": "Generating...", "delete": "Delete", "edit": "Edit", "published": "Published", "draft": "Draft", "featured": "Featured", "all": "All", "none": "None", "yes": "Yes", "no": "No", "notProvided": "Not provided", "noPermission": "You do not have permission to access this page.", "translateAll": "Translate All", "accessDenied": "Access Denied", "accessDeniedMessage": "You don't have permission to access this page. Please contact your administrator if you believe this is a mistake.", "goBack": "Go Back", "returnHome": "Return to Home", "actions": "Actions", "showing": "Showing", "of": "of", "perPage": "per page", "columns": "Columns", "pickADate": "Pick a date", "hide": "<PERSON>de", "show": "Show", "saveChanges": "Save Changes", "reset": "Reset", "apply": "Apply", "error": "Error", "success": "Success", "warning": "Warning", "id": "ID", "capacity": "Capacity", "unknown": "unknown", "na": "n/a", "created": "created", "updated": "updated", "deleted": "deleted", "next": "Next", "previous": "Previous", "back": "Back", "openMenu": "Open menu", "somethingWentWrong": "Something went wrong", "noData": "No data available", "loading": "Loading...", "time": "Time", "showAllDates": "Show All Dates", "dateRange": "Date Range", "today": "Today", "branch": "Branch", "selectBranch": "Select branch", "name": "Name", "email": "Email", "phone": "Phone", "phoneNumber": "Phone Number", "password": "Password", "permissions": "Permissions", "newPasswordOptional": "New Password (Optional)", "pressShiftEnterToSend": "Press Shift+Enter to send", "panLeft": "Pan Left", "panRight": "Pan Right", "enterFullscreen": "Enter fullscreen", "exitFullscreen": "Exit fullscreen", "comingSoon": "Coming soon...", "total": "Total", "selectEndDate": "Select end date", "pickEndDate": "Please select an end date", "showingPage": "Showing page", "page": "Page", "clear": "Clear", "redirecting": "Redirecting...", "restore": "Rest<PERSON>", "selected": "Selected", "noResults": "No results", "errorFetchingData": "Error fetching data", "close": "Close", "items": "items", "basicInfo": "Basic Info", "showDeleted": "Show Deleted", "branchRolesInfo": "Branch Roles Information", "linkCopied": "<PERSON>d", "copied": "<PERSON>pied", "createdAt": "Created At", "updatedAt": "Updated At", "retry": "Re<PERSON> Again", "refresh": "Refresh", "dashboard": "Dashboard", "users": "Users", "logout": "Logout", "login": "<PERSON><PERSON>", "branches": "Branches", "reservations": "Reservations", "settings": "Settings", "information": "Information", "view": "View", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "day": "Day", "days": {"short": {"sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat"}, "full": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}}, "months": {"full": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "short": {"jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec"}}, "status": "Status", "type": "Type", "date": "Date", "minute": "Minute", "debugInfo": "Debug Information", "duplicate": "Duplicate", "clickToSort": "Click to sort", "createdFrom": "Created From", "createdTo": "Created To", "home": "Home", "admin": "Admin"}, "errors": {"duplicatePhone": "A reservation with this phone number already exists.", "failedToFetchUsers": "Failed to fetch users"}, "login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "signIn": "Sign in", "signingIn": "Signing in...", "partnerPortalMessage": "Are you a Partner or Agent? Please use the dedicated Partner Portal to access your account.", "partnerErrorTitle": "Partner Account Detected", "partnerErrorMessage": "Partner accounts must use the designated Partner Portal to access the system. Please contact your account manager for more information.", "visitPartnerPortal": "Go to Partner Portal"}, "support": {"title": "Support", "subtitle": "Get help with AMQ Partners Platform", "contactInfo": "Contact Information", "email": "Email Support", "emailAddress": "<EMAIL>", "phone": "Phone Support", "phoneNumber": "**************", "businessHours": "Business Hours", "businessHoursText": "Monday to Friday, 9:00 AM - 5:00 PM EST", "faq": "Frequently Asked Questions", "faqQuestion1": "How do I reset my password?", "faqAnswer1": "You can reset your password by clicking the 'Forgot Password' link on the login page and following the instructions sent to your email.", "faqQuestion2": "How do I schedule an appointment?", "faqAnswer2": "Navigate to the Appointments section in your dashboard and click 'New Appointment' to schedule a new appointment with available time slots.", "faqQuestion3": "How do I view my commissions?", "faqAnswer3": "Go to the Commissions section in your dashboard to view your commission history and current status.", "faqQuestion4": "Who can I contact for technical issues?", "faqAnswer4": "For technical issues, please contact our support <NAME_EMAIL> or call our support line during business hours.", "troubleshooting": "Troubleshooting", "troubleshootingText": "If you're experiencing issues with the platform, try the following steps:", "troubleshootingStep1": "Clear your browser cache and cookies", "troubleshootingStep2": "Ensure you're using a supported browser (Chrome, Firefox, Safari, Edge)", "troubleshootingStep3": "Check your internet connection", "troubleshootingStep4": "Try logging out and logging back in", "troubleshootingStep5": "Contact support if the issue persists", "additionalResources": "Additional Resources", "userGuide": "User Guide", "userGuideText": "Access our comprehensive user guide for detailed instructions on using the platform.", "systemStatus": "System Status", "systemStatusText": "Check the current status of our services and any ongoing maintenance.", "feedback": "<PERSON><PERSON><PERSON>", "feedbackText": "We value your feedback! Please let us know how we can improve your experience with the AMQ Partners Platform."}, "privacyPolicy": {"title": "Privacy Policy", "lastUpdated": "Last updated: December 2024", "introduction": "Introduction", "introductionText": "AMQ Partners Platform ('we', 'our', or 'us') is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our platform.", "informationWeCollect": "Information We Collect", "personalInformation": "Personal Information", "personalInformationText": "We may collect personal information that you provide directly to us, including but not limited to:", "personalInfoItem1": "Name and contact information (email, phone number, address)", "personalInfoItem2": "Account credentials and authentication information", "personalInfoItem3": "Professional information (job title, company, branch affiliation)", "personalInfoItem4": "Communication preferences and history", "usageInformation": "Usage Information", "usageInformationText": "We automatically collect certain information about your use of our platform:", "usageInfoItem1": "Device information (IP address, browser type, operating system)", "usageInfoItem2": "Usage patterns and interaction data", "usageInfoItem3": "Log files and system activity", "usageInfoItem4": "Cookies and similar tracking technologies", "howWeUseInformation": "How We Use Your Information", "useInformationText": "We use the collected information for the following purposes:", "useInfoItem1": "Provide and maintain our platform services", "useInfoItem2": "Process transactions and manage appointments", "useInfoItem3": "Communicate with you about your account and services", "useInfoItem4": "Improve our platform and develop new features", "useInfoItem5": "Ensure security and prevent fraud", "useInfoItem6": "Comply with legal obligations", "informationSharing": "Information Sharing and Disclosure", "sharingText": "We do not sell, trade, or otherwise transfer your personal information to third parties except in the following circumstances:", "sharingItem1": "With your explicit consent", "sharingItem2": "To service providers who assist in our operations", "sharingItem3": "When required by law or legal process", "sharingItem4": "To protect our rights, property, or safety", "dataSecurity": "Data Security", "dataSecurityText": "We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.", "dataRetention": "Data Retention", "dataRetentionText": "We retain your personal information only for as long as necessary to fulfill the purposes outlined in this Privacy Policy, unless a longer retention period is required or permitted by law.", "yourRights": "Your Rights", "rightsText": "Depending on your location, you may have the following rights regarding your personal information:", "rightsItem1": "Access to your personal information", "rightsItem2": "Correction of inaccurate information", "rightsItem3": "Deletion of your personal information", "rightsItem4": "Restriction of processing", "rightsItem5": "Data portability", "rightsItem6": "Objection to processing", "cookies": "Cookies and Tracking Technologies", "cookiesText": "We use cookies and similar technologies to enhance your experience, analyze usage patterns, and provide personalized content. You can control cookie settings through your browser preferences.", "thirdPartyServices": "Third-Party Services", "thirdPartyText": "Our platform may contain links to third-party websites or integrate with third-party services. We are not responsible for the privacy practices of these third parties.", "childrenPrivacy": "Children's Privacy", "childrenPrivacyText": "Our platform is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.", "changes": "Changes to This Privacy Policy", "changesText": "We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the 'Last updated' date.", "contact": "Contact Us", "contactText": "If you have any questions about this Privacy Policy, please contact us at:", "contactEmail": "<EMAIL>", "contactAddress": "AMQ Partners Platform Privacy Team"}, "rdvs": {"title": "<PERSON><PERSON><PERSON>vous", "addRdv": "<PERSON>d <PERSON>-vous", "editRdv": "<PERSON>vous", "deleteRdv": "Delete Rendez-vous", "restore": "<PERSON><PERSON>-vous", "showDeleted": "Show deleted", "deleteWarning": "Are you sure you want to delete this rendez-vous? This action can be undone later.", "fields": {"firstName": "First Name", "lastName": "Last Name", "address": "Address", "postalCode": "Postal Code", "phone": "Phone", "date_rdv": "Appointment Date", "hour": "Appointment Time", "selectHour": "Select time", "status": "Status", "createdAt": "Created At", "deletedAt": "Deleted At", "selectStatus": "Select status"}, "status": {"label": "Status", "pending": "Pending", "confirmed": "Confirmed", "cancelled": "Cancelled", "completed": "Completed"}, "messages": {"created": "<PERSON><PERSON><PERSON><PERSON><PERSON> created successfully", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON> updated successfully", "deleted": "<PERSON><PERSON><PERSON><PERSON><PERSON> deleted successfully", "restored": "<PERSON><PERSON><PERSON><PERSON><PERSON> restored successfully", "fetchError": "Failed to fetch rendez-vous", "saveError": "Failed to save rendez-vous", "deleteError": "Failed to delete rendez-vous", "restoreError": "Failed to restore rendez-vous"}}, "products": {"title": "Products", "addProduct": "Add Product", "editProduct": "Edit Product", "deleteProduct": "Delete Product", "name": "Name", "price": "Price", "quantity": "Quantity", "category": "Category", "status": "Status", "image": "Image", "description": "Description", "selectCategory": "Select category", "makeVisible": "Make this product visible to customers", "showFeatured": "Show this product in featured sections"}, "users": {"assignedBranches": "Assigned Branches", "beneficiary": "Beneficiary", "branches": "Branches", "roleSpecificInfo": "Role-Specific Information", "deleted": "Deleted", "title": "Users", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "changeRole": "Change Role", "changeRoleTitle": "Change User Role", "currentRole": "Current Role", "noRole": "No role assigned", "selectNewRole": "Select new role", "changeRoleWarning": "Changing a user's role may require additional information. The user will retain their standard information (name, email, etc.) but you'll need to provide role-specific details.", "fillRoleInfo": "Fill in additional information for this role", "branchAdminRoleWarning": "As a Branch Admin, you can only modify roles for branches you have access to. Role changes will only apply to your branches.", "branchRolesRequired": "Select a branch-related role (Ad<PERSON>, Agent, or Seller) to assign branches", "name": "Name", "email": "Email", "role": "Role", "status": "Status", "lastLogin": "Last Login", "createdAt": "Created At", "actions": "Actions", "password": "Password", "confirmPassword": "Confirm Password", "selectRole": "Select role", "active": "Active", "inactive": "Inactive", "deleteConfirm": "Are you sure you want to delete this user?", "deleteWarning": "This action cannot be undone.", "search": "Search users...", "filter": "Filter users", "filterByRole": "Filter by role", "filterByStatus": "Filter by status", "allRoles": "All roles", "allStatuses": "All statuses", "noUsers": "No users found", "userCreated": "User created successfully", "userUpdated": "User updated successfully", "userDeleted": "User deleted successfully", "errorCreating": "Error creating user", "errorUpdating": "Error updating user", "errorDeleting": "Error deleting user", "roles": "Roles", "directPermissions": "Direct Permissions", "description": "Manage users and their access rights", "statusDescription": "User will be able to access the system when active", "addUserDescription": "Create a new user account", "editUserDescription": "Update user details", "phone": "Phone", "noUsersIncludingDeleted": "No users found, including deleted ones", "userRestored": "User restored successfully", "confirmDelete": "Are you sure you want to delete this user?", "basicInfo": "Basic Info", "showDeleted": "Show Deleted", "branchRolesInfo": "Branch Roles Information", "invitationLinkCopied": "Invitation link copied to clipboard", "copyLink": "Copy Link", "partnerCompany": "Partner Company", "selectPartnerCompany": "Select a partner company", "partnerCompanyHelp": "Select the partner company this user will be associated with", "checkSpecimenUpload": "Check Specimen Upload", "checkSpecimenUploadHelp": "Please upload a clear image or PDF of a voided check for payment processing", "additionalDocumentsUpload": "Additional Documents Upload", "additionalDocumentsUploadHelp": "Upload any additional required documents such as contracts, certifications, or identification", "missingRequiredFields": "Missing Required Fields", "birthDate": "Birth Date", "address": "Address", "city": "City", "postalCode": "Postal Code", "socialAssurance": "Social Insurance Number", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "taxInfo": {"isTaxable": "Tax Status", "taxType": "Tax Type"}, "partnerId": "Partner Company"}, "settings": {"translations": "Translations"}, "branches": {"title": "Branches", "viewPlan": "View plan", "plan": "Plan", "addBranch": "Add Branch", "editBranch": "Edit Branch", "deleteBranch": "Delete Branch", "name": "Name", "postalCode": "Postal Code", "address": "Address", "phone": "Phone Number", "automatedPhone": "Automated Phone", "primaryRequestPhone": "Primary Request Phone", "backupRequestPhone": "Backup Request Phone", "noPhone": "No phone number", "responsible": "Administrator", "actions": "Actions", "deleteConfirm": "Are you sure you want to delete this branch?", "deleteWarning": "This action cannot be undone.", "search": "Search branches...", "noBranches": "No branches available", "branchCreated": "Branch created successfully", "branchUpdated": "Branch updated successfully", "branchDeleted": "Branch deleted successfully", "errorCreating": "Error creating branch", "errorUpdating": "Error updating branch", "errorDeleting": "Error deleting branch", "errorFetching": "Error fetching branches", "city": "City", "email": "Email", "emailPlaceholder": "<EMAIL>", "region": "Region", "selectRegion": "Select region", "province": "Province", "users": "Users", "selectBranch": "Select branch", "searchBranches": "Search branches...", "branchCount": "{{count}} branches", "noBranchesFound": "No branches found matching your search", "restrictions": "Restrictions", "usersFilteredByBranch": "Users are filtered by selected branches", "noUsersInSelectedBranches": "No users found in the selected branches", "showDeleted": "Show deleted branches", "deleted": "Deleted", "setAvailability": "Set Availability Limit", "setAvailabilityLimit": "Set Branch Availability Limit", "availabilityUpdated": "Availability limit updated successfully", "availabilityUpdateFailed": "Failed to update availability limit", "availabilityRemoved": "Availability limit removed successfully", "availabilityRemoveFailed": "Failed to remove availability limit", "limitedAvailability": "Limited", "availableUntil": "Available until", "reservationLimit": "Reservation limit until", "selectedDate": "Selected date", "removeLimit": "Remove limit", "generalInfo": "General Info", "contactInfo": "Contact Info", "staffInfo": "Staff", "branchAdmins": "Branch Administrators", "branchAgents": "Branch Agents", "noAdminsAvailable": "No administrators available", "noAgentsAvailable": "No agents available", "userRole": {"responsible": "Administrator", "agent": "Agent", "seller": "<PERSON><PERSON>"}, "requestConfig": "Request Config", "requestPrompt": "Request Prompt", "requestPromptPlaceholder": "Enter the prompt that will be shown to clients when making a request...", "requestTags": "Request Tags", "addTag": "Add a new tag"}, "events": {"accessDenied": {"title": "Access Denied", "description": "You do not have permission to access event management features."}, "title": "Events", "viewAndManage": "View and manage events", "createEvent": "Create Event", "createNewEvent": "Create New Event", "createNewEventDescription": "Set up a new event with personnel assignments", "creating": "Creating...", "createSuccess": "Event created successfully", "filtersAndSearch": "Filters & Search", "searchPlaceholder": "Search events...", "filterByStatus": "Filter by status", "filterByDate": "Filter by date", "sortBy": "Sort by", "allStatuses": "All Statuses", "allDates": "All Dates", "upcoming": "Upcoming", "thisMonth": "This Month", "nextMonth": "Next Month", "pastEvents": "Past Events", "noEventsFound": "No events found", "noEventsMatchFilters": "No events match your current filters.", "noEventsFoundFor": "No events found for", "eventsFound": "events found.", "clearFilters": "Clear Filters", "loadingFormData": "Loading form data...", "selectEventType": "Select event type", "contactInfo": "Contact Information", "contactName": "Contact Name", "contactRole": "Contact Role", "contactPhone": "Contact Phone", "agents": "Agents", "startTime": "Start Time", "contactPhonePlaceholder": "Enter contact phone number", "responsibleUser": "Responsible User", "requiredAgents": "Required Agents", "assignedAgents": "Assigned Agents", "clientGoalDescription": "Client serving goal", "addEvent": "Add Event", "editEvent": "Edit Event", "deleteEvent": "Delete Event", "name": "Name", "startDate": "Start Date", "endDate": "End Date", "location": "Location", "description": "Description", "calendar": {"noPermissionAccess": "You do not have permission to access the events calendar", "noPermissionCreate": "You do not have permission to create events", "searchEvents": "Search events...", "clearAll": "Clear All"}, "selectCalendar": "Select calendar", "selectLocation": "Select location", "selectStartDate": "Select start date", "selectEndDate": "Select end date", "type": "Type", "selectType": "Select event type", "partner": "Partner", "selectPartner": "Select partner", "clientGoal": "Client Goal", "colorCodeByBranch": "Color code by branch", "today": "Today", "week": "Week", "month": "Month", "year": "Year", "selectBranch": "Select branch", "selectBranchPlaceholder": "Select branch", "dashboard": "Events Dashboard", "list": "Events List", "branch": "Branch", "date": "Date", "time": "Time", "capacity": "Capacity", "actions": "Actions", "noEvents": "No events found", "noEventsForDay": "No events scheduled for this day", "loading": "Loading events...", "error": "Error loading events", "pleaseSelectBranch": "Please select a branch to view events", "updateSuccess": "Event updated successfully", "deleteSuccess": "Event deleted successfully", "saveError": "Failed to save event", "deleteError": "Failed to delete event.", "deleteWarning": "Are you sure you want to delete this event? This action cannot be undone.", "endTime": "End Time", "pickDate": "Pick a date", "allStatus": "All Status", "free": "Free", "assigned": "Assigned", "completed": "Completed", "availableToAssign": "Available to Assign", "max": "max", "maxSupervisorsReached": "Maximum number of supervisors reached", "searchSupervisors": "Search supervisors...", "searchPapAgents": "Search PAP agents...", "searchCooks": "Search cooks...", "noSupervisorsFound": "No supervisors found", "noAvailableSupervisors": "No available supervisors", "noPapAgentsFound": "No PAP agents found", "noAvailablePapAgents": "No available PAP agents", "noCooksFound": "No cooks found", "noAvailableCooks": "No available cooks", "createdAt": "Created At", "selectDateRange": "Select date range", "allBranches": "All Branches", "allPartners": "All Partners", "allEventTypes": "All Event Types", "previousMonth": "Previous Month", "online": "Online", "home": "Home Visit", "family": "Family", "currentView": "Current View", "currentDate": "Current Date", "selectedBranch": "Selected Branch", "selectedPartner": "Selected Partner", "totalEvents": "Total Events", "branchesCount": "Branches Count", "eventTypesCount": "Event Types Count", "view": "View", "eventType": "Event Type", "add": "Add", "notes": "Notes", "confirmDelete": "Are you sure you want to delete this event? This action cannot be undone.", "deleted": "Event deleted successfully.", "statusScheduled": "Scheduled", "statusCompleted": "Completed", "statusCancelled": "Cancelled", "statusUpdatedSuccessfully": "Event status updated successfully", "status": {"label": "Status", "new": "New", "inProgress": "In Progress", "processingReport": "Processing Report", "awaitingValidation": "Awaiting Validation", "completed": "Completed", "cancelled": "Cancelled", "overdue": "Overdue", "pending": "Pending", "processing": "Processing", "submitted": "Submitted", "validated": "Validated"}, "sort": {"createdNewestFirst": "Created (Newest First)", "createdOldestFirst": "Created (Oldest First)", "dateEarliestFirst": "Date (Earliest First)", "dateLatestFirst": "Date (Latest First)", "nameAZ": "Name (A-Z)", "nameZA": "Name (Z-A)", "status": "Status", "location": "Location"}, "tabs": {"all": "All", "active": "Active", "reporting": "Reporting", "completed": "Completed", "overdue": "Overdue"}, "pagination": {"showing": "Showing", "to": "to", "of": "of", "events": "events"}, "selectUser": "Select user", "summary": {"title": "Summary", "activeEvents": "Active Events", "inReporting": "In Reporting", "completed": "Completed", "overdue": "Overdue"}, "card": {"papAgents": "PAP Agents", "ended": "Ended", "ago": "ago", "inProgress": "In progress", "starts": "Starts in", "branch": "Branch", "partner": "Partner", "type": "Type", "goal": "Goal", "clients": "clients", "personnel": "Personnel", "supervisors": "Supervisors", "cooks": "<PERSON><PERSON>", "noneAssigned": "None assigned", "report": "Report", "notes": "Notes", "viewDetails": "View Details", "editEvent": "Edit Event", "viewReport": "View Report", "createReport": "Create Report", "editReport": "Edit Report"}, "form": {"formCompletion": "Form completion", "supervisorRequired": "Exactly one supervisor is required", "savedSuccessfully": "Saved successfully!", "validationErrors": "Validation Errors", "formValues": "Form Values", "timePlaceholder": "HH:MM or HH:MM AM/PM", "noEventTypesFound": "No event types found", "noBranchesFound": "No branches found", "noPartnersFound": "No partners found", "noUsersFound": "No users found", "recurrent": "Recurrent", "repeatEvery": "Repeat every", "frequency": "Frequency", "frequencies": {"day": "Day", "week": "Week", "month": "Month", "year": "Year"}, "daysOfWeek": "Days of week", "dayOfMonth": "Day of month", "endDateInclusive": "End date (inclusive)", "eventDetails": "Event Details", "eventName": "Event Name", "enterEventName": "Enter event name", "location": "Location", "enterEventLocation": "Enter event location", "branch": "Branch", "selectBranch": "Select branch", "noBranchesAvailable": "No branches available", "personnelAssignment": "Personnel Assignment", "startDateTime": "Start Date & Time", "endDateTime": "End Date & Time", "selectTime": "Select time", "additionalDetails": "Additional Details", "expectedClients": "Expected number of clients", "additionalNotes": "Additional notes or instructions for the event", "enterContactName": "Enter contact name", "enterContactRole": "Enter contact role", "noPartnersAvailable": "No partners available", "noEventTypesAvailable": "No event types available", "supervisors": "Supervisors", "papAgents": "PAP Agents", "cooks": "<PERSON><PERSON>", "noSupervisorsAssigned": "No supervisors assigned", "noPapAgentsAssigned": "No PAP agents assigned", "noCooksAssigned": "No cooks assigned"}, "validation": {"viewDetails": "View Details", "eventNameRequired": "Event name is required", "branchRequired": "Branch is required", "partnerRequired": "Partner is required", "eventTypeRequired": "Event type is required", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "endDateAfterStart": "End date must be after start date", "startTimeRequired": "Start time is required", "endTimeRequired": "End time is required", "endTimeAfterStart": "End time must be after start time", "invalidTimeFormat": "Invalid time format. Use HH:MM or HH:MM AM/PM", "locationRequired": "Location is required", "supervisorRequired": "At least one supervisor is required", "exactlyOneSupervisorRequired": "Exactly one supervisor is required", "fixErrorsBeforeSubmit": "Please fix the form errors before submitting", "title": "Event Report Validation", "description": "Review and validate submitted event reports", "bulkActions": "Bulk Actions", "totalPending": "Total Pending", "urgent": "<PERSON><PERSON>", "normal": "Normal", "high": "High", "avgWaitTime": "Avg Wait Time", "searchPlaceholder": "Search reports...", "filterByPriority": "Filter by priority", "allPriorities": "All Priorities", "submissionOldest": "Submission (Oldest First)", "submissionNewest": "Submission (Newest First)", "priorityHighToLow": "Priority (High to Low)", "branchAZ": "Branch (A-Z)", "selectAll": "Select All", "deselectAll": "Deselect All", "noReportsFound": "No reports found", "noReportsMatchFilters": "No reports match your current filters.", "noReportsPendingValidation": "No reports are currently pending validation.", "supervisor": "Supervisor", "duration": "Duration", "review": "Review", "eventDataUnavailable": "Event data unavailable", "missingEventData": "Missing Event Data", "missingEventDataMessage": "This report has missing event data. Please contact an administrator.", "eventDataMissing": "Event data is missing", "cannotValidateWithoutEvent": "Cannot validate report without event information"}, "errors": {"noPermissionViewAll": "You do not have permission to view all events", "failedToLoad": "Failed to load events", "failedToUpdateStatus": "Failed to update event status", "failedToLoadFormData": "Failed to load form data", "failedToCreate": "Failed to create event"}, "editPage": {"title": "Edit Event", "description": "Modify event details and personnel assignments", "back": "Back", "saving": "Saving...", "saveChanges": "Save Changes", "eventDetails": "Event Details", "eventName": "Event Name", "eventNameRequired": "Event Name *", "enterEventName": "Enter event name", "branch": "Branch", "branchRequired": "Branch *", "selectBranch": "Select branch", "partner": "Partner", "partnerRequired": "Partner *", "selectPartner": "Select partner", "eventType": "Event Type", "eventTypeRequired": "Event Type *", "selectEventType": "Select event type", "startDate": "Start Date", "startDateRequired": "Start Date *", "endDate": "End Date", "endDateRequired": "End Date *", "location": "Location", "locationRequired": "Location *", "enterLocation": "Enter location", "clientServingGoal": "Client Serving Goal", "enterClientServingGoal": "Enter client serving goal", "notes": "Notes", "addNotesPlaceholder": "Add notes about the event...", "status": "Status", "personnelAssignment": "Personnel Assignment", "personnelAssignmentDescription": "Manage supervisors, PAPs, and cooks assigned to this event.", "cancel": "Cancel", "pleaseFixFormErrors": "Please fix the form errors before submitting", "failedToUpdateEvent": "Failed to update event", "failedToLoadEventDetails": "Failed to load event details", "failedToLoadFormData": "Failed to load form data"}, "reports": {"title": "Event Reports", "description": "Manage event reports and commission calculations", "searchPlaceholder": "Search reports...", "recentlyUpdated": "Recently Updated", "oldestUpdated": "Oldest Updated", "eventDateLatest": "Event Date (Latest)", "eventDateEarliest": "Event Date (Earliest)", "reports": "reports", "report": "report", "noReportsFound": "No reports found", "noReportsMatchFilters": "No reports match your current filters.", "reportsFound": "reports found.", "edit": {"title": "Edit Event Report", "description": "Edit event personnel, times, and submit report for validation", "backToReport": "Back to Report", "eventSummary": "Event Summary", "location": "Location", "branch": "Branch", "partner": "Partner", "reportStatus": "Report Status", "reportNotFound": "Report not found.", "noPermissionEdit": "You do not have permission to edit this report.", "cannotEditValidated": "This report cannot be edited as it has been validated.", "noPermissionEditReports": "You do not have permission to edit event reports", "noReportFound": "No report found for this event", "failedLoadEventData": "Failed to load event data", "noReportIdAvailable": "No report ID available", "failedLoadReportDetails": "Failed to load report details", "reportSavedSuccessfully": "Report saved successfully", "failedSaveReport": "Failed to save report", "saveFailed": "Save failed", "reportSubmittedValidation": "Report submitted for validation", "failedSubmitReport": "Failed to submit report", "validationFailed": "Validation failed"}, "details": {"title": "Event Report", "description": "Manage event personnel and submit report for validation", "back": "Back", "editReport": "Edit Report", "submitReport": "Submit Report", "retry": "Retry", "reportNotFound": "Report not found or you don't have permission to view it.", "noPermissionAccess": "You do not have permission to access event reports", "failedLoadEventData": "Failed to load event data", "failedLoadReport": "Failed to load report details", "reportSubmittedSuccess": "Report submitted for validation", "reportStatusUpdatedSuccess": "Report status updated successfully", "failedSubmitReport": "Failed to submit report", "failedUpdateStatus": "Failed to update report status", "validationFailed": "Validation failed", "actualEventDate": "Actual Event Date", "scheduledEventDate": "Scheduled Event Date", "reported": "✓ Reported", "branch": "Branch", "partner": "Partner", "reportStatus": "Report Status", "timesShownIn": "Times shown in", "actualEventTimes": "Actual Event Times", "actualEventTimesDescription": "The actual start and end times as reported by supervisors", "startTime": "Start Time", "endTime": "End Time", "actualEventTimesNotReported": "Actual event times have not been reported yet", "timesAvailableAfterEdit": "Times will be available after the report is edited and saved", "eventInformation": "Event Information", "eventName": "Event Name", "actualEventTimesReported": "Actual Event Times (Reported)", "notYetReported": "Not yet reported", "originalScheduledTimes": "Original Scheduled Times", "location": "Location", "commissionSummary": "Commission Summary", "totalCommissions": "Total Commissions", "supervisors": "Supervisors", "paps": "PAPs", "cooks": "<PERSON><PERSON>", "totalReservations": "Total Reservations", "timeRangesShownIn": "Time ranges shown in", "hoursWorked": "h worked", "percentageShare": "% share", "commissionEarned": "Commission earned", "perReservation": "/reservation", "linkedReservations": "Linked Reservations", "guests": "guests", "noReservationsLinked": "No reservations linked to this event.", "papCommission": "PAP Commission", "reportHistory": "Report History", "reportCreated": "Report Created", "reportProcessingStarted": "Report Processing Started", "reportSubmittedForValidation": "Report Submitted for Validation", "reportValidated": "Report Validated", "notes": "Notes", "validationNotes": "Validation Notes"}, "tabs": {"overview": "Overview", "personnel": "Personnel", "personnelCommissions": "Personnel & Commissions", "reservations": "Reservations", "history": "History"}, "form": {"eventTimes": "Event Times", "eventTimesDescription": "Adjust the actual event start and end times (displayed in {timezone})", "startDateTime": "Start Date & Time", "endDateTime": "End Date & Time", "date": "Date", "time": "Time", "selectDate": "Select date", "selectTime": "Select time", "startTimeInvalid": "Start time is invalid", "endTimeInvalid": "End time is invalid", "timezoneNote": "Times are displayed in your local timezone ({timezone})", "supervisors": "Supervisors", "supervisorsDescription": "Event supervisors (read-only)", "personnelTimeRanges": "Personnel & Time Ranges", "personnelDescription": "Manage PAP and Cook assignments with their working time ranges", "papAgents": "PAP Agents", "assigned": "Assigned", "available": "Available", "noPapsAssigned": "No PAPs assigned yet", "searchPaps": "Search PAPs...", "noPapsFound": "No PAPs found", "addPap": "Add PAP", "workingHours": "Working Hours", "from": "From", "to": "To", "cooks": "<PERSON><PERSON>", "noCooksAssigned": "No cooks assigned yet", "searchCooks": "Search cooks...", "noCooksFound": "No cooks found", "addCook": "Add <PERSON>", "percentage": "Percentage", "notes": "Notes", "notesPlaceholder": "Add any notes about the event...", "save": "Save", "saving": "Saving...", "submitForValidation": "Submit for Validation", "submitting": "Submitting...", "fixValidationErrors": "Please fix validation errors before submitting", "validationFailedDetails": "Validation failed: {details}", "papRemoved": "PAP removed from report", "cookRemoved": "<PERSON> removed from report", "papAdded": "PAP added to report", "cookAdded": "<PERSON> added to report", "invalidTimeData": "Invalid time data provided to slider", "invalidEventDuration": "Invalid event duration", "timeRange": "Time Range", "fullEvent": "Full Event", "firstHalf": "First Half", "secondHalf": "Second Half"}}, "details": {"title": "Event Details", "eventInformation": "Event Information", "dateTime": "Date & Time", "statusReport": "Status & Report", "eventStatus": "Event Status", "reportStatus": "Report Status", "reportValidationRequired": "Report Validation Required", "reportValidationMessage": "This event report has been submitted and is awaiting validation.", "validateReport": "Validate Report", "viewAllPendingReports": "View All Pending Reports", "metadata": "<PERSON><PERSON><PERSON>", "backToEvents": "Back to Events", "eventNotFound": "Event not found"}, "navigation": {"title": "Events", "expandSidebar": "Expand sidebar", "sections": {"events": "Events", "validation": "Validation"}, "allEvents": "All Events", "calendarView": "Calendar View", "createEvent": "Create Event", "myReports": "My Reports", "pendingReports": "Pending Reports", "eventReports": "Event Reports", "validateReport": "Validate Report"}, "workflow": {"title": "Workflow Status", "currentStatus": "Current Status:", "progress": "Progress", "reportStatusLabel": "Report Status:", "nextActions": "Next Actions:", "status": {"new": "New", "inProgress": "In Progress", "cancelled": "Cancelled", "processingReport": "Processing Report", "awaitingValidation": "Awaiting Validation", "completed": "Completed"}, "actions": {"startEvent": "Start Event", "beginReport": "Begin Report", "submitReport": "Submit Report", "validateReport": "Validate Report"}, "messages": {"statusUpdateSuccess": "Event status updated successfully", "statusUpdateFailed": "Failed to update event status", "reportSubmitSuccess": "Report submitted successfully", "reportSubmitFailed": "Failed to submit report", "validationFailed": "Validation failed", "waitingValidation": "Waiting for validation by authorized personnel", "eventCancelled": "This event has been cancelled"}, "reportStatus": {"pending": "Pending", "processing": "Processing", "submitted": "Submitted", "validated": "Validated"}}, "supervisors": "Supervisors", "overdue": "Overdue", "copyOf": "Copy of", "allSupervisors": "All Supervisors", "reportStatus": "Report Status", "allReports": "All Reports", "statuses": {"new": "New", "in_progress": "In Progress", "cancelled": "Cancelled", "processing_report": "Processing Report", "awaiting_validation": "Awaiting Validation", "done": "Done"}, "reportStatuses": {"pending": "Pending", "processing": "Processing", "submitted": "Submitted", "validated": "Validated", "overdue": "Overdue"}}, "reservations": {"notesAndMessages": {"title": "Notes and Messages"}, "title": "Reservations", "addReservation": "Add Reservation", "editReservation": "Edit Reservation", "deleteReservation": "Delete Reservation", "fetchError": "Failed to fetch reservations", "sortByCreation": "Sort by creation date", "description": "Manage and view all reservations", "showAllDates": "Show all dates", "allDates": "All Dates", "today": "Reservations Created Today", "viewingToday": "Viewing Today's Reservations", "todayFetchError": "Error fetching today's reservations", "showDeleted": "Show deleted", "new": "New Reservation", "selectStatus": "Select Status", "name": "Name", "event": "Event", "createdAt": "Created At", "messages": {"title": "Messages", "empty": "No messages yet. Add the first one!", "addMessage": "Add a message..."}, "confirmation": {"title": "Confirmation Method", "description": "Please select how the customer was contacted for confirmation", "types": {"sms": "SMS", "call": "Phone Call", "email": "Email"}}, "notes": {"title": "Notes", "empty": "No notes yet. Add the first one!", "addNote": "Add a note...", "deleteConfirm": "Are you sure you want to delete this note?", "deleteSuccess": "Note deleted successfully", "editSuccess": "Note updated successfully"}, "sell": {"title": "Selling Amount", "description": "Please enter the selling amount in CAD", "amountLabel": "Amount (CAD)", "invalidAmount": "Please enter a valid amount", "userLabel": "Assign User", "noUserSelected": "Please select a user"}, "confirmUser": {"title": "Assign User for Confirmation", "description": "Please assign a user before changing the status to confirmed", "userLabel": "Assign User", "noUserSelected": "Please select a user"}, "userAssignedAndStatusChanged": "User assigned and status changed successfully", "confirmUserRequired": "A user must be assigned before changing status to Confirmed", "status": {"pending": "Pending", "confirmed": "Confirmed", "cancelled": "Cancelled", "completed": "Completed"}, "filters": {"visitTime": "Visit Time"}, "reset": "Reset", "resetAllFilters": "Reset all filters", "branch": {"all": "All branches", "select": "Select a branch"}, "stats": {"noUserData": "No user data", "noTimeSlotData": "No time slot data"}, "totalReservations": "Total Reservations", "statusChangedByOther": "The reservation status was changed by another user.", "initialStatus": "Initial Status", "assignedUser": "Assigned User", "selectUser": "Select User", "statsTooltip": {"title": "Detailed Statistics", "totalSales": "Total sales", "rendezvousAssis": "Appointments", "totalConfirmed": "Total confirmed", "closingRate": "Closing rate", "salesBadge": "Sales", "presenceBadge": "Appts", "confirmedBadge": "Confirmed", "closingRateBadge": "Closing", "salesExplanation": "Reservations with sales statuses", "presenceExplanation": "Reservations with presence statuses", "confirmedExplanation": "Reservations with presence statuses + present + absent", "closingRateExplanation": "Sales rate compared to appointments (Sales ÷ Appointments)"}}, "commissions": {"searchPlaceholder": "Search by user or customer...", "customer": "Customer", "from": "From", "to": "To", "title": "Commissions", "subtitle": "Manage and approve commission payments", "filters": "Filters", "filtersDescription": "Filter commissions by status and user", "status": "Status", "allStatuses": "All Statuses", "approved": "Approved", "pending": "Pending", "applyFilters": "Apply Filters", "apply": "Apply", "reset": "Reset", "listCaption": "List of commissions", "saveBonusesFirst": "Failed to save bonuses. Please try again.", "user": "User", "reservation": "Reservation", "commissionType": "Commission Type", "type": "Commission Type", "amount": "Amount", "created": "Created", "actions": "Actions", "noCommissions": "No commissions found", "approve": "Approve", "approveTitle": "Approve Commission", "showing": "Showing", "of": "of", "commissions": "commissions", "totalCount": "Total", "commissionsFound": "commissions found", "perPage": "per page", "totalCommissions": "Total Commissions", "approveDescription": "Are you sure you want to approve this commission of {{amount}} for {{user}}?", "approveSuccess": "Commission approved successfully", "rejectSuccess": "Commission rejected successfully", "error": "Failed to update commission", "recipient": "Recipient", "recipientDescription": "Select who will receive this commission", "partner": "Partner", "papBranch": "PAP Branch", "assignedUser": "Assigned User", "allRecipients": "All Recipients", "allTypes": "All Types", "createdAt": "Created At", "selectDate": "Select date", "amountRange": "Amount Range", "min": "Min", "max": "Max", "exactAmount": "Exact Amount", "enterAmount": "Enter amount", "exact": "Exact", "activeFilters": "Active Filters", "viewReservation": "View Reservation", "startDate": "Start Date", "endDate": "End Date", "totalUsers": "Total Users", "activeUsers": "Active users with commissions", "allCommissions": "All commissions in period", "totalAmount": "Total Amount", "allPayments": "All payments in period", "userName": "User Name", "email": "Email", "commissionCount": "Commissions", "noUsers": "No users available", "view": "View Commissions", "exportExcel": "Export Excel", "generatingExcel": "Generating Excel file...", "excelGenerated": "Excel file generated successfully", "excelError": "Failed to generate Excel file", "approvedCommissions": "Approved Commissions", "pendingCommissions": "Pending Commissions", "noCommissionsFound": "No commissions found", "date": "Date", "client": "Client", "sales": "Sales", "commissionStats": "Commission Stats", "totalCommissionAmount": "Total Commission Amount", "topCommissionUser": "Top User by Commission Amount", "unknownUser": "Unknown User", "count": "Count", "bonus": "Bonus", "approveAll": "Approve All", "confirmApproveAllTitle": "Approve All Commissions", "confirmApproveAllDesc": "Are you sure you want to approve all {{count}} unapproved commissions currently loaded? This action cannot be undone.", "allApproved": "All commissions approved successfully!", "failedApproveAll": "Failed to approve all commissions.", "approvingAll": "Approving all commissions...", "bulkApproveUser": "Approve All Pending", "confirmBulkApproveUserTitle": "Approve All Pending Commissions for User", "confirmBulkApproveUserDesc": "Are you sure you want to approve all {{count}} pending commissions for this user? This action cannot be undone.", "allUsers": "All Users", "usersSelected": "Users", "searchUsers": "Search users...", "deselectAll": "Deselect All", "noUsersFound": "No users found", "selectAll": "Select All", "selectDay": "Select Day", "selectColumn": "Select Column", "selectRow": "Select Row", "syncEdits": "Sync Edits", "unsyncEdits": "Unsync Edits", "resetFilter": "Reset Filter"}, "partners": {"createdAt": "Created At", "postalCodePlaceholder": "Enter postal code", "eventsCount": "Events", "title": "Enterprises", "addPartner": "Add Enterprise", "editPartner": "Edit Enterprise", "deletePartner": "Delete Enterprise", "deleteWarning": "Are you sure you want to delete this enterprise? This action cannot be undone.", "partnerDeleted": "Enterprise deleted successfully", "partnerUpdated": "Enterprise updated successfully", "partnerCreated": "Enterprise created successfully", "errorDeleting": "Failed to delete enterprise", "errorSaving": "Failed to save enterprise", "errorFetching": "Failed to fetch enterprises", "errorLoadingBranches": "Failed to load branches", "name": "Name", "postalCode": "Postal Code", "namePlaceholder": "Partner name", "branch": "Branch", "selectBranch": "Select branch", "selectPartner": "Select partner", "email": "Email", "emailPlaceholder": "<EMAIL>", "phone": "Phone", "phonePlaceholder": "Phone number", "address": "Address", "addressPlaceholder": "123 Main St", "city": "City", "cityPlaceholder": "City name", "country": "Country", "countryPlaceholder": "Country name", "manageUsers": "Manage Users", "owners": "Owners", "agents": "Agents", "errorLoadingUsers": "Failed to load users", "errorUpdatingUsers": "Failed to update partner users", "usersUpdated": "Partner users updated successfully", "users": "Users", "tabs": {"general": "General Information", "contact": "Contact Information", "fiscal": "Fiscal Information", "additional": "Additional Information"}, "abbreviatedName": "Abbreviated Name", "abbreviatedNamePlaceholder": "Enter abbreviated name", "local": "Local", "localPlaceholder": "Enter local (optional)", "province": "Province", "selectProvince": "Select a province", "fax": "Fax", "faxPlaceholder": "Enter fax number (optional)", "internalPhone": "Internal Phone", "internalPhonePlaceholder": "Enter internal phone (optional)", "internationalFax": "International Fax", "internationalFaxPlaceholder": "Enter international fax (optional)", "website": "Website", "websitePlaceholder": "Enter website URL (optional)", "divisionId": "Division ID", "divisionIdPlaceholder": "Enter division ID", "language": "Language", "selectLanguage": "Select a language", "french": "French", "english": "English", "federalTax": "Federal Tax", "federalTaxPlaceholder": "Enter federal tax rate", "provincialTax": "Provincial Tax", "provincialTaxPlaceholder": "Enter provincial tax rate", "lastModified": "Last Modified", "notModifiedYet": "Not modified yet", "companyLogo": "Company Logo", "uploadLogo": "Upload Logo", "uploading": "Uploading...", "uploadSuccess": "Logo uploaded successfully", "uploadError": "Failed to upload logo", "invalidFileType": "Invalid file type. Only JPEG, PNG and WebP images are allowed.", "fileTooLarge": "File size too large. Maximum size is 5MB.", "searchPlaceholder": "Search by name, address, phone, email...", "allBranches": "All Branches", "noPartners": "No enterprises found", "generalTab": "General", "contactTab": "Contact", "brandingTab": "Branding", "logoUploading": "Please wait, logo is still uploading", "urlSlug": "URL Slug", "urlSlugPlaceholder": "e.g., my-partner-company", "urlSlugHelp": "This will be used in URLs like .../login/[slug] for partner-specific login pages. Only lowercase letters, numbers, and hyphens are allowed.", "loginHtml": "Custom Login HTML", "loginHtmlPlaceholder": "Enter custom HTML for the login page...", "loginHtmlHelp": "Custom HTML content to customize the appearance of the /login/[urlSlug] page. Use template variables to position login elements.", "templateVariables": "Template Variables", "templateVariablesDescription": "Use these variables to position and customize login elements in your HTML template:", "requiredVariables": "Required Variables", "optionalVariables": "Optional Variables", "emailInputDescription": "Email input field (required)", "passwordInputDescription": "Password input field (required)", "submitButtonDescription": "Form submit button (required)", "partnerNameDescription": "Company name", "partnerLogoDescription": "Company logo (if configured)", "errorMessageDescription": "Login error message display area", "stylingOptions": "Styling Options", "stylingOptionsDescription": "You can use inline CSS or CSS classes. Variables will be replaced with appropriate functional elements.", "exampleTemplate": "Example Template", "eventsFor": "Events for"}, "regions": {"title": "Regions", "addRegion": "Add Region", "editRegion": "Edit Region", "deleteRegion": "Delete Region", "name": "Name", "code": "Code", "actions": "Actions", "deleteConfirm": "Are you sure you want to delete this region?", "deleteWarning": "This action cannot be undone.", "search": "Search regions...", "noRegions": "No regions found", "regionCreated": "Region created successfully", "regionUpdated": "Region updated successfully", "regionDeleted": "Region deleted successfully", "errorCreating": "Error creating region", "errorUpdating": "Error updating region", "errorDeleting": "Error deleting region", "namePlaceholder": "Region name", "codePlaceholder": "Region code (e.g. QC)", "codeHelp": "Must be exactly 2 uppercase letters"}, "appointmentsDisponibilities": {"title": "Appointments Disponibilities", "users": "Users", "selectUser": "Select User", "previousWeek": "Previous Week", "nextWeek": "Next Week", "selectAllWeek": "Select All Week", "deselectAllWeek": "Deselect All Week", "saveAll": "Save All Disponibilities", "saving": "Saving...", "selectAll": "Select All", "deselectAll": "Deselect All", "noAppointments": "No appointments", "max": "Max", "userBranches": "User Branches"}, "allergies": {"title": "Allergies", "list": "Allergies List", "addAllergy": "Add Allergy", "editAllergy": "Edit Allergy", "deleteAllergy": "Delete Allergy", "name": "Name", "name_en": "English Name", "actions": "Actions", "noAllergies": "No allergies found", "loading": "Loading allergies...", "error": "Error loading allergies", "createSuccess": "Allergy created successfully", "updateSuccess": "Allergy updated successfully", "deleteSuccess": "Allergy deleted successfully", "saveError": "Failed to save allergy", "deleteError": "Failed to delete allergy", "deleteWarning": "Are you sure you want to delete this allergy? This action cannot be undone.", "errorLoading": "Error loading allergies", "confirmDelete": "Confirm Deletion", "confirmDeleteText": "Are you sure you want to delete this allergy? ", "update": "Update", "add": "Add", "retry": "Retry"}, "recontact": {"title": "Recontact Reservations", "description": "Manage reservations that require follow-up contact", "noPermission": "You don't have permission to access recontact reservations", "empty": "No recontact reservations found", "reservationsList": "Recontact Reservations List", "searchPlaceholder": "Search by name, phone or email...", "fetchError": "Error loading recontact reservations", "statusUpdated": "Status updated successfully", "statusUpdateError": "Error updating status", "filters": {"status": "Status", "dateRange": "Recontact Date Range", "allStatuses": "All Statuses"}, "statuses": {"title": "Recontact Status Management", "description": "Manage status options for recontact reservations", "noPermission": "You don't have permission to manage recontact statuses", "create": "Create Status", "createTitle": "Create New Status", "editTitle": "Edit Status", "listTitle": "Status List", "empty": "No statuses found", "fetchError": "Error loading statuses", "saveError": "Error saving status", "deleteError": "Error deleting status", "createSuccess": "Status created successfully", "updateSuccess": "Status updated successfully", "deleteSuccess": "Status deleted successfully", "confirmDelete": "Are you sure you want to delete this status?", "saveOrder": "Save Order", "form": {"name": "Name", "namePlaceholder": "Enter status name", "code": "Code", "codePlaceholder": "Enter status code", "color": "Color", "order": "Order"}}, "selectRecontactDate": "Select Recontact Date", "statusChangeDescription": "Changing status to {{status}} for {{customer}}. Please select when to recontact this customer.", "dateRequired": "Recontact date is required", "dateMustBeFuture": "Recontact date must be in the future", "selectDate": "Select a date", "processing": "Processing...", "transferSuccess": "Reservation transferred to recontact successfully", "transferError": "Failed to transfer reservation to recontact", "selectStatus": "Select Recontact Status", "selectStatusPlaceholder": "Choose a status...", "statusRequired": "Please select a recontact status", "convertToReservation": "Convert to Reservation", "conversionSuccess": "Recontact reservation converted successfully", "conversionError": "Failed to convert recontact reservation", "table": {"viewOldReservation": "View Old Reservation", "customer": "Customer", "contact": "Contact", "recontactDate": "Recontact Date", "status": "Status", "originalVisit": "Original Visit", "addedToRecontact": "Added to Recontact", "actions": "Actions", "overdue": "Overdue"}, "actions": {"convert": "Convert", "convertTooltip": "Convert to reservation", "view": "View", "viewTooltip": "View converted reservation in new tab", "converted": "Converted"}, "conversion": {"title": "Convert to Reservation", "success": "Success", "error": "Error", "validationError": "Validation Error", "customerNameRequired": "Customer name is required", "phoneRequired": "Phone number is required", "branchRequired": "Please select a branch", "dateRequired": "Please select a date", "timeSlotRequired": "Please select a time slot", "statusRequired": "Please select an initial status", "failedToFetchBranches": "Failed to fetch branches", "failedToFetchStatuses": "Failed to fetch statuses", "failedToFetchAvailabilities": "Failed to fetch availabilities", "failedToConvert": "Failed to convert recontact reservation", "customerNamePlaceholder": "Customer name", "phoneNumberPlaceholder": "Phone number", "emailPlaceholder": "Email address", "postalCodePlaceholder": "Postal code", "cityPlaceholder": "City", "addressPlaceholder": "Address", "noBranchesFound": "No branches found", "noBranchesAvailable": "No branches available", "loadingAvailability": "Loading availability...", "selectDateTime": "Select Date and Time", "noTimeSlotsAvailable": "No time slots available for this date", "spot": "spot", "spots": "spots", "full": "Full"}, "oldReservation": {"title": "Original Reservation Details", "noData": "No original reservation data available", "tabs": {"customerInfo": "Customer Info", "appointment": "Appointment", "notes": "Notes", "messages": "Messages", "statusHistory": "Status History"}, "customerInfo": {"primaryClient": "Primary Client", "companion": "Companion", "addressInfo": "Address Information", "preferences": "Preferences", "name": "Name", "phone": "Phone", "email": "Email", "address": "Address", "city": "City", "postalCode": "Postal Code", "allergies": "Allergies", "foodPreferences": "Food Preferences", "children": "Children", "ages": "Ages 0-5: {age0to5}, Ages 6-12: {age6to12}, Ages 13-17: {age13to17}"}, "appointment": {"details": "Appointment Details", "reservationInfo": "Reservation Info", "visitDate": "Visit Date", "visitTime": "Visit Time", "type": "Type", "created": "Created", "lastUpdated": "Last Updated", "reservationId": "Reservation ID", "types": {"branch": "Branch", "online": "Online", "home": "Home", "family": "Family"}}, "notes": {"title": "Notes History", "noNotes": "No notes available for this reservation", "unknownUser": "Unknown User"}, "messages": {"title": "Messages History", "noMessages": "No messages available for this reservation"}, "statusHistory": {"title": "Status History", "noHistory": "No status history available for this reservation", "changedBy": "by User ID: {userId}"}}, "indicator": {"fromRecontact": "From Recontact", "originalCustomer": "Original Customer:", "originalAppointment": "Original Appointment:", "previousStatus": "Previous Status:", "originalCreated": "Original Created:", "viewDetails": "View Details", "ariaLabel": "Reservation created from recontact"}}, "reservationStatuses": {"isSalesStatus": "Is Sales Status", "isPresenceStatus": "Is Presence Status", "isRecontactStatus": "Is Recontact Status", "title": "Reservation Statuses", "addStatus": "Add Status", "editStatus": "Edit Status", "deleteStatus": "Delete Status", "name": "Name", "name_en": "English Name", "code": "Code", "color": "Color", "order": "Display Order", "excludeFromAffectations": "Exclude from Affectations/Appointments", "excludeFromAffectationsDescription": "When enabled, reservations with this status will not appear in the affectations and appointments pages", "affectationsColumn": "Excluded from Affectations", "actions": "Actions", "noStatuses": "No statuses found", "loading": "Loading statuses...", "error": "Error loading statuses", "createSuccess": "Status created successfully", "updateSuccess": "Status updated successfully", "deleteSuccess": "Status deleted successfully", "saveError": "Failed to save status", "deleteError": "Failed to delete status", "deleteWarning": "Are you sure you want to delete this status? This may affect existing reservations."}, "foods": {"title": "Foods", "list": "Foods List", "addFood": "Add Food", "editFood": "Edit Food", "deleteFood": "Delete Food", "name": "Name", "name_en": "English Name", "actions": "Actions", "noFoods": "No foods found", "loading": "Loading foods...", "error": "Error loading foods", "createSuccess": "Food created successfully", "updateSuccess": "Food updated successfully", "deleteSuccess": "Food deleted successfully", "saveError": "Failed to save food", "deleteError": "Failed to delete food", "deleteWarning": "Are you sure you want to delete this food? This action cannot be undone."}, "general": {"saveChanges": "Save Changes", "copy": "<PERSON><PERSON> to Next 3 Weeks", "copyTo3Weeks": "Save Current & Copy to 3 Weeks", "error": "Error", "loading": "Loading...", "reloading": "Reloading..."}, "calendar": {"days": {"mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "months": {"jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec", "january": "January", "february": "February", "march": "March", "april": "April", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "selectDate": "Select Date", "availabilityWarning": {"title": "Monthly Availability Requirement Not Met", "description": "You are required to have availability for at least 3 weeks in the current month. Please add more availability times."}, "monthlyRequirement": "Monthly Availability Requirement", "weeksWithAvailability": "weeks with availability", "editingDisabled": "Editing Disabled", "monthlyRequirementSaved": "Monthly availability requirement has been met and saved. Only administrators can modify.", "filled": "Already filled", "canEditFutureDays": "You can edit future days that haven't been filled yet", "dateAlreadyFilled": "This date already has availability and cannot be modified", "weeksRequirementNotMet": "You must provide availability for the current week and 2 weeks ahead", "weeksRequired": "3 weeks required", "updated": "Calendar Updated", "availabilitySaved": "Your availability has been saved", "weekReset": "Week Reset", "weekSlotsCleared": "Current week slots have been cleared. Click Save Changes to apply.", "saveFailed": "Failed to save your availability", "resetFailed": "Failed to reset current week slots", "past": "Past date"}, "appointments": {"modal": {"title": "Manage Appointment Slots", "loadingSlots": "Loading appointment slots...", "quickTemplates": "Quick Templates", "addNewSlot": "Add New Slot", "startTime": "Start Time", "selectStartTime": "Select start time", "duration": "Duration", "selectDuration": "Select duration", "oneHour": "1 hour", "twoHours": "2 hours", "addSlot": "Add Slot", "defaultValues": "Default Values", "defaultCapacity": "Default Capacity", "defaultOnline": "Default Online", "defaultHome": "Default Home", "maxFamilySize": "Max Family Size", "applyToAllSlots": "Apply to All Slots", "timeline": "Timeline", "clearAll": "Clear All"}, "templates": {"morning1h": "Morning 1h Slots (11-12)", "afternoon1h": "Afternoon 1h Slots (13-17)", "evening1h": "Evening 1h Slots (17-21)", "fullDay1h": "Full Day 1h Slots (11-21)", "fullDay2hEven": "Full Day 2h Even (12-14, 14-16...)", "fullDay2hOdd": "Full Day 2h <PERSON> (11-13, 13-15...)", "pairHours": "Full Day 2h Even", "oddHours": "Full Day 2h Odd"}, "deleteDialog": {"confirmDeletion": "Confirm Deletion", "deleteWarningSingular": "This action would delete 1 existing appointment slot", "deleteWarningPlural": "This action would delete existing appointment slots", "affectExisting": "Deleting slots may affect existing appointments. This action cannot be undone.", "slotsToDelete": "Slots that would be deleted", "timeRange": "Time Range", "noDetailedInfo": "No detailed slot information available", "differentTimeRanges": "Slots with different time ranges would be removed. This may affect any existing reservations for these time slots.", "confirmDelete": "Yes, Delete Slots", "blockedDueToReservations": "You cannot delete these slots because there are existing reservations for them. Please manage or move the affected reservations first.", "safeToDelete": "These slots have no reservations and can be safely deleted.", "hasReservations": "Has Reservations", "capacityBelowReservationsWarning": "Some slots have a new capacity below the number of existing reservations. This may cause overbooking. Please review.", "oldCapacity": "Old Capacity", "newCapacity": "New Capacity", "reservationCount": "Reservations"}, "errors": {"failedToCheckDeletedSlots": "Failed to check for deleted slots", "checkingDeletedSlots": "Error checking for deleted slots", "noDaySelected": "No day selected", "inHandleSubmit": "Error in handleSubmit", "occurred": "An error occurred", "failedToUpdate": "Failed to update slots", "updatingSlots": "Error updating slots", "invalidStartTimeOrDuration": "Please enter a valid start time and duration", "invalidTimeFormat": "Please enter a valid time in HH:MM format", "overlappingSlot": "This time slot overlaps with an existing slot", "slotBeforeElevenAM": "Slot creation is not allowed before 11:00 AM", "slotAfterNinePM": "Slot creation is not allowed after 9:00 PM"}, "logs": {"slotsWillBeDeleted": "Slots will be deleted if continuing", "handleSubmitResult": "<PERSON>le submit result", "submittingChanges": "Submitting changes...", "updatingSlots": "Updating slots for", "operationComplete": "Operation complete", "submissionResult": "Submission result"}, "success": {"slotsUpdated": "Slots updated successfully"}, "sellers": {"present": "Present", "partiallyAvailable": "Partially Available", "absent": "Absent"}}, "newreservation": {"timeslotFull": "Full", "timeslotFullWarning": "This timeslot is full. You can still proceed, but capacity has been reached.", "title": "New Reservation", "description": "Create a new reservation by filling in the details below.", "buttons": {"create": "Create Reservation", "creating": "Creating...", "cancel": "Cancel"}, "clientInformation": {"title": "Client Information", "description": "Enter the client's personal information.", "firstName": "Guest 1", "secondName": "Guest 2", "secondNameOptional": "Guest 2", "email": "Email", "phone": "Phone", "alternativePhone": "Alternative Phone", "postalCode": "Postal Code", "invalidPostalCode": "Invalid postal code", "city": "City", "address": "Address", "mainClient": "Main Client"}, "preferences": {"title": "Preferences", "description": "Client's preferences for their visit.", "preferredLanguage": "Preferred Language", "serviceTypes": {"title": "Service Type Selection", "description": "Select the service type for each person attending (optional)."}, "adultServiceType": "Adult Service Type", "childServiceType": "Child Service Type", "selectServiceType": "Select service type", "allergies": {"title": "Allergies", "searchPlaceholder": "Search allergies..."}, "foods": {"title": "Favorite Foods", "searchPlaceholder": "Search foods...", "maxSelected": "Maximum {0} foods can be selected"}, "children": {"title": "Children", "description": "Will children be present during the visit?", "agesTitle": "Age Groups", "age0to5": "Ages 0-5", "age6to12": "Ages 6-12", "age13to17": "Ages 13-17", "childNumber": "Child {0}", "teenNumber": "Teen {0}", "years": "years"}}, "branchAndTime": {"timeSlot": "Time Slot", "selectDate": "Select a date", "pickDate": "Pick a date", "title": "Branch and Visit Time", "description": "Select preferred branch and visit time.", "branch": "Branch", "date": "Date", "time": "Time", "searchBranches": "Search branches...", "distance": "{0} km", "selectBranch": "Select Branch"}, "agentAssignment": {"title": "Agent Assignment", "description": "Assign this reservation to an agent.", "assignedUser": "Agent", "selectUser": "Select Agent"}, "notes": {"title": "Notes", "description": "Add internal notes about this reservation.", "placeholder": "Enter your notes here..."}}, "editReservation": {"title": "Edit Reservation", "description": "Update the details for this reservation.", "buttons": {"cancel": "Cancel", "save": "Save Changes", "saving": "Saving..."}}, "branchesAdmin": {"title": "Branch Admin", "form": {"steps": {"generalInfo": "General Information", "taxInfo": "Tax Information", "documents": "Documents", "permissions": "Permissions"}, "fields": {"name": "Name", "nameTooltip": "Enter the full name of the branch admin", "email": "Email", "password": "Password", "newPassword": "New Password (leave empty to keep current)", "passwordHelp": "Leave the password field empty to keep the current password unchanged.", "branch": "Branch", "phone": "Phone", "taxable": "Taxable", "taxableTooltip": "Indicate if this branch is subject to taxation", "tvq": "TVQ", "tvqTooltip": "Enter TVQ percentage (0-100)", "tps": "TPS", "taxationType": "Type of taxation", "checkSpecimen": "Check Specimen", "checkSpecimenTooltip": "Upload a check specimen for payments", "otherDocuments": "Other supporting documents", "otherDocumentsTooltip": "Add other relevant documents"}, "validation": {"nameRequired": "Name must be at least 2 characters", "emailInvalid": "Invalid email address", "passwordLength": "Password must be at least 6 characters", "branchRequired": "Branch is required", "phoneRequired": "Phone number must be at least 8 characters", "tvqRange": "TVQ must be between 0 and 100", "tpsRange": "TPS must be between 0 and 100", "taxFieldsRequired": "When taxable is set to 'yes', valid TVQ, TPS, and taxation type are required"}, "buttons": {"next": "Next", "previous": "Previous", "create": "Create Branch Admin", "update": "Update Branch Admin"}, "messages": {"validationFailed": "Please check all fields and try again.", "stepValidationFailed": "Please complete all required fields before proceeding.", "successCreate": "Branch admin created successfully", "successUpdate": "Branch admin updated successfully", "error": "Failed to save branch admin"}}, "taxationTypes": {"TPS_TVQ": "GST/QST", "TPS_ONLY": "GST only", "TVQ_ONLY": "QST only", "NO_TAX": "Non-taxable"}}, "branchesAgent": {"title": "Branch Agent", "form": {"fields": {"name": "Name", "email": "Email", "phoneNumber": "Phone Number", "password": "Password", "newPassword": "New Password", "passwordHelp": "Leave empty to keep current password", "branch": "Branch"}, "buttons": {"create": "Create Branch Agent", "update": "Update Branch Agent"}, "messages": {"successCreate": "Branch agent created successfully", "successUpdate": "Branch agent updated successfully"}}}, "partner": {"title": "Partner", "form": {"fields": {"name": "Name", "email": "Email", "password": "Password", "newPassword": "New Password", "passwordHelp": "Leave empty to keep current password", "companyName": "Company Name", "phone": "Phone"}, "buttons": {"create": "Create Partner", "update": "Update Partner"}, "messages": {"successCreate": "Partner created successfully", "successUpdate": "Partner updated successfully", "error": "Failed to manage partner"}}, "calendar": {"availabilityStatus": "Availability Status", "availabilityComplete": "Availability Complete", "availabilityIncomplete": "Availability Incomplete", "weeksFilledCount": "Weeks filled", "weeksRequired": "3 weeks required", "monthlyRequirementDescription": "You need to provide availability for 3 weeks", "requirementMet": "Requirement met! You've filled 3 weeks of availability", "consecutiveWeeksRequired": "You must fill availability for the current week and 2 weeks ahead"}}, "agentPartner": {"title": "Partner Agent", "form": {"fields": {"name": "Name", "email": "Email", "password": "Password", "newPassword": "New Password", "passwordHelp": "Leave empty to keep current password", "partner": "Partner", "phone": "Phone"}, "buttons": {"create": "Create Agent Partner", "update": "Update Agent Partner"}, "messages": {"successCreate": "Agent partner created successfully", "successUpdate": "Agent partner updated successfully", "error": "Failed to manage agent partner"}}}, "seller": {"title": "<PERSON><PERSON>", "form": {"fields": {"name": "Name", "email": "Email", "phoneNumber": "Phone Number", "password": "Password", "newPassword": "New Password", "passwordHelp": "Leave empty to keep current password", "branch": "Branch", "selectBranch": "Select branch"}, "buttons": {"create": "Create Seller", "update": "Update Seller"}, "messages": {"successCreate": "<PERSON><PERSON> created successfully", "successUpdate": "<PERSON><PERSON> updated successfully", "error": "Failed to manage seller"}}, "calendar": {"availabilityStatus": "Availability Status", "availabilityComplete": "Availability Complete", "availabilityIncomplete": "Availability Incomplete", "weeksFilledCount": "Weeks filled", "weeksRequired": "3 weeks required", "monthlyRequirementDescription": "You need to provide availability for 3 weeks", "requirementMet": "Requirement met! You've filled 3 weeks of availability", "consecutiveWeeksRequired": "You must fill availability for the current week and 2 weeks ahead"}}, "roles": {"pap": "PAP", "searchPermissions": "Search permissions...", "noPermissionsFound": "No permissions found.", "title": "Roles", "addRole": "Add Role", "editRole": "Edit Role", "deleteRole": "Delete Role", "addNewRole": "Add New Role", "name": "Name", "description": "Manage roles and their permissions", "descriptionPlaceholder": "Role description", "namePlaceholder": "Admin", "noRoles": "No roles found", "updateRole": "Update Role", "createRole": "Create Role", "searchRoles": "Search roles...", "deleteConfirm": "Are you absolutely sure?", "deleteWarning": "This action cannot be undone. This will permanently delete the role and remove it from any users that have it.", "roleCreated": "Role created successfully", "roleUpdated": "Role updated successfully", "roleDeleted": "Role deleted successfully", "errorSaving": "Failed to save role", "errorDeleting": "Failed to delete role", "errorFetching": "Failed to fetch roles", "errorFetchingPermissions": "Failed to fetch permissions", "validation": {"nameRequired": "Name is required", "permissionsRequired": "Select at least one permission"}, "responsible": "Administrator", "agent": "Agent", "superAdmin": "Super Admin", "branchesAdmin": "Branches Admin", "branchesAgent": "Branches Agent", "partner": "Partner", "agentPartner": "Agent Partner", "seller": "<PERSON><PERSON>", "telephoniste": "Telephoniste", "hiddenRole": "Hidden Role"}, "permissions": {"title": "Permissions", "addPermission": "Add Permission", "editPermission": "Edit Permission", "deletePermission": "Delete Permission", "addNewPermission": "Add New Permission", "name": "Name", "code": "Code", "description": "Description", "descriptionPlaceholder": "Allows viewing the list of users", "namePlaceholder": "View Users", "codePlaceholder": "VIEW_USERS", "noPermissions": "No permissions found", "updatePermission": "Update Permission", "createPermission": "Create Permission", "searchPermissions": "Search permissions...", "deleteConfirm": "Are you absolutely sure?", "deleteWarning": "This action cannot be undone. This will permanently delete the permission and remove it from any roles that use it.", "permissionCreated": "Permission created successfully", "permissionUpdated": "Permission updated successfully", "permissionDeleted": "Permission deleted successfully", "errorSaving": "Failed to save permission", "errorDeleting": "Failed to delete permission", "errorFetching": "Failed to fetch permissions", "validation": {"nameRequired": "Name is required", "codeRequired": "Code is required"}}, "invoice-signing": {"title": "Invoice Signing", "loading": "Loading...", "loadingInvoice": "Loading invoice...", "loadingInvoiceData": "Loading invoice data...", "error": "Error", "invalidLink": "Invalid Link", "invalidLinkMessage": "This invoice signing link is invalid or has expired.", "linkExpired": "This link has expired and is no longer valid for signing.", "linkExpiryNotice": "This link was first opened on {date} and will expire in {time}.", "signedSuccessfully": "Invoice Signed Successfully", "signedSuccessfullyMessage": "Thank you for signing the invoice. This invoice was signed on {date}.", "yourSignature": "Your Signature:", "downloadSignedInvoice": "Download Signed Invoice", "downloading": "Downloading...", "signYourInvoice": "Sign Your Invoice", "reviewAndSignMessage": "Please review the invoice and sign in the designated area below. Once signed, you'll be able to download a copy of the signed invoice.", "cancel": "Cancel", "signAndSubmit": "Sign & Submit", "signing": "Signing...", "downloadSignedPdf": "Download Signed PDF", "confirmSignature": "Confirm Signature", "confirmSignatureMessage": "Are you sure you want to sign this invoice? This action cannot be undone.", "processing": "Processing...", "drawSignatureHere": "Draw your signature here", "clear": "Clear", "noInvoiceData": "No invoice data available", "noInvoiceDataMessage": "Unable to load invoice details. Please try again later.", "missingSignature": "Missing Signature", "missingSignatureMessage": "Please draw your signature before submitting", "success": "Success", "signatureSubmittedSuccessfully": "Signature submitted successfully", "pdfDownloadedSuccessfully": "PDF downloaded successfully", "errorSubmittingSignature": "An error occurred while submitting the signature", "errorGeneratingPdf": "An error occurred while generating the PDF", "errorFetchingInvoiceData": "An error occurred while fetching the invoice data", "errorValidatingToken": "An error occurred while validating the token", "invoiceSignedSuccessfully": "Invoice signed successfully", "failedToValidateToken": "Failed to validate token", "failedToSubmitSignature": "Failed to submit signature", "failedToGeneratePdf": "Failed to generate PDF", "failedToFetchInvoiceData": "Failed to fetch invoice data", "invoiceDataNotAvailable": "Invoice data not available", "debugInfo": "Debug Information", "invoice": "INVOICE", "issuer": "Issuer", "client": "Client", "billingDetails": "Billing Details", "description": "Description", "date": "Date", "quantity": "Qty", "unitPrice": "Unit Price", "price": "Price", "subtotal": "Subtotal", "total": "Total", "signature": "Signature:", "signHere": "Sign here", "tel": "Tel:"}, "dashboard": {"userCreationStats": "", "home": "Home", "title": "Dashboard", "availableDashboards": "Available Dashboards", "adminDashboard": "Admin Dashboard", "adminDashboardDescription": "Manage users, branches, and access system settings", "sellerDashboard": "Seller Dashboard", "sellerDashboardDescription": "View your sales, appointments, and performance metrics", "papDashboard": "PAP Dashboard", "papDashboardDescription": "Manage PAP-specific functions and view performance", "reservations": "Reservations", "reservationsDescription": "Manage reservations and customer appointments", "eventReportValidation": "Event Report Validation", "eventReportValidationDescription": "Review and validate submitted event reports", "emptyDashboard": "No dashboard available for your role", "dateRange": "Date Range", "tabs": {"overview": "Overview", "analytics": "Analytics", "performance": "Performance", "management": "Management"}, "user": "User", "unassigned": "Unassigned", "sales": "Sales", "salesRate": "Sales Rate", "presence": "Presence", "presenceCount": "Presence Count", "presenceRate": "Presence Rate", "salesToPresenceRate": "Sales to Presence Rate", "conversions": "Conversions", "conversionRate": "Conversion Rate", "totalReservations": "Total Reservations", "totalSales": "Total Sales", "cancelledSales": "Cancelled Sales", "averageSale": "Average Sale", "todayReservations": "Reservations Created Today", "userPerformanceStats": "PAP Performance Statistics", "assignedUserPerformanceStats": "Seller Performance Statistics", "bestPerformingUser": "Best Performing PAP", "bestPerformingAssignedUser": "Best Performing Seller", "branchLegend": "Branch Legend", "debug": "Debug", "noData": "No data available", "dailyReservationsChart": "Daily Reservations", "reservationsDayTimeslot": "Reservations by Day & Timeslot", "totalCommissions": "Total Commissions", "totalCommissionAmount": "Total Commission Amount", "topCommissionUser": "Top Commission User", "commissionStats": "Commission Statistics", "recentCommissions": "Recent Commissions", "monthlyCommissions": "Monthly Commissions", "errorFetching": "Error fetching data", "weekdayDistribution": "Weekday Distribution", "dailyCreationStats": "Daily Creation Stats", "dataType": "Data Type", "creationDate": "Creation Date", "visitDate": "Visit Date", "groupBy": "Group By", "groupByDay": "Day", "groupByWeek": "Week", "groupByMonth": "Month", "creationsWithCreationDate": "Reservations by creation date", "creationsWithVisitDate": "Reservations by visit date", "creationDateTotal": "Total (creation date)", "visitDateTotal": "Total (visit date)", "matchesSummaryCards": "Matches summary cards", "selectBranchPrompt": "Please select a branch to view dashboard data", "salesByTimeslot": "Sales by Time Slot"}, "charts": {"totalReservationsCreated": "Total Reservations Created", "conversionFunnel": "Conversion Funnel", "filteredByVisitDate": "(Filtered by <PERSON><PERSON><PERSON> Date)", "funnelTotalReservations": "Total Reservations", "funnelConfirmedPresence": "Confirmed (Presence)", "funnelSales": "Sales", "conversionRates": "Conversion Rates", "reservationToConfirmed": "Reservation → Confirmed:", "confirmedToSales": "Confirmed → Sales:", "overallReservationToSales": "Overall (Reservation → Sales):", "conversionPercent": "conversion", "loadingFunnelData": "Loading funnel data...", "reservationsByMonth": "Reservations Created by Month", "filteredByCreationDate": "(Filtered by Creation Date)", "reservationsCreated": "Reservations Created", "compareReservationsCreated": "Compare Reservations Created", "created": "Created:", "loadingReservationsData": "Loading reservations data...", "noReservationsData": "No reservations data available", "salesByMonth": "Sales by Month", "filteredBySaleDate": "(Filtered by Sale Date)", "salesCount": "Sales Count", "compareSalesCount": "Compare Sales Count", "loadingSalesData": "Loading sales data...", "noSalesData": "No sales data available"}, "conversations": {"authorLabel": "by {author}", "automatic": "Automatic", "dev": {"archivedTitle": "Conversation archived", "archivedDesc": "has archived this conversation", "showArchives": "Show archives", "newMessage": "New message", "archiveBeforeDate": "Archive before this date", "searchPlaceholder": "Search contacts...", "selectConversation": "Select a conversation to view messages", "typeMessage": "Write a message...", "send": "Send", "sending": "Sending...", "searchContacts": "Search contacts...", "archiveSelected": "Archive selected", "conunarchiveSelected": "Unarchive selected", "connewMessage": "New message", "conshowArchives": "Show archives", "conhideArchives": "Hide archives", "conarchived": "Archived", "conarchiving": "Archiving...", "conarchiveBeforeDate": "Archive before this date", "consearchPlaceholder": "Search contacts...", "authorLabel": "by {author}"}, "goToReservation": "Go to reservation", "noConversations": "No conversations found", "selectConversation": "Select a conversation to view messages", "typeMessage": "Write a message...", "title": "Conversations", "phoneNumber": "Phone Number", "lastMessage": "Last Message", "unreadCount": "Unread Count", "sendMessage": "Send Message", "messages": "Messages", "send": "Send", "sending": "Sending...", "searchContacts": "Search contacts...", "enterPhoneNumber": "Enter phone number", "contactList": "Contact List", "fetchFailed": "Failed to fetch messages", "noMessages": "No messages found", "viewMessages": "View messages", "messageFailed": "Failed to send message", "messageResent": "Message resent successfully", "resendFailed": "Failed to resend message", "resend": "Resend", "resending": "Resending...", "newMessage": "New message", "cancel": "Cancel", "viewReservation": "View Reservation", "archive": "Archive", "unarchive": "Unarchive", "archived": "Archived", "showArchived": "Show Archived", "hideArchived": "Hide Archived", "archiveSuccess": "Conversation archived successfully", "unarchiveSuccess": "Conversation unarchived successfully", "archiveError": "Failed to archive conversation", "loginRequired": "You must be logged in to archive conversations", "refreshSuccess": "Conversations refreshed", "refreshFailed": "Failed to refresh conversations", "markReadFailed": "Failed to mark conversation as read", "loading": "Loading...", "loadingMessages": "Loading messages...", "errorMessage": {"title": "Unable to load messages:", "refreshInstructions": "Please refresh the page or contact support if the problem persists."}, "emptyState": {"noMessages": "No messages found", "filterInstructions": "Try changing your filter or check back later."}, "linkedReservation": "Linked reservation", "openReservation": "Open reservation", "selectBranch": "Select branch"}, "dev": {"title": "Conversations Development", "refresh": "Refresh", "openForm": "New Conversation", "closeForm": "Close Form", "createConversation": "Create a New Conversation", "loading": "Loading...", "error": "Error", "errorTitle": "Error", "errorUnknown": "An unknown error occurred", "empty": "No conversations found", "search": "Search conversations...", "friendlyName": "Friendly Name", "friendlyNamePlaceholder": "Enter a name for this conversation", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "phoneHint": "Format: +1XXXXXXXXXX or XXXXXXXXXX", "participants": "Participants", "address": "Address (phone number)", "proxyAddress": "Proxy Address (<PERSON><PERSON><PERSON> number)", "addParticipant": "Add Participant", "remove": "Remove", "submit": "Submit", "create": "Create", "creating": "Creating...", "cancel": "Cancel", "createdTitle": "Conversation Created", "createdDesc": "The conversation was created successfully", "participantsRequired": "At least one participant is required", "sid": "SID", "fullname": "Full Name", "postalCode": "Postal Code", "dateCreated": "Created", "dateUpdated": "Updated", "readBy": "Read By", "archivedBy": "Archived By", "linkedReservationId": "Reservation ID", "linkedBranch": "Branch", "customerName": "Customer Name", "noName": "No name", "conversationDetail": "Conversation Details", "state": "State", "messages": "Messages", "noMessages": "No messages yet", "typeMessage": "Type a message...", "send": "Send", "sending": "Sending...", "messageResent": "Message resent successfully", "messageSent": "Message Sent", "messageSentDesc": "Your message has been sent", "messageFailed": "Failed to send message", "resendFailed": "Failed to resend message", "loadMoreMessages": "Load More Messages", "archiveSelected": "Archive Selected", "unarchiveSelected": "Unarchive Selected", "archive": "Archive", "unarchive": "Unarchive", "archivedTitle": "Conversations Archived", "archivedDesc": "Selected conversations have been archived", "unarchivedTitle": "Conversations Unarchived", "unarchivedDesc": "Selected conversations have been unarchived", "selectConversation": "Select a conversation to view details", "newMessage": "New Message", "refreshSuccess": "Refreshed", "refreshDesc": "Conversations refreshed successfully", "refreshError": "Failed to refresh conversations"}, "telephoniste": {"title": "Telephoniste", "createdSuccess": "Telephoniste created successfully", "updatedSuccess": "Telephoniste updated successfully", "saveError": "Failed to save telephonist<PERSON>", "fetchError": "Failed to fetch permissions", "loading": "Loading commission types...", "searchUsers": "Search users..."}, "serviceTypes": {"title": "Service Types", "description": "Description", "listTitle": "Service Types List", "listDescription": "All service types available for reservations", "addNew": "Add New Service Type", "addNewDescription": "Create a new service type for reservations", "code": "Code", "codePlaceholder": "Enter a unique code", "name": "Name", "namePlaceholder": "Enter a descriptive name", "descriptionPlaceholder": "Enter a description", "forAdults": "For Adults", "forChildren": "For Children", "isActive": "Active", "status": "Status", "active": "Active", "inactive": "Inactive", "showInactive": "Show Inactive", "search": "Search service types...", "noResults": "No service types found"}, "sellerDashboard": {"welcome": "Welcome", "totalAssignments": "My Total Assignments", "todayBranchReservations": "Today's Branch Reservations", "loading": "Loading Dashboard", "comingSoon": "Coming Soon", "branch": "Branch: {{branch}}"}, "commissionTypes": {"title": "Commission Types", "listTitle": "Commission Types List", "listDescription": "All commission types available in the system", "addNew": "Add New Commission Type", "editTitle": "Edit Commission Type", "createTitle": "Create Commission Type", "createDescription": "Set up a new commission type with specific conditions and amounts", "editDescription": "Edit the details of this commission type", "name": "Name", "code": "Code", "codeTooltip": "The code is used to programmatically identify this commission type. For example, 'commission-vente' allows the system to automatically generate specialized invoices for sales commissions. This code should be unique and descriptive.", "statusTrigger": "Status Trigger", "amount": "Amount", "create": "Create Commission Type", "creating": "Creating...", "update": "Update Commission Type", "updating": "Updating...", "save": "Save", "saving": "Saving...", "recipient": "Recipient", "recipientDescription": "Select who will receive this commission", "partner": "Partner", "papBranch": "PAP Branch", "assignedUser": "Assigned User", "selectStatusTrigger": "Select a status trigger", "selectStatus": "Select status that triggers this commission", "timeRestriction": "Time Restriction", "activeRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "startTime": "Start Time", "endTime": "End Time", "dayRestriction": "Day Restriction", "daysOfWeek": "Days of Week", "selectDays": "Select Days", "selectDaysDescription": "Choose which days of the week this commission applies to", "userRestriction": "User Restriction", "branchRestriction": "Branch Restriction", "selectUsers": "Select Users", "selectBranches": "Select Branches", "isAlwaysActive": "Always Active", "alwaysActive": "Always Active", "alwaysActiveDescription": "This commission will always be active with no time or day restrictions", "timeRestricted": "Time Restricted", "timeRestrictedDescription": "This commission will only be active during specific times of the day", "dayRestricted": "Day Restricted", "dayRestrictedDescription": "This commission will only be active on specific days of the week", "branchRestricted": "Branch Restricted", "branchRestrictedDescription": "This commission will only be applicable for specific branches", "userRestricted": "User Restricted", "userRestrictedDescription": "This commission will only be applicable for specific users", "timeSettings": "Time Settings", "eligibleBranches": "Eligible Branches", "branchSelectionInfo": "Select the branches this commission applies to", "searchBranches": "Search branches...", "eligibleUsers": "Eligible Users", "dailyTimeframe": "Restrict to Daily Timeframe", "specificDays": "Restrict to Specific Days", "specificUsers": "Restrict to Specific Users", "specificBranches": "Restrict to Specific Branches", "deleteConfirm": "Are you sure you want to delete this commission type?", "deleteDescription": "This action cannot be undone.", "noCommissionTypes": "No commission types found", "restrictions": "Restrictions", "actions": "Actions", "invalidDateRange": "Invalid date range", "timeRange": "Time range", "daysOnly": "Days only", "userCount": "{{count}} Users", "branchCount": "{{count}} Branches", "dateRange": "Date Range", "deleteTitle": "Delete Commission Type", "delete": "Delete", "deleting": "Deleting...", "cancel": "Cancel", "loading": "Loading commission types...", "searchUsers": "Search users...", "isAutoApproved": "Auto-approve commissions", "isAutoApprovedDescription": "If enabled, commissions of this type are automatically approved when created."}, "newreservation.branchAndTime.selectBranch": "Select Branch", "pagination": {"showing": "Showing", "of": "of", "perPage": "per page", "previous": "Previous", "next": "Next", "first": "First", "last": "Last", "page": "Page"}, "deleteConfirmation": {"title": "Are you sure?", "description": "This action cannot be undone.", "confirm": "Confirm", "cancel": "Cancel"}, "bugReport": {"buttonAriaLabel": "Report a bug", "dialogTitle": "Report a Bug", "dialogDescription": "Describe the issue you encountered. Please be as detailed as possible.", "form": {"titleLabel": "Title", "titlePlaceholder": "e.g., <PERSON><PERSON> not working on profile page", "descriptionLabel": "Description", "descriptionPlaceholder": "Describe the steps to reproduce the bug and what you expected to happen.", "cancelButton": "Cancel", "submitButton": "Submit Report", "submittingButton": "Submitting..."}, "toast": {"successTitle": "Bug Reported", "successDescription": "Thank you for your feedback!", "errorTitle": "Submission Failed", "errorDescriptionDefault": "An unexpected error occurred."}, "validation": {"titleMin": "Title must be at least 5 characters.", "descriptionMin": "Description must be at least 10 characters."}}, "requestsPage": {"title": "Requests Management"}, "kanban": {"new": "New Messages", "processed": "Handled", "ignored": "Ignored", "filters": {"all": "All", "urgent": "<PERSON><PERSON>", "withBooking": "With Booking", "reschedule": "Reschedule", "cancel": "Cancel"}, "urgent": "<PERSON><PERSON>", "from": "From", "reservation": "Booking", "atTime": "at", "handle": "<PERSON><PERSON>", "ignore": "Ignore", "viewBooking": "View Booking", "tags": {"replanifier": "Reschedule Request", "annuler": "Cancellation", "information": "Information Request", "autre": "Other"}, "debug": {"info": "Debug Info:", "updating": "Updating", "new": "New", "handled": "Handled", "ignored": "Ignored", "total": "Total", "refreshData": "Refresh Data"}, "loadingMessages": "Loading messages...", "errorMessage": {"title": "Unable to load messages:", "refreshInstructions": "Please refresh the page or contact support if the problem persists."}, "emptyState": {"noMessages": "No messages found", "filterInstructions": "Try changing your filter or check back later."}, "noMessages": "No messages in this category"}, "requests": {"customerMessages": "Customer Messages", "kanbanView": "Kanban View", "listView": "List View", "filters": {"all": "All", "urgent": "<PERSON><PERSON>", "withBooking": "With Booking", "reschedule": "Reschedule", "cancel": "Cancel"}, "urgent": "<PERSON><PERSON>", "from": "From", "reservation": "Reservation", "atTime": "at", "handle": "<PERSON><PERSON>", "ignore": "Ignore", "viewBooking": "View Booking", "tags": {"replanifier": "Reschedule Request", "annuler": "Cancellation", "information": "Information Request", "autre": "Other"}, "startProcessing": "Start Processing", "debug": {"processing": "Processing"}, "viewFullMessage": "View Full Message", "fullMessageTitle": "Full Message", "archiveAll": "Archive All", "unarchiveAll": "Unarchive All", "showArchived": "Show Archived", "hideArchived": "Hide Archived"}, "userPerformanceStats": "User Performance Stats", "user": "User", "total": "Total", "prev": "Previous", "next": "Next", "page": "Page", "affectations": {"selectTimeSlot": "Select a time slot", "allTimeSlots": "All time slots"}, "selectDateRange": "Select date range", "from": "From", "to": "To", "You cannot cancel a message that has already been sent": "You cannot cancel a message that has already been sent", "Message canceled successfully": "Message canceled successfully", "Failed to cancel message": "Failed to cancel message", "You can only reschedule pending or failed messages": "You can only reschedule pending or failed messages", "Message rescheduled successfully": "Message rescheduled successfully", "Failed to reschedule message": "Failed to reschedule message", "Sent": "<PERSON><PERSON>", "Pending": "Pending", "Failed": "Failed", "Canceled": "Canceled", "Customer": "Customer", "Phone": "Phone", "Template": "Template", "Message": "Message", "Status": "Status", "Created At": "Created At", "Scheduled At": "Scheduled At", "Actions": "Actions", "No scheduled messages found": "No scheduled messages found", "Unknown": "Unknown", "Unknown Template": "Unknown Template", "Reschedule": "Reschedule", "Cancel": "Cancel", "Reschedule Message": "Reschedule Message", "Choose a new date and time for this message to be sent.": "Choose a new date and time for this message to be sent.", "Scheduled Date": "Scheduled Date", "Pick a date": "Pick a date", "Time": "Time", "Rescheduling...": "Rescheduling...", "Search messages...": "Search messages...", "Clear": "Clear", "First page": "First page", "Previous page": "Previous page", "Page": "Page", "Next page": "Next page", "Last page": "Last page", "Scheduled SMS Messages": "Scheduled SMS Messages", "Manage your scheduled SMS messages": "Manage your scheduled SMS messages", "Refresh": "Refresh", "Messages refreshed": "Messages refreshed", "Failed to load messages": "Failed to load messages", "Error": "Error", "action": {"copy": "Copy", "edit": "Edit", "delete": "Delete", "more": "More actions", "exportExcel": "Export to Excel", "save": "Save", "linkCells": "Link Cells"}, "brevoTemplates": {"addNew": "Add New Template", "editTemplate": "Edit Template", "name": "Template Name", "description": "Description", "content": "Content (HTML supported)", "variables": "Variables", "noVariables": "No variables yet. Add from the list below.", "disabled": "Disabled", "livePreview": "Live Preview", "noTemplates": "No email templates found.", "noTemplatesDesc": "You have not created any email templates yet.", "saveSuccess": "Template saved!", "deleteTitle": "Delete Template?", "deleteDesc": "Are you sure you want to delete this template? This action cannot be undone.", "type": "Type", "typeGeneric": "Generic", "typeAccountCreated": "Account Created (unique)", "typePasswordReset": "Password Reset (unique)", "specialVariable": "Special Variable", "specialVariableHint": "This variable is required for this template type.", "typeUniqueError": "A template of this special type already exists. Only one is allowed."}, "validation": {"required": "This field is required", "invalid": "This field is invalid", "passwordMismatch": "Passwords do not match", "invalidEmail": "Invalid email address", "minLength": "Must be at least {{min}} characters", "maxLength": "Cannot exceed {{max}} characters", "number": "Must be a number", "integer": "Must be an integer", "branchRequired": "At least one branch is required for branch-related roles", "min": "Must be at least {{min}}", "max": "Cannot exceed {{max}}"}, "eventTypes": {"title": "Event Types", "addNew": "Add New Event Type", "edit": "Edit Event Type", "code": "Code", "name": "Name", "codePlaceholder": "Enter event type code", "namePlaceholder": "Enter event type name", "codeDescription": "A unique code for this event type", "nameDescription": "The name that will be displayed", "createSuccess": "Event type created successfully", "updateSuccess": "Event type updated successfully", "deleteSuccess": "Event type deleted successfully", "deleteError": "Failed to delete event type", "saveError": "Failed to save event type", "notFound": "Event type not found", "noEventTypes": "No event types found. Create your first one!", "deleteConfirmTitle": "Delete Event Type", "deleteConfirmDescription": "Are you sure you want to delete the event type '{name}'? This action cannot be undone."}, "breadcrumb": {"home": "Home"}, "loading": "Loading...", "billing": {"cannotBeUndone": "This action cannot be undone.", "massVerify": "Verify", "verifying": "Verifying...", "massVerifySuccess": "Invoices verified", "massVerifyPartial": "Some invoices verified", "massVerifyFailed": "No invoices verified", "verified": "verified", "failed": "failed", "signatureDate": "Signed on", "archiving": "Archiving...", "current": "Current", "downloading": "Downloading...", "downloadPDFs": "Download PDFs", "sendEmails": "Send Emails", "applyFilter": "Apply Filter", "refreshTaxInfo": "Refresh Tax Info", "regenerateContreFacture": "Regenerate Contre Facture", "dateRangeOverridden": "This date range has been manually overridden.", "itemType": "Item Type", "selectType": "Select Type", "itemTitleHint": "The title will automatically update when you select an item type", "selectTaxType": "Select Tax Type", "signedOn": "Signed on", "importBonuses": "Import Bonuses", "selectBonuses": "Select Bonuses", "noBonusesSelected": "No bonuses selected", "noBonusesFound": "No bonuses found", "bonusesImported": "Bonuses imported", "bonusesImportedDesc": "Commission line added to the invoice.", "bonus": "commission", "tpsRate": "TPS Rate", "tpsRateDescription": "The tax rate for the TPS (Taxes on Sales).", "tvqRate": "TVQ Rate", "tvqRateDescription": "The tax rate for the TVQ (Taxes on Value of Goods and Services).", "taxRates": "Tax Rates", "beneficiary": "Beneficiary", "invoiceTitle": "Title", "exportPDF": "Export PDF", "title": "Title", "invoice": "Invoice", "invoices": "Invoices", "addInvoice": "Create Invoice", "createInvoice": "Create Invoice", "invoiceNumber": "Invoice Number", "invoiceDetails": "Invoice Details", "invoiceItems": "Invoice Items", "editInvoiceDetails": "Edit Invoice Details", "noItems": "No items", "addItem": "Add Item", "editItem": "<PERSON>em", "itemTitle": "Title", "date": "Date", "quantity": "Quantity", "unitPrice": "Unit Price", "tax": "Tax", "tps": "GST", "tvq": "QST", "subtotal": "Subtotal", "total": "Total", "itemAdded": "<PERSON>em Added", "itemUpdated": "Item <PERSON>", "itemDeleted": "Item deleted successfully", "itemDeletedMessage": "The invoice item has been deleted", "exportPdf": "Export PDF", "status": {"label": "Status", "new": "New", "processing": "Processing", "paid": "Paid"}, "statusDescriptions": {"nouveau": "Invoice created, awaiting verification", "verifie": "Invoice verified by AMQ management", "envoye": "Invoice sent by email to client", "signe": "Invoice signed by client", "en_traitement": "Invoice being processed for payment", "paye": "Invoice paid", "en_retard": "Invoice overdue for signature or payment"}, "actions": "Actions", "automation": {"thursdayRule": "Automatic: Thursday 12pm - Signed → Processing", "fridayRule": "Automatic: Friday 12pm - Processing → Paid", "lateRule": "Automatic: 48h after sent - <PERSON><PERSON> → Late", "automatedChange": "Automated change", "manualChange": "Manual change"}, "kanban": {"noInvoices": "No invoices in this column"}, "statusHistory": {"title": "Status History", "changedBy": "Changed by", "changedAt": "Changed on", "reason": "Reason", "automatic": "Automatic", "manual": "Manual", "noHistory": "No history available"}, "modals": {"changeStatus": {"title": "Change Status", "currentStatus": "Current Status", "newStatus": "New Status", "reason": "Reason (optional)", "reasonPlaceholder": "Explain why you're changing this status...", "cancel": "Cancel", "update": "Update", "updating": "Updating...", "invalidTransition": "Invalid status transition", "success": "Status updated successfully", "error": "Error updating status"}}, "importSelected": "Import Selected", "selectCommissions": "Select Commissions to Import", "noCommissionsFound": "No commissions found", "noCommissionsSelected": "No commissions selected", "commissionsImported": "Commissions imported", "commissionsImportedDesc": "Commission invoice item added.", "commission": "commission", "amount": "Amount", "generateInvoices": "Generate Invoices", "confirmGenerateInvoices": "Confirm Generate Invoices", "generateInvoicesConfirmText": "This will generate an invoice for each user with commissions or bonuses for the selected week:", "generateInvoicesNote": "Note: This action will save any pending bonus changes before generating invoices.", "generate": "Generate", "generationResults": "Results", "invoicesGenerated": "Invoices generated successfully", "user": "User", "savingBonuses": "Saving bonus data...", "preparingData": "Preparing commission data...", "generatingInvoices": "Generating invoices...", "processingItems": "Processing invoice items...", "finalizingInvoices": "Finalizing invoices...", "completed": "Completed", "generationTimeNote": "This process may take several minutes depending on the number of commissions.", "taxable": "Taxable", "clickToAdd": "Click the button above to add your first item", "fetchingCommissions": "Fetching commissions...", "fetchingBonuses": "Fetching bonuses...", "fetchingUsers": "Fetching user details...", "idle": "Waiting to start...", "invoiceGenerationFailed": "Failed to generate invoices", "search": "Search invoices...", "dueDate": "Due Date", "customer": "Customer", "tpsLabel": "GST", "tvqLabel": "QST", "invoiceCreated": "Invoice created successfully", "invoiceUpdated": "Invoice updated successfully", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "export": "Export", "selectDateRange": "Select Date Range", "generateError": "Error Generating Invoices", "sendEmail": "Send Email", "sendingEmail": "Sending...", "sendEmailWithInvoices": "Send emails with PDF invoices to users", "invoiceDate": "Invoice Date", "invoiceTotal": "Invoice Total", "invoiceSigning": "Invoice Signing", "generateSigningLink": "Generate Signing Link", "signingLink": "Signing Link", "signingLinkGenerated": "Signing link generated successfully", "signingLinkCopied": "Signing link copied to clipboard", "signingLinkExpiresMessage": "This link will be valid until the user signs the invoice or 2 hours after their first visit.", "signInvoice": "Sign Invoice", "invoiceSigned": "Invoice signed successfully", "invoiceSignExpired": "This link has expired and is no longer valid for signing.", "pleaseSignFullName": "Please type your full name to sign this invoice:", "due": "Due Date", "paidDate": "Payment Date", "client": "Client", "editInvoice": "Edit Invoice", "deleteInvoice": "Delete Invoice", "deleteItem": "Delete Item", "itemSaved": "<PERSON><PERSON> saved successfully", "itemSaveError": "Error saving item", "noInvoices": "No invoices", "massGenerateInvoices": "Mass Generate Invoices", "generateAndSendEmails": "Generate & Send Emails", "selectClient": "Select Client", "saveInvoice": "Save Invoice", "selectStatus": "Select Status", "description": "Description", "confirmDeleteInvoice": "Are you sure you want to delete this invoice?", "confirmDeleteItem": "Are you sure you want to delete this item?", "name": "Name", "email": "Email", "clientDetails": "Client Details", "itemDetails": "<PERSON><PERSON>", "itemDate": "Item Date", "invoiceDeleted": "Invoice deleted successfully", "errorCreatingInvoice": "Error creating invoice", "errorUpdatingInvoice": "Error updating invoice", "errorDeletingInvoice": "Error deleting invoice", "errorFetchingInvoices": "Error fetching invoices", "invalidStatusTransition": "Invalid status transition", "errorUpdatingStatus": "Error updating status", "invoiceStatusUpdated": "Invoice status updated successfully", "sending": "Sending...", "emailSent": "<PERSON>ail sent successfully", "emailError": "Error sending email", "downloadPdf": "Download PDF", "importCommissions": "Import Commissions", "itemsImported": "Items imported successfully", "importError": "Error importing items", "invoiceProgress": "Invoice Generation Progress", "searchInvoices": "Search invoices...", "billing.startDate": "Start Date", "billing.endDate": "End Date", "isSigned": "Is Signed", "isSignedTooltip": "Indicates if the invoice is signed", "signed": "Signed", "taxValidation": "Tax Validation", "taxValidationTooltip": "Tax validation status", "taxValidationValid": "Valid TPS/TVQ tax information", "taxValidationInvalid": "Invalid TPS/TVQ tax information", "taxValidationNonTpsTvq": "Non-TPS/TVQ tax type", "taxValidationUnknown": "Unknown tax validation status", "refreshTaxInfoTooltip": "Update tax information and validation status from user profile", "notSigned": "Not signed", "kanbanView": "Kanban View", "listView": "List View", "sendEmailTooltip": "By sending the email, you confirm you are satisfied with the invoice and wish to send it to the client.", "contreFactureTitle": "Counter Invoice", "contreFacturePreview": "Counter Invoice Preview", "noContreFacture": "No counter invoice available for this invoice.", "downloadContreFacture": "Download PDF", "viewContreFacture": "View Counter Invoice", "generateContreFacture": "Generate Counter Invoice", "contreFactureGenerated": "Counter invoice generated successfully.", "contreFacturesSuccess": "Contre facture emails sent successfully.", "contreFacturesPartialSuccess": "Some contre facture emails failed to send.", "contreFacturesFailed": "Failed to send contre facture emails.", "archive": "Archive", "unarchive": "Unarchive", "archivedOn": "Archived on", "showArchived": "Show archived invoices", "showDeleted": "Show deleted invoices", "deletedOn": "Deleted on", "confirmMassDelete": "Are you sure you want to delete the selected invoices? This action cannot be undone.", "deleteSuccess": "Invoices deleted successfully", "deleteFailed": "Failed to delete invoices", "contreFacturesError": "An error occurred while sending contre facture emails.", "deleting": "Deleting...", "invoicesArchived": "Invoices archived successfully", "invoicesUnarchived": "Invoices unarchived successfully", "invoicesUpdated": "Invoices updated", "archiveOperationFailed": "Failed to update invoice archive status", "noDateSelected": "No date selected"}, "contactRequests": {"reservation": "Reservation", "title": "Contact Requests", "description": "Manage and review all contact requests submitted by users or prospects.", "phone": "Phone", "postal": "Postal code", "message": "Message", "source": "Source", "noSource": "No source", "sourceDetails": "Source Details", "clicks": "clicks", "empty": "No contact requests found.", "addReservation": "Add Reservation", "showArchived": "Show Archived", "showActive": "Show Active", "showingArchived": "Showing Archived", "createReservation": "Create Reservation", "manageSources": "Manage Sources", "customerInfo": "Customer Information", "viewMessage": "View Message", "viewReservation": "View Reservation", "duplicateWarning": {"title": "Duplicate Phone Number", "description": "This phone number is already associated with existing reservations:", "viewReservation": "View Reservation", "advice": "Please verify before creating a new reservation to avoid duplicates."}, "analytics": {"title": "Analytics", "show": "Show Analytics", "hide": "Hide Analytics", "description": "Performance metrics for contact requests and their conversion to reservations.", "error": "Failed to load analytics", "totalRequests": "Total Requests", "totalRequestsDesc": "Total number of contact requests received", "conversionRate": "Conversion Rate", "conversionRateDesc": "Percentage of contact requests converted to reservations", "converted": "converted", "presenceRate": "Presence Rate", "presenceRateDesc": "Percentage of converted reservations with customer presence", "withPresence": "with presence", "salesRate": "Sales Rate", "salesRateDesc": "Percentage of converted reservations that resulted in sales", "withSales": "with sales"}, "bulkActions": {"noSelection": "No Selection", "selectItems": "Please select items", "itemsSelected": "items selected", "archive": "Archive", "unarchive": "Unarchive", "archiving": "Archiving...", "unarchiving": "Unarchiving..."}, "listView": {"title": "Contact Request List", "addReservation": "Add Reservation", "filter": "Filter", "allStatuses": "All Statuses", "actions": "Actions"}, "statuses": {"new": "New", "called": "Called", "calledTwice": "Called 2x", "calledThreeTimes": "Called 3x", "notInterested": "Not Interested", "toReserve": "To Reserve", "reserved": "Reserved", "toCallback": "Callback Later", "wrongNumber": "Wrong Number", "unavailable": "Unavailable", "title": "Contact Request Statuses", "name": "Name", "name_en": "Name (EN)", "code": "Code", "color": "Color", "order": "Order", "addStatus": "Add Status", "editStatus": "Edit Status", "deleteTitle": "Delete Status", "deleteDescription": "Are you sure you want to delete this status? This action cannot be undone.", "noStatuses": "No statuses found", "createSuccess": "Status created successfully", "updateSuccess": "Status updated successfully", "deleteSuccess": "Status deleted successfully", "saveError": "Error saving status", "deleteError": "Error deleting status", "loading": "Loading statuses...", "error": "Error loading statuses", "save": "Save", "cancel": "Cancel", "delete": "Delete", "statusUpdated": "Status updated successfully"}, "sources": {"title": "Contact Request Sources", "description": "Manage and track contact request sources with analytics", "createSource": "Create Source", "refresh": "Refresh", "filters": "Filters", "statistics": "Statistics", "showStatistics": "Show Statistics", "hideStatistics": "Hide Statistics", "statsNote": "Note: Advanced analytics are not available with the free Bitly account. Upgrade to a paid plan for detailed click tracking and statistics.", "searchPlaceholder": "Search by tag, source, or influencer...", "allSources": "All Sources", "allInfluencers": "All Influencers", "noSources": "No sources found", "createFirst": "Create your first contact request source to get started", "totalSources": "Total Sources", "totalClicks": "Total Clicks", "averageClicks": "Average Clicks", "topPerformer": "Top Performer", "performanceBySource": "Performance by Source Type", "performanceByInfluencer": "Performance by Influencer", "topPerformingSources": "Top Performing Sources", "recentActivity": "Recent Activity", "clicks": "clicks", "sources": "sources", "created": "Created", "copyUrl": "Copy URL", "openUrl": "Open URL", "create": {"title": "Create Contact Request Source", "tag": "Tag", "tagDescription": "A unique 6-character identifier for this source", "generateNew": "Generate new tag", "source": "Source", "selectSource": "Select source type", "influencer": "Influencer", "selectInfluencer": "Select influencer", "cancel": "Cancel", "create": "Create Source", "creating": "Creating...", "success": {"title": "Source Created Successfully!", "description": "Your contact request source has been created and a shortened URL has been generated.", "urlGenerated": "Generated Bitly URL:", "urlCopied": "The URL has been automatically copied to your clipboard.", "done": "Done"}, "errors": {"tagRequired": "Tag is required", "tagFormat": "Tag must be exactly 6 alphanumeric characters", "sourceRequired": "Source is required", "influencerRequired": "Influencer is required", "tagExists": "This tag already exists. Please generate a new one."}}}}, "invoiceItemTypes": {"title": "Invoice Item Types", "add": "Add Invoice Item Type", "edit": "Edit Invoice Item Type", "delete": "Delete Invoice Item Type", "name": "Name", "code": "Code", "branch": "Branch", "requiresContreFacture": "Requires Contre Facture", "addSuccess": "Invoice item type added successfully.", "addError": "Failed to add invoice item type.", "editSuccess": "Invoice item type updated successfully.", "editError": "Failed to update invoice item type.", "deleteSuccess": "Invoice item type deleted successfully.", "deleteError": "Failed to delete invoice item type.", "confirmDelete": "Are you sure you want to delete this invoice item type?", "loading": "Loading...", "empty": "No invoice item types found.", "actions": "Actions", "chargeAmount": "Charge Amount"}, "taxTypes": {"percentage": "Percentage", "name": "Name", "title": "Tax Types", "add": "Add Tax Type", "edit": "Edit Tax Type", "delete": "Delete Tax Type", "code": "Code", "nameEn": "Name (English)", "nameFr": "Name (French)", "percentages": "Percentages", "addPercentage": "Add Percentage", "addSuccess": "Tax type added successfully.", "addError": "Failed to add tax type.", "deleteSuccess": "Tax type deleted successfully.", "deleteError": "Failed to delete tax type.", "confirmDelete": "Are you sure you want to delete this tax type?", "loading": "Loading...", "empty": "No tax types found.", "actions": "Actions"}, "eventsCount": "Events Count", "deliveryAvailability": {"title": "Delivery Availability"}, "contacts": {"title": "Contacts", "name": "Name", "phone": "Phone", "postalCode": "Postal Code", "createdAt": "Created At", "error": "Failed to load contacts", "retry": "Retry", "empty": "No contacts found.", "unknown": "Unknown"}, "invitations": {"noGroup": "No Group", "group": "Group", "selectPartnerFirst": "Select a partner first", "progress": {"generating": "Generating invitations...", "success": "Invitations generated successfully", "error": "Failed to generate invitations", "loading": "Loading progress...", "completed": "Invitations generated", "successTitle": "Success!", "successDescription": "All invitations were generated successfully.", "errorTitle": "Error", "minimize": "Minimize", "close": "Close", "persistentProgress": "Generation in progress"}, "title": "Invitations", "description": "Manage partner invitations", "new": "New Invitation", "edit": "Edit Invitation", "confirmDelete": "Are you sure you want to delete this invitation?", "partner": "Partner", "reservation": "Reservation", "noReservation": "No Reservation", "searchPlaceholder": "Search by partner name or email", "filters": {"partner": "Filter by Partner", "reservation": "Filter by Reservation", "date": "Filter by Date"}, "columns": {"group": "Group", "id": "ID", "token": "Token", "partner": "Partner", "reservation": "Reservation", "created": "Created", "updated": "Updated", "actions": "Actions", "invitationUrl": "Invitation URL", "partnerName": "Partner Name", "createdAt": "Created At"}, "notFound": "No invitations found", "create": {"success": "Invitation created successfully", "error": "Failed to create invitation"}, "update": {"success": "Invitation updated successfully", "error": "Failed to update invitation"}, "delete": {"title": "Delete Invitation", "success": "Invitation deleted successfully", "error": "Failed to delete invitation"}, "generate": {"groupNamePlaceholder": "Enter group name", "groupName": "Group Name", "title": "Generate Invitations", "description": "Create multiple invitations for the selected partner", "count": "Number of Invitations", "selectPartner": "Select partner", "submit": "Generate", "success": "Invitations Generated", "successDescription": "Successfully generated invitations", "largeSuccessDescription": "Successfully started generating invitations. You can track the progress.", "error": "Failed to generate invitations", "cantCloseWhileGenerating": "Please wait while invitations are being generated. This dialog cannot be closed during generation."}, "groups": {"active": "Active", "inactive": "Inactive", "showInactive": "Show Inactive Groups", "markAsActive": "<PERSON> as Active", "markAsInactive": "<PERSON> as Inactive", "statusUpdated": "Group status updated successfully", "statusUpdateFailed": "Failed to update group status"}, "tokens": {"totalAllGroups": "Total (All Groups)", "activeGroupsOnly": "Active Groups Only", "inactiveGroupsOnly": "Inactive Groups Only", "groupBreakdown": "Group Breakdown", "tokenStatisticsComparison": "Token Statistics Comparison", "comprehensiveBreakdown": "Detailed breakdown showing impact of active vs inactive groups"}, "structure": {"partnersAndGroups": "Partners & Groups", "invitationsAndReservations": "Invitations & Reservations", "tokensAndConversion": "Tokens & Conversion", "activeVsInactiveStats": "Active vs Inactive Group Statistics", "comprehensiveBreakdown": "Comprehensive breakdown showing impact of group status on system metrics", "totalInvitations": "Total Invitations", "totalGroups": "total groups", "activeGroups": "Active Groups", "inactiveGroups": "Inactive Groups"}}, "reservationsAccess": {"title": "Reservations Access", "description": "Manage reservation access settings and permissions for roles and users.", "rolesTab": "Role Filters", "usersTab": "User Filters", "roleFiltersTitle": "Role-based Reservation Filters", "roleFiltersDescription": "Configure default reservation access settings for each role.", "userFiltersTitle": "User-specific Reservation Filters", "userFiltersDescription": "Override role settings with user-specific reservation access filters.", "errorFetchingStatuses": "Failed to fetch reservation statuses", "statusCodesFilter": "Status Codes Filter", "selectAll": "Select All", "clearAll": "Clear All", "inheritFromRoles": "Inherit from Roles", "branchAccessFilter": "Branch Access Filter", "branchAccess": {"title": "Branch Access", "all": "All Branches", "assigned": "Assigned Branches Only", "none": "No Access"}, "assignmentFilterTitle": "Assignment Filter", "assignmentFilter": {"title": "Assignment Filter", "all": "All Reservations", "self": "Self-Assigned Only", "none": "None"}, "partnerFilterTitle": "Partner Filter", "partnerFilter": {"title": "Partner Filter", "all": "All Reservations", "self": "Self-Partnered Only", "none": "None"}, "errorFetchingRoles": "Failed to fetch roles", "roleFiltersUpdated": "Role filters updated successfully", "errorUpdatingRoleFilters": "Failed to update role filters", "configureRoleFilters": "Configure <PERSON> Filters", "role": "Role", "noRolesFound": "No roles found", "configureFilters": "Configure Filters", "statusCodes": "Status Codes", "errorFetchingUsers": "Failed to fetch users", "userFiltersUpdated": "User filters updated successfully", "errorUpdatingUserFilters": "Failed to update user filters", "searchUsers": "Search users by name or email...", "configureUserFilters": "Configure User Filters", "user": "User", "viewEffectiveFilters": "View Effective Filters", "hasCustomFilters": "Has Custom Filters", "inheritingFromRoles": "Inheriting from Roles", "effectiveFilters": "Effective Filters", "effectiveFiltersDescription": "View the combined reservation access filters for {{userName}}", "inheritedFromRoles": "Inherited from Roles", "userSpecificOverrides": "User-specific Overrides", "finalEffectiveFilters": "Final Effective Filters", "filterMergingLogic": "Filter Merging Logic", "mergingRule1": "Status codes: Union of all role status codes", "mergingRule2": "Branch access: Most permissive setting from roles", "mergingRule3": "Assignment filter: Most restrictive setting from roles", "mergingRule4": "Partner filter: Most restrictive setting from roles", "mergingRule5": "User-specific filters override role settings when defined", "mergingRule6": "Available columns: Union of all columns from roles", "errorFetchingEffectiveFilters": "Failed to fetch effective filters", "noEffectiveFiltersData": "No effective filters data available", "effectiveResult": "Effective Result", "noRoleFilters": "No role filters defined", "userOverrides": "User Overrides", "allStatuses": "All statuses", "configure": "Configure", "customFilters": "Custom Filters", "viewEffective": "View Effective", "showingUsers": "Showing {{start}} to {{end}} of {{total}} users", "pageOf": "Page {{page}} of {{pages}}", "noUsers": "No users found", "noUsersFound": "No users found matching your search", "noRoles": "No roles found", "availableColumns": "Available Columns", "availableColumnsFilter": "Available Columns Filter", "columnsSelected": "Columns Selected", "allColumns": "All Columns", "moreColumns": "more columns"}, "newDashboard": {"selectDateRange": "Select Date Range", "compareAgentTypeMismatch": "Compare settings must use the same agent type as filters (Agent or Seller)", "loadingTitle": "Loading New Dashboard", "loadingMessage": "Fetching dashboard information...", "errorLoading": "Error loading new dashboard:", "filters": "Filters", "compare": "Compare", "branches": "Branches", "chooseBranches": "Choose branches", "allBranchesSelected": "All branches selected", "branchesSelected": "branch(es) selected", "searchBranches": "Search branches...", "allBranches": "All Branches", "years": "Years", "selectYear": "Select Year", "month": "Month", "selectMonth": "Select Month", "agent": "Agent", "chooseUser": "Choose a user", "agentPAP": "Agent (PAP)", "seller": "<PERSON><PERSON>", "selectUser": "Select User", "selectedUser": "Selected User", "searchUsers": "Search users...", "allUsers": "All Users", "dateRange": "Date Range", "quickSelect": "Quick Select", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This Week", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "thisMonth": "This Month", "lastMonth": "Last Month", "customRange": "Custom Range", "clearFilters": "Clear Filters", "loading": "Loading...", "applyFilters": "Apply Filters", "selectCustomDateRange": "Select Custom Date Range", "fromDate": "From Date", "toDate": "To Date", "apply": "Apply", "vsPreviousMonth": "vs. previous month", "vsPreviousYear": "vs. previous year", "presenceRate": "PRESENCE RATE", "presenceRateTooltip": "Percentage of reservations with presence status - calculated as presence/reservations that took place (filtered by visit date)", "salesCount": "SALES (COUNT)", "salesCountTooltip": "Number of reservations that resulted in a sale - filtered by visit date (when visits actually took place)", "totalSalesAmount": "TOTAL SALES AMOUNT", "totalSalesAmountTooltip": "Total amount from all sales - filtered by visit date (when visits actually took place)", "totalReservationsCreated": "TOTAL RESERVATIONS CREATED", "totalReservationsCreatedTooltip": "Total number of reservations created (booked) in the selected time period - filtered by creation date", "salesByBranch": "Sales by Branch", "reservationsCreatedByBranch": "Reservations Created by Branch", "bestPerformingSeller": "Best Performing Seller", "topPAPPerformance": "Top PAP Performance"}}