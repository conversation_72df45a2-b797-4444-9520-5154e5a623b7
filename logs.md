# Email Services

This document previously contained information about OVH Email Integration and Cerebras AI processing, which have been removed from the application.

The application now only uses Brevo for transactional email sending (contract notifications, stats emails, etc.).



### Sentry Utility

**File:** `lib/utils/sentry.ts`

- `initSentry(): void`  
  Initializes Sentry for error tracking in Next.js. Reads DSN from environment, only initializes once, and logs debug info in development. Handles missing DSN gracefully. 

### Error Boundary Sentry Integration

- **File:** `app/error.tsx`
- The custom error boundary now reports errors to Sentry in production using `@sentry/nextjs`. Errors are captured in a `useEffect` and sent to Sentry automatically when they occur. 