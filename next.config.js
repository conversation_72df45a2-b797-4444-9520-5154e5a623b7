/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  
  // Server runtime config (email service removed)
  
  // Log environment variables during build
  webpack: (config) => {
    // Add fallbacks for missing modules
    config.resolve.fallback = {
      "aws-sdk": false,
      "@aws-sdk/client-s3": false,
      "@aws-sdk/s3-request-presigner": false
    };
    
    return config;
  },

  output: 'standalone',
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  images: { 
    unoptimized: true,
    domains: ['panel.amq.company', 'localhost']
  },
  experimental: {
    optimizeCss: true,
    serverActions: {
      allowedForwardedHosts: ['localhost', '127.0.0.1'],
      bodySizeLimit: '2mb'
    }
  },
  poweredByHeader: false,
  env: {
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  },
  // Update static file serving configuration
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: '/api/uploads/:path*',
      }
    ];
  },
  // Ensure public directory is included in the build
  async headers() {
    return [
      {
        source: '/uploads/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate, proxy-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
    ];
  },
  async redirects() {
    return [
      {
        source: '/404',
        destination: '/',
        permanent: true,
      },
      {
        source: '/500',
        destination: '/',
        permanent: true,
      },
    ];
  }
};

module.exports = nextConfig;
