# Task 7: Implement Recontact Status Management ✅ COMPLETED

## Objective
Create CRUD operations for RecontactReservationStatus and admin interface for managing recontact statuses.

## Deliverables

### 1. Admin Status Management Page
**File**: `app/recontact-statuses/page.tsx`

### 2. Status Form Component
**File**: `app/recontact-statuses/components/status-form.tsx`

### 3. Status List Component
**File**: `app/recontact-statuses/components/status-list.tsx`

### 4. API Endpoints
**Files**: 
- `app/api/recontact-statuses/route.ts`
- `app/api/recontact-statuses/[id]/route.ts`

## Admin Page Implementation

### Main Status Management Page

```typescript
// app/recontact-statuses/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserManageRecontactStatuses } from '@/lib/utils/permissions-utils';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { PageHeader } from '@/components/ui/page-header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus } from 'lucide-react';
import { StatusForm } from './components/status-form';
import { StatusList } from './components/status-list';
import { useToast } from '@/components/ui/use-toast';

interface RecontactStatus {
  _id: string;
  name: string;
  code: string;
  color: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

export default function RecontactStatusesPage() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { data: session, status } = useSession();
  const router = useRouter();
  const permissions = useAppSelector(state => state.permissions);

  // Check permissions using Redux store (add the manage recontact statuses permission to permission codes & define the utility function in permission utils file)
  const hasManageAccess = permissions && canUserManageRecontactStatuses(permissions);

  const [statuses, setStatuses] = useState<RecontactStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingStatus, setEditingStatus] = useState<RecontactStatus | null>(null);
  const [showForm, setShowForm] = useState(false);

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (!hasManageAccess) {
      toast({
        title: t('common.error'),
        description: t('recontact.statuses.noPermission'),
        variant: 'destructive'
      });
      router.push('/');
      return;
    }
  }, [session, status, hasManageAccess, router, toast, t]);

  // Don't render if no access
  if (!hasManageAccess) {
    return null;
  }

  // Fetch statuses
  const fetchStatuses = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/recontact-statuses');
      if (!response.ok) throw new Error('Failed to fetch statuses');
      const data = await response.json();
      setStatuses(data.sort((a, b) => a.order - b.order));
    } catch (error) {
      console.error('Error fetching statuses:', error);
      toast({
        title: t('common.error'),
        description: t('recontact.statuses.fetchError'),
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatuses();
  }, []);

  const handleCreateStatus = () => {
    setEditingStatus(null);
    setShowForm(true);
  };

  const handleEditStatus = (status: RecontactStatus) => {
    setEditingStatus(status);
    setShowForm(true);
  };

  const handleDeleteStatus = async (statusId: string) => {
    if (!confirm(t('recontact.statuses.confirmDelete'))) return;

    try {
      const response = await fetch(`/api/recontact-statuses/${statusId}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete status');

      await fetchStatuses();
      toast({
        title: t('common.success'),
        description: t('recontact.statuses.deleteSuccess')
      });
    } catch (error) {
      console.error('Error deleting status:', error);
      toast({
        title: t('common.error'),
        description: t('recontact.statuses.deleteError'),
        variant: 'destructive'
      });
    }
  };

  const handleFormSubmit = async (data: any) => {
    try {
      const url = editingStatus 
        ? `/api/recontact-statuses/${editingStatus._id}`
        : '/api/recontact-statuses';
      
      const method = editingStatus ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (!response.ok) throw new Error('Failed to save status');

      await fetchStatuses();
      setShowForm(false);
      setEditingStatus(null);
      
      toast({
        title: t('common.success'),
        description: editingStatus 
          ? t('recontact.statuses.updateSuccess')
          : t('recontact.statuses.createSuccess')
      });
    } catch (error) {
      console.error('Error saving status:', error);
      toast({
        title: t('common.error'),
        description: t('recontact.statuses.saveError'),
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="space-y-6">
        <PageHeader
          title={t('recontact.statuses.title')}
          description={t('recontact.statuses.description')}
          action={
            <Button onClick={handleCreateStatus}>
              <Plus className="h-4 w-4 mr-2" />
              {t('recontact.statuses.create')}
            </Button>
          }
        />

        {showForm && (
          <Card>
            <CardHeader>
              <CardTitle>
                {editingStatus 
                  ? t('recontact.statuses.editTitle')
                  : t('recontact.statuses.createTitle')
                }
              </CardTitle>
            </CardHeader>
            <CardContent>
              <StatusForm
                initialData={editingStatus}
                onSubmit={handleFormSubmit}
                onCancel={() => {
                  setShowForm(false);
                  setEditingStatus(null);
                }}
              />
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>{t('recontact.statuses.listTitle')}</CardTitle>
          </CardHeader>
          <CardContent>
            <StatusList
              statuses={statuses}
              loading={loading}
              onEdit={handleEditStatus}
              onDelete={handleDeleteStatus}
              onReorder={fetchStatuses}
            />
          </CardContent>
        </Card>
      </div>
  );
}
```

## Form Component

### Status Form

```typescript
// app/recontact-statuses/components/status-form.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ColorPicker } from '@/components/ui/color-picker';
import { useTranslation } from 'react-i18next';

const statusSchema = z.object({
  name: z.string().min(1, 'Name is required').max(50, 'Name too long'),
  code: z.string()
    .min(1, 'Code is required')
    .max(20, 'Code too long')
    .regex(/^[a-z0-9_-]+$/, 'Code must be lowercase alphanumeric with dashes/underscores'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
  order: z.number().min(0, 'Order must be positive')
});

type StatusFormData = z.infer<typeof statusSchema>;

interface StatusFormProps {
  initialData?: any;
  onSubmit: (data: StatusFormData) => void;
  onCancel: () => void;
}

export function StatusForm({ initialData, onSubmit, onCancel }: StatusFormProps) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<StatusFormData>({
    resolver: zodResolver(statusSchema),
    defaultValues: {
      name: initialData?.name || '',
      code: initialData?.code || '',
      color: initialData?.color || '#6E6E6E',
      order: initialData?.order || 0
    }
  });

  const watchedColor = watch('color');

  const handleFormSubmit = async (data: StatusFormData) => {
    setLoading(true);
    try {
      await onSubmit(data);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">{t('recontact.statuses.form.name')}</Label>
          <Input
            id="name"
            {...register('name')}
            placeholder={t('recontact.statuses.form.namePlaceholder')}
          />
          {errors.name && (
            <p className="text-sm text-red-500">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="code">{t('recontact.statuses.form.code')}</Label>
          <Input
            id="code"
            {...register('code')}
            placeholder={t('recontact.statuses.form.codePlaceholder')}
          />
          {errors.code && (
            <p className="text-sm text-red-500">{errors.code.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="color">{t('recontact.statuses.form.color')}</Label>
          <ColorPicker
            value={watchedColor}
            onChange={(color) => setValue('color', color)}
          />
          {errors.color && (
            <p className="text-sm text-red-500">{errors.color.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="order">{t('recontact.statuses.form.order')}</Label>
          <Input
            id="order"
            type="number"
            {...register('order', { valueAsNumber: true })}
            placeholder="0"
          />
          {errors.order && (
            <p className="text-sm text-red-500">{errors.order.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          {t('common.cancel')}
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? t('common.saving') : t('common.save')}
        </Button>
      </div>
    </form>
  );
}
```

## List Component

### Status List with Drag & Drop

```typescript
// app/recontact-statuses/components/status-list.tsx
'use client';

import { useState } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, GripVertical } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface StatusListProps {
  statuses: RecontactStatus[];
  loading: boolean;
  onEdit: (status: RecontactStatus) => void;
  onDelete: (statusId: string) => void;
  onReorder: () => void;
}

export function StatusList({
  statuses,
  loading,
  onEdit,
  onDelete,
  onReorder
}: StatusListProps) {
  const { t } = useTranslation();
  const [reordering, setReordering] = useState(false);

  const handleDragEnd = async (result: any) => {
    if (!result.destination) return;

    const items = Array.from(statuses);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order values
    const updates = items.map((item, index) => ({
      id: item._id,
      order: index
    }));

    setReordering(true);
    try {
      await fetch('/api/recontact-statuses/reorder', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates })
      });

      onReorder();
    } catch (error) {
      console.error('Error reordering statuses:', error);
    } finally {
      setReordering(false);
    }
  };

  if (loading) {
    return <div className="text-center py-8">{t('common.loading')}</div>;
  }

  if (statuses.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        {t('recontact.statuses.empty')}
      </div>
    );
  }

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="statuses">
        {(provided) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="space-y-2"
          >
            {statuses.map((status, index) => (
              <Draggable
                key={status._id}
                draggableId={status._id}
                index={index}
                isDragDisabled={reordering}
              >
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    className={`flex items-center justify-between p-4 border rounded-lg bg-white ${
                      snapshot.isDragging ? 'shadow-lg' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div
                        {...provided.dragHandleProps}
                        className="text-muted-foreground hover:text-foreground cursor-grab"
                      >
                        <GripVertical className="h-4 w-4" />
                      </div>

                      <Badge
                        style={{
                          backgroundColor: `${status.color}20`,
                          color: status.color,
                          borderColor: status.color
                        }}
                      >
                        {status.name}
                      </Badge>

                      <span className="text-sm text-muted-foreground">
                        {status.code}
                      </span>

                      <span className="text-xs text-muted-foreground">
                        Order: {status.order}
                      </span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(status)}
                        disabled={reordering}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDelete(status._id)}
                        disabled={reordering}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}
```

## API Endpoints

### Main CRUD Endpoint

```typescript
// app/api/recontact-statuses/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';

// GET - List all recontact statuses
export const GET =async () => {
  try {
  await dbConnect();

    const statuses = await RecontactReservationStatus.find()
      .sort({ order: 1 })
      .lean();

    return NextResponse.json(statuses);
  } catch (error) {
    console.error('Error fetching recontact statuses:', error);
    return NextResponse.json(
      { message: 'Failed to fetch statuses' },
      { status: 500 }
    );
  }
};

// POST - Create new recontact status
export const POST = async (request: NextRequest) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, code, color, order } = body;

    // Validate required fields
    if (!name || !code) {
      return NextResponse.json(
        { message: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Check for duplicate code
    const existingStatus = await RecontactReservationStatus.findOne({ code });
    if (existingStatus) {
      return NextResponse.json(
        { message: 'Status code already exists' },
        { status: 400 }
      );
    }

    const status = new RecontactReservationStatus({
      name,
      code: code.toLowerCase(),
      color: color || '#6E6E6E',
      order: order || 0,
      createdBy: session.user.id,
      updatedBy: session.user.id
    });

    await status.save();

    return NextResponse.json(status, { status: 201 });
  } catch (error) {
    console.error('Error creating recontact status:', error);
    return NextResponse.json(
      { message: 'Failed to create status' },
      { status: 500 }
    );
  }
};
```



## Acceptance Criteria

- [x] Admin can create, edit, and delete recontact statuses
- [x] Status list supports drag-and-drop reordering
- [x] Form validation prevents invalid data
- [x] Color picker allows custom status colors
- [x] Duplicate codes are prevented
- [x] All operations are properly audited
- [x] Permission guards protect admin functions
- [x] API endpoints handle errors gracefully

## Dependencies
- Task 1: Database models
- Task 5: Permissions system
- Drag and drop library
- Color picker component
- Form validation library

## Estimated Time
8-10 hours

## ✅ COMPLETION SUMMARY

**Status:** COMPLETED
**Date:** January 2025

### Implemented Components

1. **Main Status Management Page** (`app/recontact-statuses/page.tsx`)
   - Permission-based access control using Redux pattern
   - Full CRUD interface for recontact statuses
   - Responsive design with proper error handling

2. **Status Form Component** (`app/recontact-statuses/components/status-form.tsx`)
   - React Hook Form with Zod validation
   - Custom color picker integration
   - Form validation with user-friendly error messages

3. **Status List Component** (`app/recontact-statuses/components/status-list.tsx`)
   - Drag-and-drop reordering using react-dnd
   - Visual status badges with custom colors
   - Edit and delete actions with confirmation

4. **Color Picker Component** (`components/ui/color-picker.tsx`)
   - Preset color palette
   - Custom hex color input
   - Popover-based interface

5. **API Endpoints**
   - `GET/POST /api/recontact-statuses` - List and create statuses
   - `GET/PUT/DELETE /api/recontact-statuses/[id]` - Individual status operations
   - `PATCH /api/recontact-statuses/reorder` - Drag-and-drop reordering

6. **Permission System**
   - Added `MANAGE_RECONTACT_STATUSES` permission
   - Updated permission utilities and constants
   - Integrated with existing Redux permission pattern

7. **Navigation Integration**
   - Added to sidebar under General Settings
   - Proper permission-based visibility

8. **Translation Support**
   - Complete English and French translations
   - Integrated with existing language context

9. **Database Seeding**
   - Default status options with appropriate colors
   - Permission seeding script updated

### Technical Notes

- Used react-dnd instead of @hello-pangea/dnd to match existing codebase patterns
- Implemented proper error handling and user feedback
- Followed existing code patterns for consistency
- All operations include proper audit trails (createdBy/updatedBy)
- Duplicate prevention for both names and codes
- Usage validation before deletion

```
