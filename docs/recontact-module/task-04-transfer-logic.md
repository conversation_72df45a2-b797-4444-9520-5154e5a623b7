# Task 4: Implement Reservation Transfer Logic

**Status: ✅ COMPLETED**

## Objective
Create API endpoint and logic to safely transfer reservations to RecontactReservations collection with proper error handling to prevent data loss.

## Deliverables

### 1. Transfer Service
**File**: `lib/services/recontact-transfer-service.ts`

### 2. API Endpoint
**File**: `app/api/reservations/[id]/transfer-to-recontact/route.ts`

### 3. Database Transaction Logic
Implement atomic operations to ensure data integrity.

## Transfer Service Implementation

### Core Transfer Function

```typescript
// lib/services/recontact-transfer-service.ts
import mongoose from 'mongoose';
import Reservation from '@/models/Reservation';
import RecontactReservation from '@/models/RecontactReservation';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';
import { ReservationAuditLogger } from '@/lib/utils/audit-utils';

export interface TransferToRecontactParams {
  reservationId: string;
  recontactDate: Date;
  statusId: string;
  userId: string;
}

export interface TransferResult {
  success: boolean;
  recontactReservationId?: string;
  error?: string;
}

export class RecontactTransferService {
  /**
   * Safely transfer a reservation to recontact collection
   */
  static async transferToRecontact(params: TransferToRecontactParams): Promise<TransferResult> {
    const session = await mongoose.startSession();
    
    try {
      return await session.withTransaction(async () => {
        const { reservationId, recontactDate, statusId, userId } = params;
        
        // 1. Validate inputs
        await this.validateTransferParams(params);
        
        // 2. Fetch original reservation
        const originalReservation = await this.fetchOriginalReservation(reservationId, session);
        
        // 3. Create recontact reservation
        const recontactReservation = await this.createRecontactReservation({
          reservation: originalReservation,
          recontactDate,
          statusId,
          userId
        }, session);
        
        // 4. Delete original reservation (only after successful creation)
        await this.deleteOriginalReservation(reservationId, userId, session);
        
        // 5. Log the transfer
        await this.logTransfer(originalReservation, recontactReservation, userId);
        
        return {
          success: true,
          recontactReservationId: recontactReservation._id.toString()
        };
      });
    } catch (error) {
      console.error('Transfer to recontact failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    } finally {
      await session.endSession();
    }
  }

  /**
   * Validate transfer parameters
   */
  private static async validateTransferParams(params: TransferToRecontactParams): Promise<void> {
    const { reservationId, recontactDate, statusId, userId } = params;
    
    // Validate reservation ID
    if (!mongoose.Types.ObjectId.isValid(reservationId)) {
      throw new Error('Invalid reservation ID');
    }
    
    // Validate recontact date
    if (!recontactDate || recontactDate <= new Date()) {
      throw new Error('Recontact date must be in the future');
    }
    
    // Validate status ID and ensure it exists
    if (!mongoose.Types.ObjectId.isValid(statusId)) {
      throw new Error('Invalid status ID');
    }
    
    const status = await RecontactReservationStatus.findById(statusId);
    if (!status) {
      throw new Error('Recontact status not found');
    }
    
    // Validate user ID
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid user ID');
    }
  }

  /**
   * Fetch and validate original reservation
   */
  private static async fetchOriginalReservation(
    reservationId: string, 
    session: mongoose.ClientSession
  ): Promise<any> {
    const reservation = await Reservation.findById(reservationId).session(session);
    
    if (!reservation) {
      throw new Error('Reservation not found');
    }
    
    if (reservation.isDeleted) {
      throw new Error('Cannot transfer deleted reservation');
    }
    
    return reservation.toObject();
  }

  /**
   * Create recontact reservation
   */
  private static async createRecontactReservation(
    data: {
      reservation: any;
      recontactDate: Date;
      statusId: string;
      userId: string;
    },
    session: mongoose.ClientSession
  ): Promise<any> {
    const recontactReservation = new RecontactReservation({
      recontactDate: data.recontactDate,
      statusId: new mongoose.Types.ObjectId(data.statusId),
      reservation: data.reservation,
      createdBy: new mongoose.Types.ObjectId(data.userId),
      updatedBy: new mongoose.Types.ObjectId(data.userId)
    });
    
    const saved = await recontactReservation.save({ session });
    return saved;
  }

  /**
   * Delete original reservation
   */
  private static async deleteOriginalReservation(
    reservationId: string,
    userId: string,
    session: mongoose.ClientSession
  ): Promise<void> {
    const result = await Reservation.findByIdAndDelete(reservationId).session(session);
    
    if (!result) {
      throw new Error('Failed to delete original reservation');
    }
  }

  /**
   * Log the transfer operation
   */
  private static async logTransfer(
    originalReservation: any,
    recontactReservation: any,
    userId: string
  ): Promise<void> {
    try {
      await ReservationAuditLogger.logReservationTransfer({
        originalReservationId: originalReservation._id,
        recontactReservationId: recontactReservation._id,
        recontactDate: recontactReservation.recontactDate,
        transferredBy: userId,
        customerInfo: originalReservation.customerInfo
      });
    } catch (error) {
      console.error('Failed to log transfer:', error);
      // Don't throw here as the transfer was successful
    }
  }
}
```

## API Endpoint Implementation

```typescript
// app/api/reservations/[id]/transfer-to-recontact/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { RecontactTransferService } from '@/lib/services/recontact-transfer-service';

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    const body = await request.json();
    
    const { recontactDate, statusId } = body;

    if (!recontactDate || !statusId) {
      return NextResponse.json(
        { message: 'Recontact date and status ID are required' },
        { status: 400 }
      );
    }

    // Validate date format
    const parsedDate = new Date(recontactDate);
    if (isNaN(parsedDate.getTime())) {
      return NextResponse.json(
        { message: 'Invalid recontact date format' },
        { status: 400 }
      );
    }

    // Perform the transfer
    const result = await RecontactTransferService.transferToRecontact({
      reservationId: id,
      recontactDate: parsedDate,
      statusId,
      userId: session.user.id
    });

    if (!result.success) {
      return NextResponse.json(
        { message: result.error || 'Transfer failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Reservation transferred to recontact successfully',
      recontactReservationId: result.recontactReservationId
    });

  } catch (error) {
    console.error('Transfer to recontact API error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## Error Handling Strategy

### Transaction Safety
- Use MongoDB transactions for atomic operations
- Rollback on any failure
- Validate all inputs before starting transaction
- Log all operations for audit trail

### Error Types and Handling
```typescript
export enum TransferErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RESERVATION_NOT_FOUND = 'RESERVATION_NOT_FOUND',
  STATUS_NOT_FOUND = 'STATUS_NOT_FOUND',
  DATABASE_ERROR = 'DATABASE_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR'
}

export class TransferError extends Error {
  constructor(
    public type: TransferErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'TransferError';
  }
}
```

### Retry Logic
```typescript
export class RecontactTransferService {
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second

  static async transferWithRetry(params: TransferToRecontactParams): Promise<TransferResult> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        return await this.transferToRecontact(params);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.MAX_RETRIES) {
          await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY * attempt));
          continue;
        }
      }
    }
    
    return {
      success: false,
      error: `Transfer failed after ${this.MAX_RETRIES} attempts: ${lastError?.message}`
    };
  }
}
```



## Monitoring and Logging

### Audit Trail
- Log all transfer attempts
- Track success/failure rates
- Monitor performance metrics
- Alert on high failure rates

### Metrics to Track
- Transfer success rate
- Average transfer time
- Error distribution
- User activity patterns

## Acceptance Criteria

- [x] Transfer service safely moves reservations to recontact collection
- [x] Original reservation is deleted only after successful recontact creation
- [x] All operations are atomic using database transactions
- [x] Comprehensive error handling prevents data loss
- [x] API endpoint validates all inputs properly
- [x] Retry logic handles transient failures
- [x] All operations are properly logged and audited
- [ ] Performance is acceptable for expected load
- [ ] Monitoring and alerting are in place

## Dependencies
- Task 1: Database models
- MongoDB transaction support
- Audit logging system
- Error handling utilities

## Estimated Time
10-12 hours

## Notes
- Prioritize data safety over performance
- Test thoroughly with various failure scenarios
- Consider implementing circuit breaker pattern for high load
- Document all error codes and recovery procedures
