# Task 8: Add Recontact API Endpoints

## Objective
Create all necessary API endpoints for recontact reservations CRUD operations and status management.

## Deliverables

### 1. Recontact Reservations CRUD
**Files**:
- `app/api/recontact-reservations/route.ts`
- `app/api/recontact-reservations/[id]/route.ts`
- `app/api/recontact-reservations/[id]/status/route.ts`

### 2. Recontact Statuses CRUD
**Files**:
- `app/api/recontact-statuses/route.ts`
- `app/api/recontact-statuses/[id]/route.ts`
- `app/api/recontact-statuses/reorder/route.ts`

### 3. Analytics and Reports
**Files**:
- `app/api/recontact-reservations/analytics/route.ts`
- `app/api/recontact-reservations/export/route.ts`

## Recontact Reservations API

### Main CRUD Endpoint

```typescript
// app/api/recontact-reservations/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import RecontactReservation from '@/models/RecontactReservation';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';
import mongoose from 'mongoose';

// GET - List recontact reservations with filtering and pagination
export const GET = async (request: NextRequest) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const statusId = searchParams.get('statusId');
    const search = searchParams.get('search');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Build filter query
    const filter: any = {};

    if (statusId) {
      filter.statusId = new mongoose.Types.ObjectId(statusId);
    }

    if (search) {
      filter.$or = [
        { 'reservation.customerInfo.client1Name': { $regex: search, $options: 'i' } },
        { 'reservation.customerInfo.client2Name': { $regex: search, $options: 'i' } },
        { 'reservation.customerInfo.phone': { $regex: search, $options: 'i' } },
        { 'reservation.customerInfo.email': { $regex: search, $options: 'i' } }
      ];
    }

    if (dateFrom || dateTo) {
      filter.recontactDate = {};
      if (dateFrom) filter.recontactDate.$gte = new Date(dateFrom);
      if (dateTo) filter.recontactDate.$lte = new Date(dateTo);
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    
    const [data, total] = await Promise.all([
      RecontactReservation.aggregate([
        { $match: filter },
        {
          $lookup: {
            from: 'recontactreservationstatuses',
            localField: 'statusId',
            foreignField: '_id',
            as: 'status'
          }
        },
        { $unwind: '$status' },
        { $sort: { recontactDate: 1, createdAt: -1 } },
        { $skip: skip },
        { $limit: limit }
      ]),
      RecontactReservation.countDocuments(filter)
    ]);

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching recontact reservations:', error);
    return NextResponse.json(
      { message: 'Failed to fetch recontact reservations' },
      { status: 500 }
    );
  }
};

// POST - Create new recontact reservation (used by transfer service)
export const POST = async (request: NextRequest) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { recontactDate, statusId, reservation } = body;

    // Validate required fields
    if (!recontactDate || !statusId || !reservation) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate status exists
    const status = await RecontactReservationStatus.findById(statusId);
    if (!status) {
      return NextResponse.json(
        { message: 'Invalid status ID' },
        { status: 400 }
      );
    }

    const recontactReservation = new RecontactReservation({
      recontactDate: new Date(recontactDate),
      statusId: new mongoose.Types.ObjectId(statusId),
      reservation,
      createdBy: session.user.id,
      updatedBy: session.user.id
    });

    await recontactReservation.save();
    
    return NextResponse.json(recontactReservation, { status: 201 });

  } catch (error) {
    console.error('Error creating recontact reservation:', error);
    return NextResponse.json(
      { message: 'Failed to create recontact reservation' },
      { status: 500 }
    );
  }
};
```

### Individual Recontact Reservation Endpoint

```typescript
// app/api/recontact-reservations/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import RecontactReservation from '@/models/RecontactReservation';
import { withApiLogging } from '@/app/api/utils/with-api-logging';
import mongoose from 'mongoose';

// GET - Get single recontact reservation
export const GET = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid ID' }, { status: 400 });
    }

    const recontactReservation = await RecontactReservation.aggregate([
      { $match: { _id: new mongoose.Types.ObjectId(id) } },
      {
        $lookup: {
          from: 'recontactreservationstatuses',
          localField: 'statusId',
          foreignField: '_id',
          as: 'status'
        }
      },
      { $unwind: '$status' }
    ]);

    if (!recontactReservation.length) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(recontactReservation[0]);

  } catch (error) {
    console.error('Error fetching recontact reservation:', error);
    return NextResponse.json(
      { message: 'Failed to fetch recontact reservation' },
      { status: 500 }
    );
  }
};

// PUT - Update recontact reservation
export const PUT = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    const body = await request.json();

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid ID' }, { status: 400 });
    }

    const updateData: any = {
      updatedBy: session.user.id
    };

    if (body.recontactDate) {
      updateData.recontactDate = new Date(body.recontactDate);
    }

    if (body.statusId) {
      updateData.statusId = new mongoose.Types.ObjectId(body.statusId);
    }

    const updatedRecontact = await RecontactReservation.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedRecontact) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedRecontact);

  } catch (error) {
    console.error('Error updating recontact reservation:', error);
    return NextResponse.json(
      { message: 'Failed to update recontact reservation' },
      { status: 500 }
    );
  }
};

// DELETE - Delete recontact reservation
export const DELETE = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid ID' }, { status: 400 });
    }

    const deletedRecontact = await RecontactReservation.findByIdAndDelete(id);

    if (!deletedRecontact) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Recontact reservation deleted successfully' });

  } catch (error) {
    console.error('Error deleting recontact reservation:', error);
    return NextResponse.json(
      { message: 'Failed to delete recontact reservation' },
      { status: 500 }
    );
  }
};
```

### Status Update Endpoint

```typescript
// app/api/recontact-reservations/[id]/status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import RecontactReservation from '@/models/RecontactReservation';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';
import mongoose from 'mongoose';

// PATCH - Update recontact reservation status
export const PATCH = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    const body = await request.json();
    const { statusId } = body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid ID' }, { status: 400 });
    }

    if (!statusId || !mongoose.Types.ObjectId.isValid(statusId)) {
      return NextResponse.json({ message: 'Invalid status ID' }, { status: 400 });
    }

    // Validate status exists
    const status = await RecontactReservationStatus.findById(statusId);
    if (!status) {
      return NextResponse.json({ message: 'Status not found' }, { status: 404 });
    }

    const updatedRecontact = await RecontactReservation.findByIdAndUpdate(
      id,
      {
        statusId: new mongoose.Types.ObjectId(statusId),
        updatedBy: session.user.id
      },
      { new: true, runValidators: true }
    );

    if (!updatedRecontact) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedRecontact);

  } catch (error) {
    console.error('Error updating recontact status:', error);
    return NextResponse.json(
      { message: 'Failed to update status' },
      { status: 500 }
    );
  }
};
```



## Acceptance Criteria

- [x] All CRUD operations work correctly
- [x] Filtering and pagination perform well
- [x] Status updates are atomic and validated
- [x] Error handling is comprehensive
- [x] Authentication is enforced
- [x] API documentation is complete

## Dependencies
- Task 1: Database models
- Authentication system
- API logging utilities
- Validation libraries

## Estimated Time
10-12 hours
