# Task 5: Add Recontact Permissions and Navigation

**Status: ✅ COMPLETED**

## Objective
Create a single permission for recontact access and add navigation item to sidebar under Reservations section.

## Deliverables

### 1. Permission Constants
**File**: `types/recontact-permission-codes.ts`

### 2. Updated Permission Types
**Files to Modify**:
- `types/permission-codes.ts`
- `components/sidebar.tsx`

### 3. Navigation Structure
Add recontact reservations as a sub-item under reservations in the sidebar.

## Permission Implementation

### Single Permission Approach

```typescript
// types/recontact-permission-codes.ts
export const RECONTACT_PERMISSIONS = {
  ACCESS_RECONTACT_RESERVATIONS: 'ACCESS_RECONTACT_RESERVATIONS'
} as const;

export type RecontactPermissionCode = typeof RECONTACT_PERMISSIONS[keyof typeof RECONTACT_PERMISSIONS];
```

### Updated Permission Types

```typescript
// Modify types/permission-codes.ts
import { RECONTACT_PERMISSIONS } from './recontact-permission-codes';

// Add to existing imports
export { RECONTACT_PERMISSIONS } from './recontact-permission-codes';

// Update PermissionCode type
export type PermissionCode =
  | typeof USER_PERMISSIONS[keyof typeof USER_PERMISSIONS]
  | typeof ROLE_PERMISSIONS[keyof typeof ROLE_PERMISSIONS]
  | typeof REGION_PERMISSIONS[keyof typeof REGION_PERMISSIONS]
  | typeof EVENT_PERMISSIONS[keyof typeof EVENT_PERMISSIONS]
  | typeof BRANCH_PERMISSIONS[keyof typeof BRANCH_PERMISSIONS]
  | typeof RESERVATION_PERMISSIONS[keyof typeof RESERVATION_PERMISSIONS]
  | typeof RECONTACT_PERMISSIONS[keyof typeof RECONTACT_PERMISSIONS] // Add this line
  | typeof PARTNER_PERMISSIONS[keyof typeof PARTNER_PERMISSIONS]
  // ... rest of existing types
```

## Navigation Implementation

### Sidebar Structure Update

```typescript
// Modify components/sidebar.tsx
import { RECONTACT_PERMISSIONS } from '@/types/recontact-permission-codes';

// Update the navigation array to include recontact as a child of reservations
const navigation: NavigationItem[] = [
  // ... existing items
  {
    labelKey: 'sidebar.reservations',
    icon: CalendarRange,
    href: '/reservations',
    permission: RESERVATION_PERMISSIONS.ACCESS_RESERVATIONS,
    customCheck: undefined,
    children: [
      {
        labelKey: 'sidebar.allReservations',
        icon: CalendarRange,
        href: '/reservations',
        permission: RESERVATION_PERMISSIONS.ACCESS_RESERVATIONS,
      },
      {
        labelKey: 'sidebar.recontactReservations',
        icon: PhoneCall, // or another appropriate icon
        href: '/recontact-reservations',
        permission: RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS,
      }
    ]
  },
  // ... rest of existing items
];
```

### Alternative Flat Structure (if preferred)

```typescript
// Alternative: Add as separate top-level item
{
  labelKey: 'sidebar.reservations',
  icon: CalendarRange,
  href: '/reservations',
  permission: RESERVATION_PERMISSIONS.ACCESS_RESERVATIONS,
  customCheck: undefined
},
{
  labelKey: 'sidebar.recontactReservations',
  icon: PhoneCall,
  href: '/recontact-reservations',
  permission: RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS,
  customCheck: undefined
},
```

## Translation Keys

### Required Translations

```json
// Add to translation files (en.json, fr.json)
{
  "sidebar": {
    "recontactReservations": "Recontact Reservations",
    "allReservations": "All Reservations"
  },
  "recontact": {
    "title": "Recontact Reservations",
    "description": "Manage reservations that require follow-up contact",
    "noPermission": "You don't have permission to access recontact reservations",
    "empty": "No recontact reservations found",
    "filters": {
      "status": "Status",
      "dateRange": "Recontact Date Range",
      "partner": "Partner"
    }
  }
}
```

### French Translations

```json
{
  "sidebar": {
    "recontactReservations": "Réservations à Recontacter",
    "allReservations": "Toutes les Réservations"
  },
  "recontact": {
    "title": "Réservations à Recontacter",
    "description": "Gérer les réservations qui nécessitent un suivi",
    "noPermission": "Vous n'avez pas la permission d'accéder aux réservations à recontacter",
    "empty": "Aucune réservation à recontacter trouvée",
    "filters": {
      "status": "Statut",
      "dateRange": "Plage de Dates de Recontact",
      "partner": "Partenaire"
    }
  }
}
```

## Redux Permission Implementation

### Permission Utility Functions

```typescript
// Add to lib/utils/permissions-utils.ts
import { RECONTACT_PERMISSIONS } from '@/types/recontact-permission-codes';

// --- RECONTACT PERMISSIONS ---
export function canUserAccessRecontactReservations(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS);
}
```

### Page-Level Protection (Redux Pattern)

```typescript
// app/recontact-reservations/page.tsx
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserAccessRecontactReservations } from '@/lib/utils/permissions-utils';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function RecontactReservationsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const permissions = useAppSelector(state => state.permissions);

  // Check permissions using Redux store
  const hasRecontactAccess = permissions && canUserAccessRecontactReservations(permissions);

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (!hasRecontactAccess) {
      toast.error('You do not have permission to access recontact reservations');
      router.push('/');
      return;
    }
  }, [session, status, hasRecontactAccess, router]);

  // Don't render if no access
  if (!hasRecontactAccess) {
    return null;
  }

  return <RecontactReservationsContent />;
}
```

### Component-Level Protection (Redux Pattern)

```typescript
// For individual components that need permission checks
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserAccessRecontactReservations } from '@/lib/utils/permissions-utils';

function RecontactActionButton() {
  const permissions = useAppSelector(state => state.permissions);
  const canAccessRecontact = canUserAccessRecontactReservations(permissions);

  if (!canAccessRecontact) {
    return null;
  }

  return <Button>Manage Recontact</Button>;
}
```

## Icon Selection

### Recommended Icons
- `PhoneCall` - Primary choice for recontact
- `Phone` - Alternative
- `UserCheck` - Another option
- `Clock` - For time-based follow-up
- `MessageCircle` - For communication aspect

```typescript
import { PhoneCall } from 'lucide-react';

// Usage in navigation
icon: PhoneCall,
```

## Database Seeding

### Default Permissions Setup

```typescript
// scripts/seed-recontact-permissions.ts
import Permission from '@/models/Permission';
import { RECONTACT_PERMISSIONS } from '@/types/recontact-permission-codes';

export async function seedRecontactPermissions() {
await dbConnect();

  const permission = {
    code: RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS,
    name: 'Access Recontact Reservations',
    description: 'View and manage recontact reservations list and details',
    category: 'recontact'
  };

  await Permission.findOneAndUpdate(
    { code: permission.code },
    permission,
    { upsert: true, new: true }
  );

  console.log('Recontact permission seeded successfully');
}
```

## Role Assignment

### Default Role Permissions

```typescript
// Suggested role assignments
const rolePermissions = {
  'SuperAdmin': [
    RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS
  ],
  'BranchesAdmin': [
    RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS
  ],
  'Agent': [
    RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS
  ]
};
```



## Acceptance Criteria

- [x] Single permission constant `ACCESS_RECONTACT_RESERVATIONS` is defined
- [x] Permission types are updated correctly
- [x] Permission utility function `canUserAccessRecontactReservations` added to permissions-utils.ts
- [x] Navigation includes recontact reservations item with permission check
- [x] Redux-based permission checking protects recontact functionality
- [ ] Pages redirect unauthorized users appropriately
- [ ] Components hide for unauthorized users
- [x] Translations are added for all languages
- [x] Icons are appropriate and consistent
- [x] Database seeding script creates the permission
- [x] Role assignments include the single permission
- [x] Navigation works on all screen sizes

## Dependencies
- Existing permission system
- Sidebar navigation component
- Translation system
- Permission guard components

## Estimated Time
2-3 hours

## Notes
- Single permission approach simplifies implementation and maintenance
- The `ACCESS_RECONTACT_RESERVATIONS` permission covers both viewing and managing recontact reservations
- Consider the navigation hierarchy carefully
- Test with different user roles
- Follow existing permission naming conventions
- Future expansion can add more granular permissions if needed
