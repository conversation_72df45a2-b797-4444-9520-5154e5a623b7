# Phase 6: Migration Strategy ⚠️ NOT REQUIRED

## ⚠️ STATUS: NOT REQUIRED
**Reason:** Database is empty - no existing invoice data to migrate
**Decision:** Skip migration phase as there are no existing invoices to convert
**Alternative:** New invoices will automatically use the 7-status system

## Overview
~~Safely migrate existing invoice data to the new 7-status system with rollback capabilities and minimal downtime.~~

**Note:** This migration strategy was prepared for production systems with existing data. Since the current database is empty, this entire phase can be skipped. All new invoices created will automatically use the new 7-status system.

## Tasks

### 6.1 Create Migration Script

**File:** `scripts/migrate-invoice-statuses.ts`

```typescript
import mongoose from 'mongoose';
import Invoice from '@/models/Invoice';

interface MigrationStats {
  totalInvoices: number;
  migrated: number;
  skipped: number;
  errors: number;
  statusMapping: Record<string, number>;
}

export async function migrateInvoiceStatuses(dryRun: boolean = true): Promise<MigrationStats> {
await dbConnect();

  const stats: MigrationStats = {
    totalInvoices: 0,
    migrated: 0,
    skipped: 0,
    errors: 0,
    statusMapping: {}
  };

  // Status mapping from old to new system
  const statusMapping = {
    'new': 'nouveau',
    'processing': 'en_traitement',
    'paid': 'paye',
    // Add any other existing statuses
    'sent': 'envoye',
    'signed': 'signe',
    'verified': 'verifie',
    'late': 'en_retard'
  };

  console.log(`Starting migration (${dryRun ? 'DRY RUN' : 'LIVE'})...`);

  try {
    // Get all invoices
    const invoices = await Invoice.find({}).lean();
    stats.totalInvoices = invoices.length;

    console.log(`Found ${invoices.length} invoices to migrate`);

    for (const invoice of invoices) {
      try {
        const oldStatus = invoice.status;
        const newStatus = statusMapping[oldStatus] || 'nouveau';

        // Track status mapping statistics
        stats.statusMapping[oldStatus] = (stats.statusMapping[oldStatus] || 0) + 1;

        // Skip if already using new status system
        if (Object.values(statusMapping).includes(oldStatus)) {
          stats.skipped++;
          continue;
        }

        if (!dryRun) {
          // Create status history entry
          const statusHistory = [{
            status: newStatus,
            changedAt: invoice.createdAt || new Date(),
            isAutomatic: false,
            reason: 'Migration from old status system'
          }];

          // Add additional history entries based on existing data
          if (invoice.sentAt) {
            statusHistory.push({
              status: 'envoye',
              changedAt: invoice.sentAt,
              isAutomatic: false,
              reason: 'Migrated: Email was sent'
            });
          }

          if (invoice.signedAt) {
            statusHistory.push({
              status: 'signe',
              changedAt: invoice.signedAt,
              isAutomatic: false,
              reason: 'Migrated: Invoice was signed'
            });
          }

          // Sort history by date
          statusHistory.sort((a, b) => a.changedAt.getTime() - b.changedAt.getTime());

          // Update the invoice
          await Invoice.updateOne(
            { _id: invoice._id },
            {
              $set: {
                status: statusHistory[statusHistory.length - 1].status, // Latest status
                statusHistory: statusHistory
              }
            }
          );
        }

        stats.migrated++;

        if (stats.migrated % 100 === 0) {
          console.log(`Migrated ${stats.migrated}/${stats.totalInvoices} invoices...`);
        }
      } catch (error) {
        console.error(`Error migrating invoice ${invoice._id}:`, error);
        stats.errors++;
      }
    }

    console.log('Migration completed!');
    console.log('Statistics:', stats);

    return stats;
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// CLI execution
if (require.main === module) {
  const dryRun = process.argv.includes('--dry-run');
  const force = process.argv.includes('--force');

  if (!dryRun && !force) {
    console.error('Use --dry-run to test or --force to execute migration');
    process.exit(1);
  }

  migrateInvoiceStatuses(dryRun)
    .then((stats) => {
      console.log('Migration completed successfully:', stats);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
```

### 6.2 Create Rollback Script

**File:** `scripts/rollback-invoice-statuses.ts`

```typescript
import mongoose from 'mongoose';
import Invoice from '@/models/Invoice';

interface RollbackStats {
  totalInvoices: number;
  rolledBack: number;
  skipped: number;
  errors: number;
}

export async function rollbackInvoiceStatuses(dryRun: boolean = true): Promise<RollbackStats> {
await dbConnect();

  const stats: RollbackStats = {
    totalInvoices: 0,
    rolledBack: 0,
    skipped: 0,
    errors: 0
  };

  // Reverse mapping from new to old system
  const reverseMapping = {
    'nouveau': 'new',
    'verifie': 'new', // Map back to new if no better option
    'envoye': 'sent',
    'signe': 'signed',
    'en_traitement': 'processing',
    'paye': 'paid',
    'en_retard': 'late'
  };

  console.log(`Starting rollback (${dryRun ? 'DRY RUN' : 'LIVE'})...`);

  try {
    // Get all invoices with new status system
    const invoices = await Invoice.find({
      status: { $in: Object.keys(reverseMapping) }
    }).lean();

    stats.totalInvoices = invoices.length;
    console.log(`Found ${invoices.length} invoices to rollback`);

    for (const invoice of invoices) {
      try {
        const newStatus = invoice.status;
        const oldStatus = reverseMapping[newStatus];

        if (!oldStatus) {
          stats.skipped++;
          continue;
        }

        if (!dryRun) {
          await Invoice.updateOne(
            { _id: invoice._id },
            {
              $set: { status: oldStatus },
              $unset: { statusHistory: 1 } // Remove status history
            }
          );
        }

        stats.rolledBack++;

        if (stats.rolledBack % 100 === 0) {
          console.log(`Rolled back ${stats.rolledBack}/${stats.totalInvoices} invoices...`);
        }
      } catch (error) {
        console.error(`Error rolling back invoice ${invoice._id}:`, error);
        stats.errors++;
      }
    }

    console.log('Rollback completed!');
    console.log('Statistics:', stats);

    return stats;
  } catch (error) {
    console.error('Rollback failed:', error);
    throw error;
  }
}

// CLI execution
if (require.main === module) {
  const dryRun = process.argv.includes('--dry-run');
  const force = process.argv.includes('--force');

  if (!dryRun && !force) {
    console.error('Use --dry-run to test or --force to execute rollback');
    process.exit(1);
  }

  rollbackInvoiceStatuses(dryRun)
    .then((stats) => {
      console.log('Rollback completed successfully:', stats);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Rollback failed:', error);
      process.exit(1);
    });
}
```

### 6.3 Create Feature Flag System

**File:** `lib/feature-flags.ts`

```typescript
export interface FeatureFlags {
  newInvoiceStatusSystem: boolean;
  invoiceAutomation: boolean;
  statusHistoryTracking: boolean;
}

export function getFeatureFlags(): FeatureFlags {
  return {
    newInvoiceStatusSystem: process.env.FEATURE_NEW_INVOICE_STATUS === 'true',
    invoiceAutomation: process.env.FEATURE_INVOICE_AUTOMATION === 'true',
    statusHistoryTracking: process.env.FEATURE_STATUS_HISTORY === 'true'
  };
}

export function isFeatureEnabled(feature: keyof FeatureFlags): boolean {
  const flags = getFeatureFlags();
  return flags[feature];
}
```

### 6.4 Create Gradual Rollout Component

**File:** `app/billing/components/StatusSystemWrapper.tsx`

```typescript
'use client';

import { useEffect, useState } from 'react';
import { isFeatureEnabled } from '@/lib/feature-flags';
import { InvoiceKanbanBoard } from './InvoiceKanbanBoard';
import { LegacyInvoiceList } from './LegacyInvoiceList';

export function StatusSystemWrapper({ invoices, onInvoiceUpdate }) {
  const [useNewSystem, setUseNewSystem] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check feature flag
    const newSystemEnabled = isFeatureEnabled('newInvoiceStatusSystem');
    setUseNewSystem(newSystemEnabled);
    setIsLoading(false);
  }, []);

  if (isLoading) {
    return <div className="animate-pulse">Loading...</div>;
  }

  if (useNewSystem) {
    return (
      <div>
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700">
            🆕 Nouveau système de statuts activé avec 7 statuts et automatisation
          </p>
        </div>
        <InvoiceKanbanBoard invoices={invoices} onInvoiceUpdate={onInvoiceUpdate} />
      </div>
    );
  }

  return <LegacyInvoiceList invoices={invoices} onInvoiceUpdate={onInvoiceUpdate} />;
}
```

### 6.5 Create Migration API Endpoints

**File:** `app/api/admin/migration/invoice-statuses/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { migrateInvoiceStatuses, rollbackInvoiceStatuses } from '@/scripts/migrate-invoice-statuses';

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  // Only allow admin users
  if (!session || !session.user.permissions?.includes('ADMIN')) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { action, dryRun = true } = await request.json();

    let result;
    switch (action) {
      case 'migrate':
        result = await migrateInvoiceStatuses(dryRun);
        break;
      case 'rollback':
        result = await rollbackInvoiceStatuses(dryRun);
        break;
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      action,
      dryRun,
      stats: result
    });
  } catch (error) {
    console.error('Migration API error:', error);
    return NextResponse.json({
      error: 'Migration failed',
      details: error.message
    }, { status: 500 });
  }
}
```

### 6.6 Create Migration Dashboard

**File:** `app/admin/migration/page.tsx`

```typescript
'use client';

import { useState } from 'react';

export default function MigrationDashboard() {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState(null);

  const runMigration = async (action: 'migrate' | 'rollback', dryRun: boolean) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/migration/invoice-statuses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, dryRun })
      });

      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('Migration failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Invoice Status Migration</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="p-4 border rounded-lg">
          <h2 className="font-semibold mb-3">Migration Actions</h2>
          <div className="space-y-2">
            <button
              onClick={() => runMigration('migrate', true)}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
            >
              Test Migration (Dry Run)
            </button>
            <button
              onClick={() => runMigration('migrate', false)}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              Run Migration (LIVE)
            </button>
          </div>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="font-semibold mb-3">Rollback Actions</h2>
          <div className="space-y-2">
            <button
              onClick={() => runMigration('rollback', true)}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 disabled:opacity-50"
            >
              Test Rollback (Dry Run)
            </button>
            <button
              onClick={() => runMigration('rollback', false)}
              disabled={isLoading}
              className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
            >
              Run Rollback (LIVE)
            </button>
          </div>
        </div>
      </div>

      {isLoading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2">Running migration...</p>
        </div>
      )}

      {results && (
        <div className="mt-6 p-4 border rounded-lg">
          <h3 className="font-semibold mb-3">Migration Results</h3>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
```

### 6.7 Create Environment Configuration

**File:** `.env.example`

```bash
# Invoice Status System Feature Flags
FEATURE_NEW_INVOICE_STATUS=false
FEATURE_INVOICE_AUTOMATION=false
FEATURE_STATUS_HISTORY=false

# Migration Settings
MIGRATION_BATCH_SIZE=100
MIGRATION_DELAY_MS=100
```

## Deployment Strategy

### Phase 1: Preparation
1. Deploy code with feature flags disabled
2. Run migration dry-run to validate data
3. Create database backup

### Phase 2: Gradual Rollout
1. Enable `FEATURE_STATUS_HISTORY=true` first
2. Monitor for 24 hours
3. Enable `FEATURE_NEW_INVOICE_STATUS=true`
4. Monitor for 48 hours
5. Enable `FEATURE_INVOICE_AUTOMATION=true`

### Phase 3: Full Migration
1. Run live migration during low-traffic period
2. Monitor system performance
3. Validate data integrity
4. Remove feature flags after 1 week

## Acceptance Criteria

- [ ] Migration script with dry-run capability
- [ ] Rollback script for emergency recovery
- [ ] Feature flag system for gradual rollout
- [ ] Migration API endpoints for admin control
- [ ] Migration dashboard for monitoring
- [ ] Data validation and integrity checks
- [ ] Backup and recovery procedures
- [ ] Performance monitoring during migration
- [ ] Error handling and logging
- [ ] Documentation for deployment team

## Dependencies
- All previous phases (1-5)

## Estimated Time
- 6-8 hours

## Testing Requirements
- Migration script testing with sample data
- Rollback testing
- Feature flag testing
- Performance testing with large datasets
- Data integrity validation
