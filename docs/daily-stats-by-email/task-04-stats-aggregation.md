# Task 4: Statistics Aggregation Service

**Estimated Time**: 35 minutes  
**Priority**: High  
**Dependencies**: Task 2 (API Endpoints), Task 3 (Email Template)

## Overview

Build a comprehensive service to aggregate all required statistics from existing APIs, optimize database queries, and calculate branch-specific and top performer metrics.

## Subtask 4.1: Create Stats Aggregation Service

### File Location
`lib/services/stats-aggregation-service.ts`

### Core Service Structure
```typescript
import Reservation from '@/models/Reservation';
import User from '@/models/User';
import Branch from '@/models/Branch';
import { ObjectId } from 'mongodb';

export interface DailyStatsResult {
  date: string;
  totalSales: {
    amount: number;
    count: number;
  };
  totalReservations: number;
  totalPresence: number;
  salesByBranch: BranchSalesData[];
  reservationsByBranch: BranchReservationData[];
  presenceByBranch: BranchPresenceData[];
  topSellers: TopPerformerData[];
  topPaps: TopPerformerData[];
  executionMetrics: {
    totalQueries: number;
    executionTime: number;
    cacheHits: number;
  };
}

export interface BranchSalesData {
  branchId: string;
  branchName: string;
  amount: number;
  count: number;
}

export interface BranchReservationData {
  branchId: string;
  branchName: string;
  count: number;
}

export interface BranchPresenceData {
  branchId: string;
  branchName: string;
  count: number;
}

export interface TopPerformerData {
  userId: string;
  name: string;
  amount?: number;
  count: number;
}

export class StatsAggregationService {
  private static instance: StatsAggregationService;
  private queryCount = 0;
  private cacheHits = 0;
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  public static getInstance(): StatsAggregationService {
    if (!StatsAggregationService.instance) {
      StatsAggregationService.instance = new StatsAggregationService();
    }
    return StatsAggregationService.instance;
  }

  public async aggregateDailyStats(date: string): Promise<DailyStatsResult> {
    const startTime = Date.now();
    this.queryCount = 0;
    this.cacheHits = 0;

  await dbConnect();

    const { startOfDay, endOfDay } = this.getDateRange(date);

    // Execute all aggregations in parallel for performance
    const [
      totalSales,
      totalReservations,
      totalPresence,
      salesByBranch,
      reservationsByBranch,
      presenceByBranch,
      topSellers,
      topPaps
    ] = await Promise.all([
      this.calculateTotalSales(startOfDay, endOfDay),
      this.calculateTotalReservations(startOfDay, endOfDay),
      this.calculateTotalPresence(startOfDay, endOfDay),
      this.calculateSalesByBranch(startOfDay, endOfDay),
      this.calculateReservationsByBranch(startOfDay, endOfDay),
      this.calculatePresenceByBranch(startOfDay, endOfDay),
      this.calculateTopSellers(startOfDay, endOfDay, 5),
      this.calculateTopPaps(startOfDay, endOfDay, 5)
    ]);

    const executionTime = Date.now() - startTime;

    return {
      date,
      totalSales,
      totalReservations,
      totalPresence,
      salesByBranch,
      reservationsByBranch,
      presenceByBranch,
      topSellers,
      topPaps,
      executionMetrics: {
        totalQueries: this.queryCount,
        executionTime,
        cacheHits: this.cacheHits
      }
    };
  }

  private getDateRange(date: string) {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    return { startOfDay, endOfDay };
  }

  private getCacheKey(operation: string, params: any): string {
    return `${operation}:${JSON.stringify(params)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      this.cacheHits++;
      return cached.data as T;
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }
}
```

## Subtask 4.2: Branch-Specific Calculations

### Sales by Branch Implementation
```typescript
private async calculateSalesByBranch(startOfDay: Date, endOfDay: Date): Promise<BranchSalesData[]> {
  const cacheKey = this.getCacheKey('salesByBranch', { startOfDay, endOfDay });
  const cached = this.getFromCache<BranchSalesData[]>(cacheKey);
  if (cached) return cached;

  this.queryCount++;

  const pipeline = [
    {
      $match: {
        'preferences.visitDate': {
          $gte: startOfDay.toISOString().split('T')[0],
          $lte: endOfDay.toISOString().split('T')[0]
        },
        sellingAmount: { $gt: 0 },
        deletedAt: null
      }
    },
    {
      $group: {
        _id: '$preferences.branchId',
        amount: { $sum: '$sellingAmount' },
        count: { $sum: 1 }
      }
    },
    {
      $lookup: {
        from: 'branches',
        localField: '_id',
        foreignField: '_id',
        as: 'branch'
      }
    },
    {
      $unwind: '$branch'
    },
    {
      $project: {
        branchId: { $toString: '$_id' },
        branchName: '$branch.name',
        amount: 1,
        count: 1
      }
    },
    {
      $sort: { amount: -1 }
    }
  ];

  const results = await Reservation.aggregate(pipeline);
  this.setCache(cacheKey, results);
  return results;
}

private async calculateReservationsByBranch(startOfDay: Date, endOfDay: Date): Promise<BranchReservationData[]> {
  const cacheKey = this.getCacheKey('reservationsByBranch', { startOfDay, endOfDay });
  const cached = this.getFromCache<BranchReservationData[]>(cacheKey);
  if (cached) return cached;

  this.queryCount++;

  const pipeline = [
    {
      $match: {
        createdAt: {
          $gte: startOfDay,
          $lte: endOfDay
        },
        deletedAt: null
      }
    },
    {
      $group: {
        _id: '$preferences.branchId',
        count: { $sum: 1 }
      }
    },
    {
      $lookup: {
        from: 'branches',
        localField: '_id',
        foreignField: '_id',
        as: 'branch'
      }
    },
    {
      $unwind: '$branch'
    },
    {
      $project: {
        branchId: { $toString: '$_id' },
        branchName: '$branch.name',
        count: 1
      }
    },
    {
      $sort: { count: -1 }
    }
  ];

  const results = await Reservation.aggregate(pipeline);
  this.setCache(cacheKey, results);
  return results;
}

private async calculatePresenceByBranch(startOfDay: Date, endOfDay: Date): Promise<BranchPresenceData[]> {
  const cacheKey = this.getCacheKey('presenceByBranch', { startOfDay, endOfDay });
  const cached = this.getFromCache<BranchPresenceData[]>(cacheKey);
  if (cached) return cached;

  this.queryCount++;

  const pipeline = [
    {
      $match: {
        'preferences.visitDate': {
          $gte: startOfDay.toISOString().split('T')[0],
          $lte: endOfDay.toISOString().split('T')[0]
        },
        presentAt: { $ne: null },
        deletedAt: null
      }
    },
    {
      $group: {
        _id: '$preferences.branchId',
        count: { $sum: 1 }
      }
    },
    {
      $lookup: {
        from: 'branches',
        localField: '_id',
        foreignField: '_id',
        as: 'branch'
      }
    },
    {
      $unwind: '$branch'
    },
    {
      $project: {
        branchId: { $toString: '$_id' },
        branchName: '$branch.name',
        count: 1
      }
    },
    {
      $sort: { count: -1 }
    }
  ];

  const results = await Reservation.aggregate(pipeline);
  this.setCache(cacheKey, results);
  return results;
}
```

## Subtask 4.3: Top Performers Calculation

### Top Sellers Implementation
```typescript
private async calculateTopSellers(startOfDay: Date, endOfDay: Date, limit: number): Promise<TopPerformerData[]> {
  const cacheKey = this.getCacheKey('topSellers', { startOfDay, endOfDay, limit });
  const cached = this.getFromCache<TopPerformerData[]>(cacheKey);
  if (cached) return cached;

  this.queryCount++;

  const pipeline = [
    {
      $match: {
        'preferences.visitDate': {
          $gte: startOfDay.toISOString().split('T')[0],
          $lte: endOfDay.toISOString().split('T')[0]
        },
        sellingAmount: { $gt: 0 },
        assigned_user_id: { $ne: null },
        deletedAt: null
      }
    },
    {
      $group: {
        _id: '$assigned_user_id',
        amount: { $sum: '$sellingAmount' },
        count: { $sum: 1 }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        userId: { $toString: '$_id' },
        name: '$user.name',
        amount: 1,
        count: 1
      }
    },
    {
      $sort: { amount: -1 }
    },
    {
      $limit: limit
    }
  ];

  const results = await Reservation.aggregate(pipeline);
  this.setCache(cacheKey, results);
  return results;
}

private async calculateTopPaps(startOfDay: Date, endOfDay: Date, limit: number): Promise<TopPerformerData[]> {
  const cacheKey = this.getCacheKey('topPaps', { startOfDay, endOfDay, limit });
  const cached = this.getFromCache<TopPerformerData[]>(cacheKey);
  if (cached) return cached;

  this.queryCount++;

  const pipeline = [
    {
      $match: {
        'preferences.visitDate': {
          $gte: startOfDay.toISOString().split('T')[0],
          $lte: endOfDay.toISOString().split('T')[0]
        },
        partnerId: { $ne: null },
        deletedAt: null
      }
    },
    {
      $group: {
        _id: '$partnerId',
        count: { $sum: 1 }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        userId: { $toString: '$_id' },
        name: '$user.name',
        count: 1
      }
    },
    {
      $sort: { count: -1 }
    },
    {
      $limit: limit
    }
  ];

  const results = await Reservation.aggregate(pipeline);
  this.setCache(cacheKey, results);
  return results;
}
```

## Subtask 4.4: Database Query Optimization

### Total Statistics Implementation
```typescript
private async calculateTotalSales(startOfDay: Date, endOfDay: Date): Promise<{ amount: number; count: number }> {
  const cacheKey = this.getCacheKey('totalSales', { startOfDay, endOfDay });
  const cached = this.getFromCache<{ amount: number; count: number }>(cacheKey);
  if (cached) return cached;

  this.queryCount++;

  const pipeline = [
    {
      $match: {
        'preferences.visitDate': {
          $gte: startOfDay.toISOString().split('T')[0],
          $lte: endOfDay.toISOString().split('T')[0]
        },
        sellingAmount: { $gt: 0 },
        deletedAt: null
      }
    },
    {
      $group: {
        _id: null,
        amount: { $sum: '$sellingAmount' },
        count: { $sum: 1 }
      }
    }
  ];

  const results = await Reservation.aggregate(pipeline);
  const result = results[0] || { amount: 0, count: 0 };
  
  this.setCache(cacheKey, result);
  return result;
}

private async calculateTotalReservations(startOfDay: Date, endOfDay: Date): Promise<number> {
  const cacheKey = this.getCacheKey('totalReservations', { startOfDay, endOfDay });
  const cached = this.getFromCache<number>(cacheKey);
  if (cached !== null) return cached;

  this.queryCount++;

  const count = await Reservation.countDocuments({
    createdAt: {
      $gte: startOfDay,
      $lte: endOfDay
    },
    deletedAt: null
  });

  this.setCache(cacheKey, count);
  return count;
}

private async calculateTotalPresence(startOfDay: Date, endOfDay: Date): Promise<number> {
  const cacheKey = this.getCacheKey('totalPresence', { startOfDay, endOfDay });
  const cached = this.getFromCache<number>(cacheKey);
  if (cached !== null) return cached;

  this.queryCount++;

  const count = await Reservation.countDocuments({
    'preferences.visitDate': {
      $gte: startOfDay.toISOString().split('T')[0],
      $lte: endOfDay.toISOString().split('T')[0]
    },
    presentAt: { $ne: null },
    deletedAt: null
  });

  this.setCache(cacheKey, count);
  return count;
}
```

## Debug Component: Stats Calculation Validator

### File Location
`app/admin-dashboard/components/debug/stats-aggregation-debug.tsx`

### Debug Features

1. **Performance Monitoring**
   - Query execution times
   - Cache hit rates
   - Memory usage tracking
   - Database connection monitoring

2. **Data Validation**
   - Cross-reference with existing APIs
   - Validate calculation accuracy
   - Test edge cases
   - Compare aggregated vs individual queries

3. **Query Analysis**
   - Explain query execution plans
   - Index usage analysis
   - Optimization recommendations
   - Bottleneck identification

4. **Cache Management**
   - Cache hit/miss statistics
   - Cache invalidation testing
   - TTL effectiveness analysis
   - Memory usage monitoring

### Debug Interface
```typescript
interface StatsAggregationDebugProps {
  onPerformanceTest: (results: PerformanceTestResults) => void;
  onValidationTest: (results: ValidationTestResults) => void;
}

interface PerformanceTestResults {
  executionTimes: Record<string, number>;
  cacheMetrics: {
    hits: number;
    misses: number;
    hitRate: number;
  };
  queryMetrics: {
    totalQueries: number;
    slowQueries: QueryAnalysis[];
  };
  recommendations: string[];
}

interface ValidationTestResults {
  totalSalesAccuracy: boolean;
  branchDataConsistency: boolean;
  topPerformersValidity: boolean;
  dataIntegrityChecks: boolean;
  errors: string[];
}
```

### Debug Actions
- **Run Performance Test**: Comprehensive performance analysis
- **Validate Calculations**: Cross-check with existing data
- **Analyze Queries**: Database query optimization
- **Test Cache**: Cache effectiveness testing
- **Generate Report**: Detailed performance report

## Implementation Checklist

### Subtask 4.1: Service Creation
- [x] Create aggregation service file
- [x] Implement core service structure
- [x] Add caching mechanism
- [x] Implement parallel processing
- [x] Test service initialization

### Subtask 4.2: Branch Calculations
- [x] Implement sales by branch
- [x] Implement reservations by branch
- [x] Implement presence by branch
- [x] Add branch name resolution
- [x] Test branch aggregations

### Subtask 4.3: Top Performers
- [x] Implement top sellers calculation
- [x] Implement top PAPs calculation
- [x] Add user name resolution
- [x] Test performer rankings
- [x] Validate calculation accuracy

### Subtask 4.4: Optimization
- [x] Implement total statistics
- [x] Add database indexes
- [x] Optimize aggregation pipelines
- [x] Test query performance
- [x] Add error handling

### Debug Component
- [x] Create debug component
- [x] Implement performance monitoring
- [x] Add validation testing
- [x] Create query analysis tools
- [x] Test all debug features



## Success Criteria

- [x] All statistics calculated accurately
- [x] Query performance meets requirements (<2 seconds)
- [x] Cache provides significant performance improvement
- [x] Debug component enables comprehensive testing
- [x] Service handles edge cases gracefully
- [x] Database queries are optimized

## Next Steps

After completing this task:
1. Proceed to Task 5: Email Rendering and Sending Service
2. Integrate aggregated stats with email template
3. Test complete stats-to-email workflow
