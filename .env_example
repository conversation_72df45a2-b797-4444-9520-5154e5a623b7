# Authentication
NEXTAUTH_URL=http://localhost:5000
# NEXTAUTH_URL=https://your-production-domain.com  # for production
NEXTAUTH_SECRET=your-long-secret-value-here
NODE_ENV=development
NEXT_PUBLIC_COOKIE_DOMAIN=.knock-pro.com
# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:5000

# Database
MONGODB_URI=mongodb+srv://knockprocom:<EMAIL>/amqpartners

# Session Configuration
NEXTAUTH_JWT_SECRET=another-secure-random-string-here # For JWT signing
NEXTAUTH_SESSION_MAXAGE=2592000 # 30 days in seconds

EN_PATH=G:\qa_shop\src\locales\en.json
FR_PATH=G:\qa_shop\src\locales\fr.json

#N_PATH=/var/www/qa_shop/qa_shop/src/locales/en.json
#FR_PATH=/var/www/qa_shop/qa_shop/src/locales/fr.json

# Email Service Configuration (removed)

# Brevo Email Service Configuration
BREVO_API_KEY=your-brevo-api-key
BREVO_SENDER_EMAIL=<EMAIL>
BREVO_SENDER_NAME=AMQ Partners

# Google Gemini API
GOOGLE_GENAI_API_KEY=your-gemini-api-key-here

# Sentry Configuration
SENTRY_DSN=your-sentry-dsn-here
