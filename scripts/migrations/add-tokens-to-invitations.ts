import Invitation from '../../models/Invitation';
import { generateUniqueToken } from '../../app/invitations/utils/token-generator';
import dbConnect from '../../lib/db';
/**
 * Migration script to add tokens to any existing invitations
 * This script can be run with either:
 * - npm run migrate-invitations (tsx)
 * - npm run migration:invitation-tokens (ts-node)
 */
async function migrateInvitations() {
  try {
    // Connect to the database
  await dbConnect();
    console.log('Connected to database');

    // Find all invitations without tokens
    const invitationsWithoutTokens = await Invitation.find({ token: { $exists: false } });
    console.log(`Found ${invitationsWithoutTokens.length} invitations without tokens`);

    // If no invitations need migration, exit
    if (invitationsWithoutTokens.length === 0) {
      console.log('No invitations need migration. Exiting.');
      process.exit(0);
    }

    // Get existing tokens to avoid duplicates
    const existingInvitations = await Invitation.find({ token: { $exists: true } }).select('token');
    const existingTokens = existingInvitations.map(inv => inv.token);
    console.log(`Found ${existingTokens.length} existing tokens to avoid duplicates`);

    // Update each invitation with a new token
    let updatedCount = 0;
    for (const invitation of invitationsWithoutTokens) {
      const token = generateUniqueToken(existingTokens);
      existingTokens.push(token); // Add to our list to avoid duplicates in future iterations
      
      await Invitation.updateOne(
        { _id: invitation._id },
        { $set: { token } }
      );
      
      updatedCount++;
      
      // Log progress for every 10 invitations
      if (updatedCount % 10 === 0) {
        console.log(`Updated ${updatedCount}/${invitationsWithoutTokens.length} invitations`);
      }
    }

    console.log(`Migration complete. Updated ${updatedCount} invitations with new tokens.`);
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateInvitations(); 