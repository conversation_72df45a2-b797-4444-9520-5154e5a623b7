import Invitation from '../../models/Invitation';
import { generateUniqueTokenBatch } from '../../app/invitations/utils/token-generator';
import dbConnect from '../../lib/db';
/**
 * Migration script to update existing invitation tokens to the new format
 * Old format: ABC1234 (3 letters + 4 digits)
 * New format: XXX-XXXXX-XXXXX (15 digits with dashes)
 */
async function migrateInvitationTokens() {
  try {
    console.log('Starting invitation token format migration...');
    
  await dbConnect();
    
    // Find all invitations with old format tokens (7 characters without dashes)
    const oldFormatInvitations = await Invitation.find({
      token: { $regex: /^[A-Z]{3}\d{4}$/ } // Matches old format: 3 letters + 4 digits
    }).select('_id token groupId partner_id').lean();
    
    console.log(`Found ${oldFormatInvitations.length} invitations with old format tokens`);
    
    if (oldFormatInvitations.length === 0) {
      console.log('No invitations found with old format tokens. Migration complete.');
      process.exit(0);
    }
    
    // Group invitations by groupId for efficient token generation
    const invitationsByGroup = new Map<string, any[]>();
    
    oldFormatInvitations.forEach(invitation => {
      const groupKey = invitation.groupId?.toString() || 'null';
      if (!invitationsByGroup.has(groupKey)) {
        invitationsByGroup.set(groupKey, []);
      }
      invitationsByGroup.get(groupKey)!.push(invitation);
    });
    
    console.log(`Processing ${invitationsByGroup.size} groups...`);
    
    let totalUpdated = 0;
    
    // Process each group separately to ensure uniqueness within groups
    for (const [groupKey, groupInvitations] of invitationsByGroup) {
      const groupId = groupKey === 'null' ? null : groupKey;
      const partnerId = groupInvitations[0]?.partner_id?.toString();
      
      console.log(`Processing group ${groupKey} with ${groupInvitations.length} invitations...`);
      
      // Generate new unique tokens for this group
      const newTokens = await generateUniqueTokenBatch(
        groupInvitations.length,
        groupId,
        partnerId
      );
      
      // Update each invitation with its new token
      for (let i = 0; i < groupInvitations.length; i++) {
        const invitation = groupInvitations[i];
        const newToken = newTokens[i];
        
        try {
          await Invitation.updateOne(
            { _id: invitation._id },
            { $set: { token: newToken, updated_at: new Date() } }
          );
          
          totalUpdated++;
          
          // Log progress for every 100 invitations
          if (totalUpdated % 100 === 0) {
            console.log(`Updated ${totalUpdated}/${oldFormatInvitations.length} invitations`);
          }
        } catch (error: any) {
          console.error(`Failed to update invitation ${invitation._id}:`, error.message);
          
          // If there's a duplicate key error, generate a new token and retry
          if (error.code === 11000) {
            console.log(`Duplicate token detected for invitation ${invitation._id}, generating new token...`);
            const retryTokens = await generateUniqueTokenBatch(1, groupId, partnerId);
            await Invitation.updateOne(
              { _id: invitation._id },
              { $set: { token: retryTokens[0], updated_at: new Date() } }
            );
            totalUpdated++;
          }
        }
      }
      
      console.log(`Completed group ${groupKey}`);
    }
    
    console.log(`Migration complete. Updated ${totalUpdated} invitations with new token format.`);
    
    // Verify the migration
    const remainingOldTokens = await Invitation.countDocuments({
      token: { $regex: /^[A-Z]{3}\d{4}$/ }
    });
    
    if (remainingOldTokens > 0) {
      console.warn(`Warning: ${remainingOldTokens} invitations still have old format tokens`);
    } else {
      console.log('✅ All invitations now have the new token format');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateInvitationTokens();
