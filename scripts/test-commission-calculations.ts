import mongoose from 'mongoose';
import { EventCommissionCalculationService } from '../lib/services/event-commission-calculation';
import { EventReport } from '../models/EventReport';
import Event from '../models/Event';
import Reservation from '../models/Reservation';
import User from '../models/User';
import CommissionConfiguration from '../models/CommissionConfiguration';
import Commission from '../models/Commission';
import dbConnect from '@/lib/db';
interface TestScenario {
  name: string;
  description: string;
  setup: () => Promise<any>;
  expectedResults: {
    papCommissions?: number;
    cookCommissions?: number;
    supervisorCommissions?: number;
    totalAmount?: number;
    errorCount?: number;
  };
}

class CommissionCalculationTester {
  private testResults: any[] = [];

  async runAllTests() {
    console.log('🧪 Starting Commission Calculation Tests\n');

    const scenarios: TestScenario[] = [
      {
        name: 'Basic Commission Calculation',
        description: 'Test basic commission calculation with standard configuration',
        setup: () => this.setupBasicScenario(),
        expectedResults: {
          papCommissions: 14, // 2 reservations * $7
          cookCommissions: 4,  // 2 reservations * $2
          supervisorCommissions: 4, // 2 reservations * $2
          totalAmount: 22
        }
      },
      {
        name: 'PAP Time-Based Eligibility',
        description: 'Test PAP commission eligibility based on 3-week scheduling rule',
        setup: () => this.setupTimeBasedScenario(),
        expectedResults: {
          papCommissions: 7, // Only 1 reservation qualifies (scheduled <3 weeks)
          cookCommissions: 4, // All reservations count for cooks
          supervisorCommissions: 4, // All reservations count for supervisors
          totalAmount: 15
        }
      },
      {
        name: 'Cook Time Proportion',
        description: 'Test cook commission distribution based on time worked',
        setup: () => this.setupTimeProportionScenario(),
        expectedResults: {
          cookCommissions: 4, // $2 per reservation, split by time proportion
          totalAmount: 18 // PAP + Cook + Supervisor commissions
        }
      },
      {
        name: 'Multiple Supervisors',
        description: 'Test commission calculation with multiple supervisors',
        setup: () => this.setupMultipleSupervisorsScenario(),
        expectedResults: {
          supervisorCommissions: 8, // 2 supervisors * 2 reservations * $2
          totalAmount: 26 // PAP + Cook + Supervisor commissions
        }
      },
      {
        name: 'No Reservations',
        description: 'Test commission calculation with no linked reservations',
        setup: () => this.setupNoReservationsScenario(),
        expectedResults: {
          totalAmount: 0,
          errorCount: 0
        }
      },
      {
        name: 'Edge Case: Zero Hours Cook',
        description: 'Test handling of cook with zero hours worked',
        setup: () => this.setupZeroHoursCookScenario(),
        expectedResults: {
          errorCount: 1 // Should generate an error for zero hours
        }
      }
    ];

    for (const scenario of scenarios) {
      await this.runTestScenario(scenario);
    }

    this.printTestSummary();
  }

  private async runTestScenario(scenario: TestScenario) {
    console.log(`📋 Running: ${scenario.name}`);
    console.log(`   ${scenario.description}`);

    try {
      // Setup test data
      const testData = await scenario.setup();
      
      // Run commission calculation
      const commissionService = new EventCommissionCalculationService();
      const result = await commissionService.calculateEventCommissions(testData.eventReportId);

      // Validate results
      const validation = this.validateResults(result, scenario.expectedResults);
      
      this.testResults.push({
        scenario: scenario.name,
        passed: validation.passed,
        details: validation.details,
        actual: result.summary,
        expected: scenario.expectedResults
      });

      if (validation.passed) {
        console.log(`   ✅ PASSED`);
      } else {
        console.log(`   ❌ FAILED`);
        console.log(`   Expected: ${JSON.stringify(scenario.expectedResults)}`);
        console.log(`   Actual: ${JSON.stringify(result.summary)}`);
        console.log(`   Issues: ${validation.details.join(', ')}`);
      }

      // Cleanup test data
      await this.cleanupTestData(testData);

    } catch (error) {
      console.log(`   💥 ERROR: ${error.message}`);
      this.testResults.push({
        scenario: scenario.name,
        passed: false,
        error: error.message
      });
    }

    console.log('');
  }

  private validateResults(actual: any, expected: any): { passed: boolean; details: string[] } {
    const details: string[] = [];
    let passed = true;

    if (expected.papCommissions !== undefined) {
      if (Math.abs(actual.totalPapCommissions - expected.papCommissions) > 0.01) {
        passed = false;
        details.push(`PAP commissions: expected ${expected.papCommissions}, got ${actual.totalPapCommissions}`);
      }
    }

    if (expected.cookCommissions !== undefined) {
      if (Math.abs(actual.totalCookCommissions - expected.cookCommissions) > 0.01) {
        passed = false;
        details.push(`Cook commissions: expected ${expected.cookCommissions}, got ${actual.totalCookCommissions}`);
      }
    }

    if (expected.supervisorCommissions !== undefined) {
      if (Math.abs(actual.totalSupervisorCommissions - expected.supervisorCommissions) > 0.01) {
        passed = false;
        details.push(`Supervisor commissions: expected ${expected.supervisorCommissions}, got ${actual.totalSupervisorCommissions}`);
      }
    }

    if (expected.totalAmount !== undefined) {
      if (Math.abs(actual.totalAmount - expected.totalAmount) > 0.01) {
        passed = false;
        details.push(`Total amount: expected ${expected.totalAmount}, got ${actual.totalAmount}`);
      }
    }

    return { passed, details };
  }

  private async setupBasicScenario() {
    // Create test users
    const papUser = await this.createTestUser('PAP Agent', '<EMAIL>');
    const cookUser = await this.createTestUser('Cook Agent', '<EMAIL>');
    const supervisorUser = await this.createTestUser('Supervisor', '<EMAIL>');

    // Create test event
    const event = await this.createTestEvent('Basic Test Event');

    // Create test reservations
    const reservations = await this.createTestReservations(papUser._id, 2);

    // Create event report
    const eventReport = await this.createTestEventReport(event._id, {
      paps: [{ userId: papUser._id, timeRange: { startTime: new Date(), endTime: new Date(Date.now() + 4 * 60 * 60 * 1000) } }],
      cooks: [{ userId: cookUser._id, timeRange: { startTime: new Date(), endTime: new Date(Date.now() + 4 * 60 * 60 * 1000) } }],
      supervisors: [supervisorUser._id],
      linkedReservationIds: reservations.map(r => r._id)
    });

    return {
      eventReportId: eventReport._id.toString(),
      eventId: event._id,
      userIds: [papUser._id, cookUser._id, supervisorUser._id],
      reservationIds: reservations.map(r => r._id)
    };
  }

  private async setupTimeBasedScenario() {
    // Similar to basic but with reservations created at different times
    const papUser = await this.createTestUser('PAP Agent 2', '<EMAIL>');
    const cookUser = await this.createTestUser('Cook Agent 2', '<EMAIL>');
    const supervisorUser = await this.createTestUser('Supervisor 2', '<EMAIL>');

    const event = await this.createTestEvent('Time-Based Test Event');

    // Create one reservation within 3 weeks, one outside
    const recentReservation = await this.createTestReservation(papUser._id, new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)); // 10 days ago
    const oldReservation = await this.createTestReservation(papUser._id, new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)); // 30 days ago

    const eventReport = await this.createTestEventReport(event._id, {
      paps: [{ userId: papUser._id, timeRange: { startTime: new Date(), endTime: new Date(Date.now() + 4 * 60 * 60 * 1000) } }],
      cooks: [{ userId: cookUser._id, timeRange: { startTime: new Date(), endTime: new Date(Date.now() + 4 * 60 * 60 * 1000) } }],
      supervisors: [supervisorUser._id],
      linkedReservationIds: [recentReservation._id, oldReservation._id]
    });

    return {
      eventReportId: eventReport._id.toString(),
      eventId: event._id,
      userIds: [papUser._id, cookUser._id, supervisorUser._id],
      reservationIds: [recentReservation._id, oldReservation._id]
    };
  }

  // Additional setup methods would be implemented here...
  private async setupTimeProportionScenario() {
    // Implementation for time proportion testing
    return this.setupBasicScenario(); // Simplified for now
  }

  private async setupMultipleSupervisorsScenario() {
    // Implementation for multiple supervisors testing
    return this.setupBasicScenario(); // Simplified for now
  }

  private async setupNoReservationsScenario() {
    // Implementation for no reservations testing
    return this.setupBasicScenario(); // Simplified for now
  }

  private async setupZeroHoursCookScenario() {
    // Implementation for zero hours cook testing
    return this.setupBasicScenario(); // Simplified for now
  }

  // Helper methods for creating test data
  private async createTestUser(name: string, email: string) {
    return await User.create({
      name,
      email,
      password: 'test123',
      roles: []
    });
  }

  private async createTestEvent(name: string) {
    return await Event.create({
      name,
      location: 'Test Location',
      startDate: new Date(),
      endDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      status: 'scheduled'
    });
  }

  private async createTestReservations(partnerId: mongoose.Types.ObjectId, count: number) {
    const reservations = [];
    for (let i = 0; i < count; i++) {
      const reservation = await this.createTestReservation(partnerId);
      reservations.push(reservation);
    }
    return reservations;
  }

  private async createTestReservation(partnerId: mongoose.Types.ObjectId, createdAt?: Date) {
    return await Reservation.create({
      appointmentId: new mongoose.Types.ObjectId(),
      partnerId,
      type: 'branch',
      status: 'confirmed',
      customerInfo: {
        client1Name: 'Test Client',
        hasCompanion: false,
        city: 'Test City',
        postalCode: 'H1H1H1',
        phone: '5141234567',
        email: '<EMAIL>',
        isPostalCodeValid: true
      },
      preferences: {
        preferredLanguage: 'fr',
        allergies: '',
        foods: [],
        hasChildren: false,
        childrenAges: { age0to5: 0, age6to12: 0, age13to17: 0 },
        branchId: 'test-branch',
        visitDate: new Date().toISOString().split('T')[0],
        visitTime: '18:00'
      },
      createdAt: createdAt || new Date()
    });
  }

  private async createTestEventReport(eventId: mongoose.Types.ObjectId, data: any) {
    return await EventReport.create({
      eventId,
      eventStartTime: new Date(),
      eventEndTime: new Date(Date.now() + 4 * 60 * 60 * 1000),
      status: 'validated',
      ...data
    });
  }

  private async cleanupTestData(testData: any) {
    // Clean up test data
    await EventReport.findByIdAndDelete(testData.eventReportId);
    await Event.findByIdAndDelete(testData.eventId);
    await User.deleteMany({ _id: { $in: testData.userIds } });
    await Reservation.deleteMany({ _id: { $in: testData.reservationIds } });
    await Commission.deleteMany({ eventId: testData.eventId });
  }

  private printTestSummary() {
    console.log('📊 Test Summary');
    console.log('================');
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
      console.log('\n🎉 All tests passed!');
    } else {
      console.log('\n❌ Some tests failed. Review the details above.');
    }
  }
}

async function runCommissionTests() {
  try {
  await dbConnect();
    console.log('Connected to database');

    // Ensure commission configuration exists
    const config = await CommissionConfiguration.findOne({ isActive: true });
    if (!config) {
      console.log('Creating default commission configuration for testing...');
      await CommissionConfiguration.create({
        papCommissionAmount: 7,
        cookCommissionPerReservation: 2,
        supervisorCommissionPerReservation: 2,
        papSchedulingThresholdWeeks: 3,
        isActive: true,
        createdBy: new mongoose.Types.ObjectId()
      });
    }

    const tester = new CommissionCalculationTester();
    await tester.runAllTests();

  } catch (error) {
    console.error('❌ Test execution failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the tests
if (require.main === module) {
  runCommissionTests();
}

export { runCommissionTests, CommissionCalculationTester };
