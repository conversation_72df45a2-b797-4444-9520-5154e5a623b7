import Permission from '@/models/Permission';
import { RECONTACT_PERMISSIONS } from '@/types/recontact-permission-codes';
import dbConnect from '@/lib/db';
export async function seedRecontactPermissions() {
await dbConnect();
  
  const permission = {
    code: RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS,
    name: 'Access Recontact Reservations',
    description: 'View and manage recontact reservations list and details',
    category: 'recontact'
  };
  
  await Permission.findOneAndUpdate(
    { code: permission.code },
    permission,
    { upsert: true, new: true }
  );
  
  console.log('Recontact permission seeded successfully');
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedRecontactPermissions()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
