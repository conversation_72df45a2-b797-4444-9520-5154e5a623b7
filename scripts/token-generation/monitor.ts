#!/usr/bin/env ts-node

/**
 * Token pool monitoring and management script
 * 
 * Usage:
 * npm run token-monitor [action] [options]
 * 
 * Actions:
 * - stats: Show token pool statistics
 * - cleanup: Clean up old unused tokens
 * - health: Check token pool health
 * 
 * Examples:
 * npm run token-monitor stats
 * npm run token-monitor cleanup --days 30
 * npm run token-monitor health --threshold 10000
 */

import dbConnect from '@/lib/db';
import { getTokenPoolStats, cleanupOldTokens } from '../../app/invitations/utils/token-pregeneration';
import InvitationToken from '../../models/InvitationToken';
import InvitationGroup from '../../models/InvitationGroup';

interface MonitorConfig {
  action: string;
  groupId?: string;
  days?: number;
  threshold?: number;
  verbose?: boolean;
}

async function showStats(config: MonitorConfig) {
  console.log('📊 Token Pool Statistics\n');

  if (config.groupId) {
    // Show stats for specific group
    const group = await InvitationGroup.findById(config.groupId);
    if (!group) {
      throw new Error(`Group with ID ${config.groupId} not found`);
    }

    console.log(`📁 Group: ${group.name} (${group._id})`);
    const stats = await getTokenPoolStats(config.groupId);
    
    console.log(`   Total tokens: ${stats.total}`);
    console.log(`   Active tokens: ${stats.active}`);
    console.log(`   Unused tokens: ${stats.unused}`);
    console.log(`   Usage rate: ${stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : 0}%`);
  } else {
    // Show global stats and per-group breakdown
    const globalStats = await getTokenPoolStats(null);
    
    console.log('🌐 Global Statistics:');
    console.log(`   Total tokens: ${globalStats.total}`);
    console.log(`   Active tokens: ${globalStats.active}`);
    console.log(`   Unused tokens: ${globalStats.unused}`);
    console.log(`   Usage rate: ${globalStats.total > 0 ? ((globalStats.active / globalStats.total) * 100).toFixed(1) : 0}%`);

    // Get per-group breakdown
    const groups = await InvitationGroup.find({}, { _id: 1, name: 1 }).lean();
    
    if (groups.length > 0) {
      console.log('\n📁 Per-Group Breakdown:');
      
      for (const group of groups) {
        const groupStats = await getTokenPoolStats(group._id.toString());
        if (groupStats.total > 0) {
          console.log(`   ${group.name}: ${groupStats.unused}/${groupStats.total} unused (${((groupStats.unused / groupStats.total) * 100).toFixed(1)}%)`);
        }
      }
    }

    // Show recent activity
    if (config.verbose) {
      console.log('\n📈 Recent Activity (last 24 hours):');
      
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const recentTokens = await InvitationToken.countDocuments({
        createdAt: { $gte: yesterday }
      });
      
      const recentUsed = await InvitationToken.countDocuments({
        usedAt: { $gte: yesterday }
      });
      
      console.log(`   Tokens generated: ${recentTokens}`);
      console.log(`   Tokens consumed: ${recentUsed}`);
    }
  }
}

async function cleanupTokens(config: MonitorConfig) {
  const days = config.days || 30;
  console.log(`🧹 Cleaning up unused tokens older than ${days} days...\n`);

  if (config.groupId) {
    const group = await InvitationGroup.findById(config.groupId);
    if (!group) {
      throw new Error(`Group with ID ${config.groupId} not found`);
    }

    console.log(`📁 Target group: ${group.name} (${group._id})`);
    const cleanedCount = await cleanupOldTokens(days, config.groupId);
    console.log(`✅ Cleaned up ${cleanedCount} tokens from group`);
  } else {
    console.log('🌐 Cleaning up global tokens...');
    const globalCleaned = await cleanupOldTokens(days, null);
    console.log(`✅ Cleaned up ${globalCleaned} global tokens`);

    // Clean up per-group tokens
    const groups = await InvitationGroup.find({}, { _id: 1, name: 1 }).lean();
    let totalGroupCleaned = 0;

    for (const group of groups) {
      const groupCleaned = await cleanupOldTokens(days, group._id.toString());
      if (groupCleaned > 0) {
        console.log(`   ${group.name}: ${groupCleaned} tokens`);
        totalGroupCleaned += groupCleaned;
      }
    }

    console.log(`\n📊 Total cleaned: ${globalCleaned + totalGroupCleaned} tokens`);
  }
}

async function checkHealth(config: MonitorConfig) {
  const threshold = config.threshold || 10000;
  console.log(`🏥 Token Pool Health Check (threshold: ${threshold} unused tokens)\n`);

  let healthIssues = 0;

  if (config.groupId) {
    const group = await InvitationGroup.findById(config.groupId);
    if (!group) {
      throw new Error(`Group with ID ${config.groupId} not found`);
    }

    console.log(`📁 Checking group: ${group.name} (${group._id})`);
    const stats = await getTokenPoolStats(config.groupId);
    
    if (stats.unused < threshold) {
      console.log(`❌ LOW TOKENS: Only ${stats.unused} unused tokens (below threshold of ${threshold})`);
      healthIssues++;
    } else {
      console.log(`✅ HEALTHY: ${stats.unused} unused tokens available`);
    }
  } else {
    // Check global pool
    const globalStats = await getTokenPoolStats(null);
    console.log('🌐 Global Pool:');
    
    if (globalStats.unused < threshold) {
      console.log(`❌ LOW TOKENS: Only ${globalStats.unused} unused tokens (below threshold of ${threshold})`);
      healthIssues++;
    } else {
      console.log(`✅ HEALTHY: ${globalStats.unused} unused tokens available`);
    }

    // Check per-group pools
    const groups = await InvitationGroup.find({}, { _id: 1, name: 1 }).lean();
    
    if (groups.length > 0) {
      console.log('\n📁 Group Pools:');
      
      for (const group of groups) {
        const groupStats = await getTokenPoolStats(group._id.toString());
        
        if (groupStats.total > 0) {
          if (groupStats.unused < Math.min(threshold, 1000)) { // Lower threshold for groups
            console.log(`❌ ${group.name}: Only ${groupStats.unused} unused tokens`);
            healthIssues++;
          } else {
            console.log(`✅ ${group.name}: ${groupStats.unused} unused tokens`);
          }
        }
      }
    }
  }

  console.log(`\n📋 Health Summary: ${healthIssues === 0 ? '✅ All pools healthy' : `❌ ${healthIssues} issue(s) found`}`);
  
  if (healthIssues > 0) {
    console.log('\n💡 Recommendations:');
    console.log('   - Run overnight batch generation to replenish token pools');
    console.log('   - Consider increasing batch generation frequency');
    console.log('   - Monitor token consumption patterns');
  }

  return healthIssues === 0;
}

async function main() {
  try {
    // Parse command line arguments
    const args = process.argv.slice(2);
    const action = args[0] || 'stats';
    
    const config: MonitorConfig = {
      action,
      groupId: args.find(arg => arg.startsWith('--group='))?.split('=')[1],
      days: parseInt(args.find(arg => arg.startsWith('--days='))?.split('=')[1] || '30'),
      threshold: parseInt(args.find(arg => arg.startsWith('--threshold='))?.split('=')[1] || '10000'),
      verbose: args.includes('--verbose') || args.includes('-v')
    };

    console.log('🔍 Token Pool Monitor\n');

    // Connect to database
  await dbConnect();

    switch (action) {
      case 'stats':
        await showStats(config);
        break;
      
      case 'cleanup':
        await cleanupTokens(config);
        break;
      
      case 'health':
        const isHealthy = await checkHealth(config);
        process.exit(isHealthy ? 0 : 1);
        break;
      
      default:
        console.error(`❌ Unknown action: ${action}`);
        console.log('\nAvailable actions: stats, cleanup, health');
        process.exit(1);
    }

    process.exit(0);

  } catch (error: any) {
    console.error('❌ Error:', error.message);
    if (config?.verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
