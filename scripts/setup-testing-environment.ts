import bcrypt from 'bcryptjs';
import mongoose from 'mongoose';
import dbConnect from '../lib/db';
import User from '../models/User';
import Branch from '../models/Branch';
import Partner from '../models/Partner';
import EventType from '../models/EventType';
import { Event } from '../models/Event';
import { EventReport } from '../models/EventReport';
import Permission from '../models/Permission';
import Role from '../models/Role';

interface TestUser {
  name: string;
  email: string;
  password: string;
  roles: string[];
  branchId?: mongoose.Types.ObjectId;
}

interface TestBranch {
  name: string;
  code: string;
  address: string;
}

interface TestPartner {
  name: string;
  email: string;
  phone: string;
}

interface TestEventType {
  name: string;
  code: string;
  description: string;
}

class TestingEnvironmentSetup {
  private branches: any[] = [];
  private partners: any[] = [];
  private eventTypes: any[] = [];
  private users: any[] = [];
  private events: any[] = [];

  async setup() {
    try {
      console.log('🚀 Setting up testing environment...');
      
      await dbConnect();
      console.log('✅ Connected to database');

      // Clean existing test data
      await this.cleanTestData();
      console.log('✅ Cleaned existing test data');

      // Setup permissions and roles
      await this.setupPermissionsAndRoles();
      console.log('✅ Setup permissions and roles');

      // Create test branches
      await this.createTestBranches();
      console.log('✅ Created test branches');

      // Create test partners
      await this.createTestPartners();
      console.log('✅ Created test partners');

      // Create test event types
      await this.createTestEventTypes();
      console.log('✅ Created test event types');

      // Create test users
      await this.createTestUsers();
      console.log('✅ Created test users');

      // Create test events
      await this.createTestEvents();
      console.log('✅ Created test events');

      // Create test reservations
      await this.createTestReservations();
      console.log('✅ Created test reservations');

      console.log('\n🎉 Testing environment setup complete!');
      this.printTestCredentials();

    } catch (error) {
      console.error('❌ Error setting up testing environment:', error);
      throw error;
    }
  }

  private async cleanTestData() {
    // Clean test users
    await User.deleteMany({ email: { $regex: /test.*@example\.com/ } });
    
    // Clean test branches
    await Branch.deleteMany({ name: { $regex: /Test Branch/ } });
    
    // Clean test partners
    await Partner.deleteMany({ name: { $regex: /Test Partner/ } });
    
    // Clean test event types
    await EventType.deleteMany({ name: { $regex: /Test Event/ } });
    
    // Clean test events
    await Event.deleteMany({ name: { $regex: /Test Event/ } });
    
    // Clean test event reports
    await EventReport.deleteMany({});
  }

  private async setupPermissionsAndRoles() {
    // Run the existing seeding script logic
    const { execSync } = require('child_process');
    try {
      execSync('node scripts/seed-event-permissions-roles.js', { stdio: 'inherit' });
    } catch (error) {
      console.log('Note: Permission seeding script may have already run or failed - continuing...');
    }
  }

  private async createTestBranches() {
    const testBranches: TestBranch[] = [
      {
        name: 'Test Branch North',
        code: 'TBN',
        address: '123 North Test St, Test City, TC 12345'
      },
      {
        name: 'Test Branch South',
        code: 'TBS',
        address: '456 South Test Ave, Test City, TC 12346'
      }
    ];

    for (const branchData of testBranches) {
      const branch = await Branch.create(branchData);
      this.branches.push(branch);
    }
  }

  private async createTestPartners() {
    const testPartners: TestPartner[] = [
      {
        name: 'Test Partner Alpha',
        email: '<EMAIL>',
        phone: '555-0101'
      },
      {
        name: 'Test Partner Beta',
        email: '<EMAIL>',
        phone: '555-0102'
      },
      {
        name: 'Test Partner Gamma',
        email: '<EMAIL>',
        phone: '555-0103'
      }
    ];

    for (const partnerData of testPartners) {
      const partner = await Partner.create(partnerData);
      this.partners.push(partner);
    }
  }

  private async createTestEventTypes() {
    const testEventTypes: TestEventType[] = [
      {
        name: 'Test Event BBQ',
        code: 'TEST_BBQ',
        description: 'Test BBQ event type for testing'
      },
      {
        name: 'Test Event Catering',
        code: 'TEST_CATERING',
        description: 'Test catering event type for testing'
      }
    ];

    for (const eventTypeData of testEventTypes) {
      const eventType = await EventType.create(eventTypeData);
      this.eventTypes.push(eventType);
    }
  }

  private async createTestUsers() {
    // Get roles
    const supervisorRole = await Role.findOne({ name: 'Event Supervisor' });
    const cookRole = await Role.findOne({ name: 'Cook' });
    const adminRole = await Role.findOne({ name: 'Admin' });

    const testUsers: TestUser[] = [
      // Supervisors
      {
        name: 'Test Supervisor North',
        email: '<EMAIL>',
        password: 'test123',
        roles: supervisorRole ? [supervisorRole._id.toString()] : [],
        branchId: this.branches[0]._id
      },
      {
        name: 'Test Supervisor South',
        email: '<EMAIL>',
        password: 'test123',
        roles: supervisorRole ? [supervisorRole._id.toString()] : [],
        branchId: this.branches[1]._id
      },
      // Admins
      {
        name: 'Test Admin North',
        email: '<EMAIL>',
        password: 'test123',
        roles: adminRole ? [adminRole._id.toString()] : [],
        branchId: this.branches[0]._id
      },
      {
        name: 'Test Admin South',
        email: '<EMAIL>',
        password: 'test123',
        roles: adminRole ? [adminRole._id.toString()] : [],
        branchId: this.branches[1]._id
      },
      // PAPs
      {
        name: 'Test PAP Alpha',
        email: '<EMAIL>',
        password: 'test123',
        roles: []
      },
      {
        name: 'Test PAP Beta',
        email: '<EMAIL>',
        password: 'test123',
        roles: []
      },
      {
        name: 'Test PAP Gamma',
        email: '<EMAIL>',
        password: 'test123',
        roles: []
      },
      // Cooks
      {
        name: 'Test Cook Alpha',
        email: '<EMAIL>',
        password: 'test123',
        roles: cookRole ? [cookRole._id.toString()] : []
      },
      {
        name: 'Test Cook Beta',
        email: '<EMAIL>',
        password: 'test123',
        roles: cookRole ? [cookRole._id.toString()] : []
      }
    ];

    for (const userData of testUsers) {
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      const user = await User.create({
        ...userData,
        password: hashedPassword,
        isActive: true
      });
      this.users.push(user);
    }
  }

  private async createTestEvents() {
    const now = new Date();
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    const testEvents = [
      {
        name: 'Test Event Alpha - New',
        branchId: this.branches[0]._id,
        partnerId: this.partners[0]._id,
        eventTypeId: this.eventTypes[0]._id,
        startDate: tomorrow,
        endDate: new Date(tomorrow.getTime() + 8 * 60 * 60 * 1000),
        location: 'Test Location Alpha',
        clientGoal: 100,
        notes: 'Test event in new status',
        status: 'new',
        supervisors: [this.users[0]._id], // Test Supervisor North
        cooks: [
          {
            userId: this.users[7]._id, // Test Cook Alpha
            percentage: 60,
            timeRange: {
              start: tomorrow,
              end: new Date(tomorrow.getTime() + 8 * 60 * 60 * 1000)
            }
          },
          {
            userId: this.users[8]._id, // Test Cook Beta
            percentage: 40,
            timeRange: {
              start: tomorrow,
              end: new Date(tomorrow.getTime() + 8 * 60 * 60 * 1000)
            }
          }
        ],
        paps: [
          {
            userId: this.users[4]._id, // Test PAP Alpha
            timeRange: {
              start: tomorrow,
              end: new Date(tomorrow.getTime() + 4 * 60 * 60 * 1000)
            }
          }
        ]
      },
      {
        name: 'Test Event Beta - In Progress',
        branchId: this.branches[1]._id,
        partnerId: this.partners[1]._id,
        eventTypeId: this.eventTypes[1]._id,
        startDate: nextWeek,
        endDate: new Date(nextWeek.getTime() + 6 * 60 * 60 * 1000),
        location: 'Test Location Beta',
        clientGoal: 150,
        notes: 'Test event in progress',
        status: 'in_progress',
        supervisors: [this.users[1]._id], // Test Supervisor South
        cooks: [
          {
            userId: this.users[7]._id, // Test Cook Alpha
            percentage: 100,
            timeRange: {
              start: nextWeek,
              end: new Date(nextWeek.getTime() + 6 * 60 * 60 * 1000)
            }
          }
        ],
        paps: [
          {
            userId: this.users[5]._id, // Test PAP Beta
            timeRange: {
              start: nextWeek,
              end: new Date(nextWeek.getTime() + 3 * 60 * 60 * 1000)
            }
          },
          {
            userId: this.users[6]._id, // Test PAP Gamma
            timeRange: {
              start: new Date(nextWeek.getTime() + 3 * 60 * 60 * 1000),
              end: new Date(nextWeek.getTime() + 6 * 60 * 60 * 1000)
            }
          }
        ]
      }
    ];

    for (const eventData of testEvents) {
      const event = await Event.create(eventData);
      this.events.push(event);

      // Create corresponding EventReport
      const report = await EventReport.create({
        eventId: event._id,
        supervisorId: eventData.supervisors[0],
        status: 'draft',
        personnel: {
          supervisors: eventData.supervisors.map(id => ({ userId: id, verified: false })),
          cooks: eventData.cooks.map(cook => ({ 
            userId: cook.userId, 
            percentage: cook.percentage,
            timeRange: cook.timeRange,
            verified: false 
          })),
          paps: eventData.paps.map(pap => ({ 
            userId: pap.userId, 
            timeRange: pap.timeRange,
            verified: false 
          }))
        },
        linkedReservations: [],
        commissions: {
          calculated: false,
          rates: {
            supervisorRate: 2.00,
            papCommissionThreshold: 21,
            papCommissionRate: 7.00,
            cookBaseRate: 2.00
          },
          breakdown: []
        },
        notes: '',
        history: [{
          action: 'created',
          timestamp: new Date(),
          userId: eventData.supervisors[0],
          details: 'Report created automatically with event'
        }]
      });

      // Update event with reportId
      await Event.findByIdAndUpdate(event._id, { reportId: report._id });
    }
  }

  private async createTestReservations() {
    // This would create test reservations for linking tests
    // For now, we'll skip this as it requires the Reservation model
    console.log('Note: Test reservations creation skipped - implement if Reservation model is available');
  }

  private printTestCredentials() {
    console.log('\n📋 TEST CREDENTIALS:');
    console.log('===================');
    console.log('Supervisors:');
    console.log('  - <EMAIL> / test123 (North Branch)');
    console.log('  - <EMAIL> / test123 (South Branch)');
    console.log('\nAdmins:');
    console.log('  - <EMAIL> / test123 (North Branch)');
    console.log('  - <EMAIL> / test123 (South Branch)');
    console.log('\nPAPs:');
    console.log('  - <EMAIL> / test123');
    console.log('  - <EMAIL> / test123');
    console.log('  - <EMAIL> / test123');
    console.log('\nCooks:');
    console.log('  - <EMAIL> / test123');
    console.log('  - <EMAIL> / test123');
    console.log('\n🌐 Application URL: http://localhost:5000');
    console.log('\n✅ Ready for testing!');
  }
}

// Run the setup
const setup = new TestingEnvironmentSetup();
setup.setup()
  .then(() => {
    console.log('✅ Setup completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
