#!/usr/bin/env tsx

/**
 * Test script for Contact Request Sources API endpoints
 * This script tests the basic functionality of the API endpoints
 */

import ContactRequestSource from '../models/ContactRequestSource';
import dbConnect from '../lib/db';
async function testContactRequestSourcesAPI() {
  console.log('🧪 Testing Contact Request Sources API...\n');

  try {
    // Connect to database
  await dbConnect();
    console.log('✅ Database connection established');

    // Test 1: Verify model can be imported and used
    console.log('\n📋 Test 1: Model validation');
    
    const testSource = {
      tag: 'TEST01',
      source: 'facebook',
      influencer: 'AMQ',
      bitlyUrl: 'https://bit.ly/test01',
      hits: 0,
      webhookData: []
    };

    // Validate the model structure
    const sourceInstance = new ContactRequestSource(testSource);
    const validationError = sourceInstance.validateSync();
    
    if (validationError) {
      console.log('❌ Model validation failed:', validationError.message);
    } else {
      console.log('✅ Model validation passed');
    }

    // Test 2: Check if we can query the collection
    console.log('\n📋 Test 2: Database query test');
    
    try {
      const count = await ContactRequestSource.countDocuments();
      console.log(`✅ Successfully queried collection. Found ${count} existing sources.`);
    } catch (queryError) {
      console.log('❌ Database query failed:', queryError);
    }

    // Test 3: Test tag validation
    console.log('\n📋 Test 3: Tag validation tests');
    
    const validTags = ['ABC123', 'XYZ789', '123ABC'];
    const invalidTags = ['ABC12', 'ABC1234', 'ABC-12', 'abc123'];
    
    validTags.forEach(tag => {
      const isValid = /^[A-Za-z0-9]{6}$/.test(tag);
      console.log(`${isValid ? '✅' : '❌'} Tag "${tag}": ${isValid ? 'valid' : 'invalid'}`);
    });
    
    invalidTags.forEach(tag => {
      const isValid = /^[A-Za-z0-9]{6}$/.test(tag);
      console.log(`${!isValid ? '✅' : '❌'} Tag "${tag}": ${isValid ? 'valid' : 'invalid'} (should be invalid)`);
    });

    // Test 4: Test enum validation
    console.log('\n📋 Test 4: Enum validation tests');
    
    const validSources = ['facebook', 'instagram', 'tiktok', 'youtube'];
    const invalidSources = ['twitter', 'linkedin', 'snapchat'];
    
    validSources.forEach(source => {
      const testDoc = new ContactRequestSource({
        tag: 'TEST01',
        source: source,
        influencer: 'AMQ',
        bitlyUrl: 'https://bit.ly/test',
        hits: 0
      });
      const error = testDoc.validateSync();
      console.log(`${!error ? '✅' : '❌'} Source "${source}": ${!error ? 'valid' : 'invalid'}`);
    });

    const validInfluencers = ['AMQ', 'Dominique Paquet'];
    const invalidInfluencers = ['John Doe', 'Jane Smith'];
    
    validInfluencers.forEach(influencer => {
      const testDoc = new ContactRequestSource({
        tag: 'TEST01',
        source: 'facebook',
        influencer: influencer,
        bitlyUrl: 'https://bit.ly/test',
        hits: 0
      });
      const error = testDoc.validateSync();
      console.log(`${!error ? '✅' : '❌'} Influencer "${influencer}": ${!error ? 'valid' : 'invalid'}`);
    });

    // Test 5: Test aggregation queries (similar to stats endpoint)
    console.log('\n📋 Test 5: Aggregation query tests');
    
    try {
      const hitsSummary = await ContactRequestSource.aggregate([
        {
          $group: {
            _id: null,
            totalHits: { $sum: '$hits' },
            averageHits: { $avg: '$hits' }
          }
        }
      ]);
      
      console.log('✅ Hits aggregation query successful');
      console.log(`   Total hits: ${hitsSummary[0]?.totalHits || 0}`);
      console.log(`   Average hits: ${hitsSummary[0]?.averageHits || 0}`);
    } catch (aggError) {
      console.log('❌ Aggregation query failed:', aggError);
    }

    console.log('\n🎉 All tests completed!');
    console.log('\n📝 Summary:');
    console.log('   - Model validation: Working');
    console.log('   - Database queries: Working');
    console.log('   - Tag validation: Working');
    console.log('   - Enum validation: Working');
    console.log('   - Aggregation queries: Working');
    console.log('\n✅ Contact Request Sources API is ready for use!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    process.exit(1);
  }
}

// Run the test
testContactRequestSourcesAPI()
  .then(() => {
    console.log('\n🏁 Test script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test script failed:', error);
    process.exit(1);
  });