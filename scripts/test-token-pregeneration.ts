#!/usr/bin/env ts-node

/**
 * Test script for the invitation token pre-generation system
 *
 * This script tests all major functionality:
 * 1. Token batch generation
 * 2. Token consumption
 * 3. Fallback to on-demand generation
 * 4. Statistics and monitoring
 * 5. Cleanup functionality
 */

import dbConnect from '@/lib/db';
import {
  generateTokenBatch,
  consumePreGeneratedToken,
  getTokenPoolStats,
  cleanupOldTokens
} from '../app/invitations/utils/token-pregeneration';
import { generateUniqueToken } from '../app/invitations/utils/token-generator';
import InvitationToken from '../models/InvitationToken';
import Invitation from '../models/Invitation';
import mongoose from 'mongoose';

async function testTokenGeneration() {
  console.log('🧪 Testing token batch generation...');

  const testGroupId = new mongoose.Types.ObjectId().toString();
  // Note: groupId parameter is ignored during generation, tokens are created without groupId
  const result = await generateTokenBatch(100, null, 50);

  console.log(`   Generated: ${result.generated}/${result.total} tokens`);
  console.log(`   Status: ${result.status}`);
  console.log(`   Failed: ${result.failed}`);

  if (result.generated < 95) {
    throw new Error('Token generation success rate too low');
  }

  return testGroupId;
}

async function testTokenConsumption(testGroupId: string) {
  console.log('🧪 Testing token consumption...');

  const invitationId = new mongoose.Types.ObjectId().toString();
  const result = await consumePreGeneratedToken(testGroupId, invitationId);

  if (!result.success || !result.token) {
    throw new Error('Failed to consume or create token');
  }

  console.log(`   Got token: ${result.token}`);

  // Verify token is marked as active in InvitationToken collection
  const tokenRecord = await InvitationToken.findOne({
    token: result.token,
    active: true,
    invitationId: new mongoose.Types.ObjectId(invitationId)
  });

  if (!tokenRecord) {
    throw new Error('Token not properly marked as active in InvitationToken collection');
  }

  console.log('   ✅ Token properly stored and marked as active in InvitationToken collection');
  return result.token;
}

async function testFallbackGeneration() {
  console.log('🧪 Testing fallback to on-demand generation...');

  // First, consume all available pre-generated tokens to force fallback
  const consumedTokens: string[] = [];
  let availableTokens = await InvitationToken.countDocuments({ active: false });

  console.log(`   Found ${availableTokens} available pre-generated tokens`);

  // Consume all available tokens
  for (let i = 0; i < availableTokens; i++) {
    const tempId = new mongoose.Types.ObjectId().toString();
    const result = await consumePreGeneratedToken(null, tempId);
    if (result.success && result.token) {
      consumedTokens.push(result.token);
    }
  }

  console.log(`   Consumed ${consumedTokens.length} pre-generated tokens`);

  // Now try to get another token - this should trigger fallback generation
  const fallbackInvitationId = new mongoose.Types.ObjectId().toString();
  const fallbackResult = await consumePreGeneratedToken(null, fallbackInvitationId);

  if (!fallbackResult.success || !fallbackResult.token) {
    throw new Error('Fallback token generation failed');
  }

  console.log(`   ✅ Fallback token generated and stored: ${fallbackResult.token}`);

  // Verify the fallback token was created in InvitationToken collection
  const fallbackTokenRecord = await InvitationToken.findOne({
    token: fallbackResult.token,
    active: true,
    invitationId: new mongoose.Types.ObjectId(fallbackInvitationId)
  });

  if (!fallbackTokenRecord) {
    throw new Error('Fallback token not found in InvitationToken collection');
  }

  console.log('   ✅ Fallback token properly stored in InvitationToken collection');
  return fallbackResult.token;
}

async function testStatistics(testGroupId: string) {
  console.log('🧪 Testing statistics...');

  const stats = await getTokenPoolStats(testGroupId);

  console.log(`   Total tokens: ${stats.total}`);
  console.log(`   Active tokens: ${stats.active}`);
  console.log(`   Unused tokens: ${stats.unused}`);

  if (stats.total === 0) {
    throw new Error('No tokens found in test group');
  }

  if (stats.active === 0) {
    throw new Error('No active tokens found (should have at least one from consumption test)');
  }

  if (stats.unused === 0) {
    throw new Error('No unused tokens found');
  }

  console.log('   ✅ Statistics look correct');
}

async function testCleanup(testGroupId: string) {
  console.log('🧪 Testing cleanup...');

  // Create some old tokens for testing
  const oldDate = new Date();
  oldDate.setDate(oldDate.getDate() - 35); // 35 days old

  const oldTokens = await InvitationToken.insertMany([
    {
      token: '999-99999-99999',
      active: false,
      groupId: new mongoose.Types.ObjectId(testGroupId),
      createdAt: oldDate
    },
    {
      token: '888-88888-88888',
      active: false,
      groupId: new mongoose.Types.ObjectId(testGroupId),
      createdAt: oldDate
    }
  ]);

  console.log(`   Created ${oldTokens.length} old test tokens`);

  // Test cleanup
  const cleanedCount = await cleanupOldTokens(30, testGroupId);

  console.log(`   Cleaned up ${cleanedCount} tokens`);

  if (cleanedCount < 2) {
    throw new Error('Cleanup did not remove expected number of tokens');
  }

  console.log('   ✅ Cleanup working correctly');
}

async function testGlobalUniqueness() {
  console.log('🧪 Testing global uniqueness...');

  // Generate a token and add it to Invitation collection
  const testToken = '123-45678-90123';

  const testInvitation = new Invitation({
    partner_id: new mongoose.Types.ObjectId(),
    token: testToken,
    groupId: null
  });

  await testInvitation.save();

  // Try to create the same token in InvitationToken collection
  try {
    await InvitationToken.create({
      token: testToken,
      active: false,
      groupId: null
    });

    throw new Error('Duplicate token was allowed - uniqueness constraint failed');
  } catch (error: any) {
    if (error.code === 11000) {
      console.log('   ✅ Global uniqueness constraint working');
    } else {
      throw error;
    }
  }

  // Clean up test data
  await Invitation.deleteOne({ _id: testInvitation._id });
}

async function runTests() {
  try {
    console.log('🚀 Starting invitation token pre-generation system tests...\n');

    // Connect to database
  await dbConnect();
    console.log('✅ Connected to database\n');

    // Run tests
    const testGroupId = await testTokenGeneration();
    console.log('');

    const consumedToken = await testTokenConsumption(testGroupId);
    console.log('');

    await testFallbackGeneration();
    console.log('');

    await testStatistics(testGroupId);
    console.log('');

    await testCleanup(testGroupId);
    console.log('');

    await testGlobalUniqueness();
    console.log('');

    // Clean up test data
    console.log('🧹 Cleaning up test data...');
    // Clean up tokens that were assigned to the test group
    await InvitationToken.deleteMany({
      groupId: new mongoose.Types.ObjectId(testGroupId)
    });
    // Clean up any remaining unused tokens from the test
    await InvitationToken.deleteMany({
      groupId: null,
      active: false,
      createdAt: { $gte: new Date(Date.now() - 60000) } // Last minute
    });
    console.log('   ✅ Test data cleaned up');

    console.log('\n🎉 All tests passed! The invitation token pre-generation system is working correctly.');

  } catch (error: any) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

export { runTests };
