#!/usr/bin/env tsx

/**
 * Test script for Bitly webhook handler
 * This script tests the webhook endpoint with sample data
 */

import ContactRequestSource from '../models/ContactRequestSource';
import crypto from 'crypto';
import dbConnect from '../lib/db';
async function testBitlyWebhook() {
  try {
    console.log('🧪 Testing Bitly webhook handler...');
    
  await dbConnect();
    
    // Create a test source first
    const testSource = new ContactRequestSource({
      tag: 'TEST01',
      source: 'facebook',
      influencer: 'AMQ',
      bitlyUrl: 'https://bit.ly/test123',
      hits: 0,
      webhookData: []
    });
    
    await testSource.save();
    console.log('✅ Created test source:', testSource.tag);
    
    // Simulate webhook payload
    const webhookPayload = {
      event_type: 'click',
      bitlink: 'https://bit.ly/test123',
      timestamp: new Date().toISOString(),
      ip_address: '***********',
      user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      referrer: 'https://facebook.com'
    };
    
    const webhookBody = JSON.stringify(webhookPayload);
    
    // Test signature generation (if secret is configured)
    const webhookSecret = process.env.BITLY_WEBHOOK_SECRET;
    let signature = '';
    if (webhookSecret) {
      signature = 'sha256=' + crypto
        .createHmac('sha256', webhookSecret)
        .update(webhookBody)
        .digest('hex');
      console.log('🔐 Generated signature for testing');
    }
    
    // Test the webhook endpoint
    const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
    const response = await fetch(`${baseUrl}/api/webhooks/bitly`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(signature && { 'x-bitly-signature': signature })
      },
      body: webhookBody
    });
    
    const responseData = await response.json();
    console.log('📡 Webhook response:', response.status, responseData);
    
    // Verify the source was updated
    const updatedSource = await ContactRequestSource.findOne({ tag: 'TEST01' });
    if (updatedSource) {
      console.log('✅ Source updated successfully:');
      console.log('  - Hits:', updatedSource.hits);
      console.log('  - Webhook events:', updatedSource.webhookData.length);
      console.log('  - Latest event:', updatedSource.webhookData[updatedSource.webhookData.length - 1]);
    }
    
    // Clean up test data
    await ContactRequestSource.deleteOne({ tag: 'TEST01' });
    console.log('🧹 Cleaned up test data');
    
    console.log('✅ Bitly webhook test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Clean up on error
    try {
      await ContactRequestSource.deleteOne({ tag: 'TEST01' });
    } catch (cleanupError) {
      console.error('Failed to clean up test data:', cleanupError);
    }
    
    process.exit(1);
  }
}

// Run the test
testBitlyWebhook().then(() => {
  console.log('Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('Test failed:', error);
  process.exit(1);
});