import { generateInvitationToken, generateUniqueToken, generateUniqueTokenBatch } from '../app/invitations/utils/token-generator';
import dbConnect from '../lib/db';
/**
 * Test script to verify the new token generation system
 */
async function testTokenGeneration() {
  try {
    console.log('🧪 Testing new token generation system...\n');
    
  await dbConnect();
    
    // Test 1: Basic token format
    console.log('Test 1: Basic token format validation');
    const basicToken = generateInvitationToken();
    const tokenRegex = /^\d{3}-\d{5}-\d{5}$/;
    
    console.log(`Generated token: ${basicToken}`);
    console.log(`Format valid: ${tokenRegex.test(basicToken) ? '✅' : '❌'}`);
    
    if (!tokenRegex.test(basicToken)) {
      throw new Error('Token format validation failed');
    }
    
    // Test 2: Multiple token generation (uniqueness within batch)
    console.log('\nTest 2: Batch uniqueness validation');
    const batchSize = 1000;
    const tokenBatch = await generateUniqueTokenBatch(batchSize, null, 'test-partner');
    const uniqueTokens = new Set(tokenBatch);
    
    console.log(`Generated ${tokenBatch.length} tokens`);
    console.log(`Unique tokens: ${uniqueTokens.size}`);
    console.log(`All unique: ${uniqueTokens.size === tokenBatch.length ? '✅' : '❌'}`);
    
    if (uniqueTokens.size !== tokenBatch.length) {
      throw new Error('Batch uniqueness validation failed');
    }
    
    // Test 3: Format validation for all tokens in batch
    console.log('\nTest 3: Batch format validation');
    const invalidTokens = tokenBatch.filter(token => !tokenRegex.test(token));
    console.log(`Invalid format tokens: ${invalidTokens.length}`);
    console.log(`All formats valid: ${invalidTokens.length === 0 ? '✅' : '❌'}`);
    
    if (invalidTokens.length > 0) {
      console.log('Invalid tokens:', invalidTokens.slice(0, 5));
      throw new Error('Batch format validation failed');
    }
    
    // Test 4: Performance test for large batch
    console.log('\nTest 4: Performance test (10,000 tokens)');
    const startTime = Date.now();
    const largeBatch = await generateUniqueTokenBatch(10000, null, 'test-partner-large');
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`Generated ${largeBatch.length} tokens in ${duration}ms`);
    console.log(`Average: ${(duration / largeBatch.length).toFixed(2)}ms per token`);
    console.log(`Performance acceptable: ${duration < 30000 ? '✅' : '❌'} (< 30s)`);
    
    // Test 5: Group-based uniqueness
    console.log('\nTest 5: Group-based token generation');
    const group1Tokens = await generateUniqueTokenBatch(100, 'group1', 'test-partner');
    const group2Tokens = await generateUniqueTokenBatch(100, 'group2', 'test-partner');
    
    const group1Set = new Set(group1Tokens);
    const group2Set = new Set(group2Tokens);
    const intersection = new Set([...group1Set].filter(x => group2Set.has(x)));
    
    console.log(`Group 1 tokens: ${group1Tokens.length} (unique: ${group1Set.size})`);
    console.log(`Group 2 tokens: ${group2Tokens.length} (unique: ${group2Set.size})`);
    console.log(`Overlapping tokens: ${intersection.size}`);
    console.log(`Groups can have overlapping tokens: ${intersection.size >= 0 ? '✅' : '❌'}`);
    
    // Test 6: Single token generation
    console.log('\nTest 6: Single token generation');
    const singleToken = await generateUniqueToken('test-group', 'test-partner');
    console.log(`Single token: ${singleToken}`);
    console.log(`Format valid: ${tokenRegex.test(singleToken) ? '✅' : '❌'}`);
    
    // Test 7: Edge cases
    console.log('\nTest 7: Edge cases');
    
    // Empty batch
    const emptyBatch = await generateUniqueTokenBatch(0, null, 'test-partner');
    console.log(`Empty batch (0 tokens): ${emptyBatch.length === 0 ? '✅' : '❌'}`);
    
    // Single token batch
    const singleBatch = await generateUniqueTokenBatch(1, null, 'test-partner');
    console.log(`Single token batch: ${singleBatch.length === 1 && tokenRegex.test(singleBatch[0]) ? '✅' : '❌'}`);
    
    // Test 8: Token distribution analysis
    console.log('\nTest 8: Token distribution analysis');
    const analysisTokens = Array(1000).fill(0).map(() => generateInvitationToken());
    
    // Check first digit distribution
    const firstDigits = analysisTokens.map(token => token[0]);
    const digitCounts = Array(10).fill(0);
    firstDigits.forEach(digit => digitCounts[parseInt(digit)]++);
    
    const minCount = Math.min(...digitCounts);
    const maxCount = Math.max(...digitCounts);
    const distribution = maxCount - minCount;
    
    console.log(`First digit distribution range: ${distribution} (${minCount}-${maxCount})`);
    console.log(`Distribution reasonable: ${distribution < 200 ? '✅' : '❌'} (< 200)`);
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log('- Token format: XXX-XXXXX-XXXXX (15 digits with dashes)');
    console.log('- Uniqueness: Ensured within groups');
    console.log('- Performance: Optimized for large batches');
    console.log('- Distribution: Statistically random');
    
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the tests
testTokenGeneration();
