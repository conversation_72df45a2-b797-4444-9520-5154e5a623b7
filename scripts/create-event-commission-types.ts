import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// Event commission type codes
const EVENT_COMMISSION_TYPES = {
  EVENT_PAP_COMMISSION: 'EVENT_PAP_COMMISSION',
  EVENT_COOK_COMMISSION: 'EVENT_COOK_COMMISSION',
  EVENT_SUPERVISOR_COMMISSION: 'EVENT_SUPERVISOR_COMMISSION'
};

async function createEventCommissionTypes() {
  try {
    await dbConnect();
    console.log('Connected to database');

    // Set a reasonable timeout for database operations
    if (mongoose.connection.db) {
      try {
        await mongoose.connection.db.admin().command({ setParameter: 1, maxTimeMS: 10000 });
      } catch (error) {
        console.log('Note: Could not set database timeout parameters');
      }
    }

    const db = mongoose.connection.db;
    if (!db) {
      throw new Error('Database connection not established');
    }

    // Find the 'new' status trigger using direct collection query
    console.log('Looking for "new" status...');
    const statusTrigger = await db.collection('reservationstatuses').findOne(
      { code: 'new' },
      { maxTimeMS: 5000 }
    );

    if (!statusTrigger) {
      throw new Error('Status with code "new" not found. Please ensure it exists in the database.');
    }

    console.log(`Using status trigger: ${statusTrigger.name} (${statusTrigger._id})`);

    // Create EVENT_PAP_COMMISSION type
    const existingPapCommission = await db.collection('commissiontypes').findOne(
      { code: EVENT_COMMISSION_TYPES.EVENT_PAP_COMMISSION },
      { maxTimeMS: 5000 }
    );

    if (!existingPapCommission) {
      const papCommissionType = {
        name: 'Event PAP Commission',
        code: EVENT_COMMISSION_TYPES.EVENT_PAP_COMMISSION,
        statusTrigger: statusTrigger._id,
        recipient: {
          partner: true,
          branchPAP: false,
          assignedUser: false
        },
        amount: 7, // Default amount, will be overridden by configuration
        isAlwaysActive: true,
        timeRestricted: false,
        dayRestricted: false,
        userRestricted: false,
        branchRestricted: false,
        isAutoApproved: false, // Event commissions require manual approval
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.collection('commissiontypes').insertOne(papCommissionType);
      console.log('✅ Created EVENT_PAP_COMMISSION commission type');
    } else {
      console.log('ℹ️  EVENT_PAP_COMMISSION commission type already exists');
    }

    // Create EVENT_COOK_COMMISSION type
    const existingCookCommission = await db.collection('commissiontypes').findOne(
      { code: EVENT_COMMISSION_TYPES.EVENT_COOK_COMMISSION },
      { maxTimeMS: 5000 }
    );

    if (!existingCookCommission) {
      const cookCommissionType = {
        name: 'Event Cook Commission',
        code: EVENT_COMMISSION_TYPES.EVENT_COOK_COMMISSION,
        statusTrigger: statusTrigger._id,
        recipient: {
          partner: false,
          branchPAP: false,
          assignedUser: true // Cook commissions go to assigned users (cooks)
        },
        amount: 2, // Default amount per reservation, will be calculated based on configuration
        isAlwaysActive: true,
        timeRestricted: false,
        dayRestricted: false,
        userRestricted: false,
        branchRestricted: false,
        isAutoApproved: false, // Event commissions require manual approval
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.collection('commissiontypes').insertOne(cookCommissionType);
      console.log('✅ Created EVENT_COOK_COMMISSION commission type');
    } else {
      console.log('ℹ️  EVENT_COOK_COMMISSION commission type already exists');
    }

    // Create EVENT_SUPERVISOR_COMMISSION type
    const existingSupervisorCommission = await db.collection('commissiontypes').findOne(
      { code: EVENT_COMMISSION_TYPES.EVENT_SUPERVISOR_COMMISSION },
      { maxTimeMS: 5000 }
    );

    if (!existingSupervisorCommission) {
      const supervisorCommissionType = {
        name: 'Event Supervisor Commission',
        code: EVENT_COMMISSION_TYPES.EVENT_SUPERVISOR_COMMISSION,
        statusTrigger: statusTrigger._id,
        recipient: {
          partner: false,
          branchPAP: false,
          assignedUser: true // Supervisor commissions go to assigned users (supervisors)
        },
        amount: 2, // Default amount per reservation, will be calculated based on configuration
        isAlwaysActive: true,
        timeRestricted: false,
        dayRestricted: false,
        userRestricted: false,
        branchRestricted: false,
        isAutoApproved: false, // Event commissions require manual approval
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.collection('commissiontypes').insertOne(supervisorCommissionType);
      console.log('✅ Created EVENT_SUPERVISOR_COMMISSION commission type');
    } else {
      console.log('ℹ️  EVENT_SUPERVISOR_COMMISSION commission type already exists');
    }

    // Create default commission configuration if it doesn't exist
    const existingConfig = await db.collection('commissionconfigurations').findOne(
      { isActive: true },
      { maxTimeMS: 5000 }
    );

    if (!existingConfig) {
      // Find a branchAdmin user to create the configuration
      // First, find any branch to get its responsible users (branchAdmins)
      const anyBranch = await db.collection('branches').findOne(
        { responsible: { $exists: true, $ne: [] } },
        { maxTimeMS: 5000 }
      );

      let branchAdminUser = null;
      if (anyBranch && anyBranch.responsible && anyBranch.responsible.length > 0) {
        // Get the first responsible user (branchAdmin) from this branch
        branchAdminUser = await db.collection('users').findOne(
          { _id: anyBranch.responsible[0] },
          { maxTimeMS: 5000 }
        );
      }

      if (!branchAdminUser) {
        console.warn('⚠️  No branchAdmin user found. Skipping default commission configuration creation.');
        console.log('Please create a commission configuration manually through the branchAdmin interface.');
        console.log('Note: Commission configuration should be created by a branchAdmin of the event\'s branch.');
      } else {
        const defaultConfig = {
          papCommissionAmount: 7,
          cookCommissionPerReservation: 2,
          supervisorCommissionPerReservation: 2,
          papSchedulingThresholdWeeks: 3,
          isActive: true,
          createdBy: branchAdminUser._id,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        await db.collection('commissionconfigurations').insertOne(defaultConfig);
        console.log(`✅ Created default commission configuration (created by branchAdmin: ${branchAdminUser.name})`);
      }
    } else {
      console.log('ℹ️  Commission configuration already exists');
    }

    console.log('\n🎉 Event commission types setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Verify commission types in the admin interface');
    console.log('2. Configure commission rates if needed');
    console.log('3. Test commission calculations with sample event reports');

  } catch (error) {
    console.error('❌ Error creating event commission types:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  createEventCommissionTypes();
}

export { createEventCommissionTypes, EVENT_COMMISSION_TYPES };
