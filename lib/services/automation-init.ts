import { CronScheduler } from './cron-scheduler';

/**
 * Initialize automation services when the server starts
 * This should be called during application startup
 */
export async function initializeAutomation() {


  console.log('Initializing automation services...');

  // Check if automation should be enabled
  const shouldEnableAutomation =
    process.env.NODE_ENV === 'production' ||
    process.env.ENABLE_AUTOMATION === 'true';

  if (shouldEnableAutomation) {
    console.log('Automation is enabled for this environment');

    try {
      await CronScheduler.start(); // This now includes branch stats automation
      console.log('✅ All automation services initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize automation services:', error);
    }
  } else {
    console.log('Automation is disabled for this environment');
    console.log('To enable automation, set ENABLE_AUTOMATION=true in your environment variables');
  }
}

/**
 * Gracefully shutdown automation services
 * This should be called when the server is shutting down
 */
export function shutdownAutomation() {
  try {
    CronScheduler.stopInvoiceAutomation();
    CronScheduler.stopDailyStatsEmailAutomation();
  } catch (error) {
    console.error('❌ Error during automation shutdown:', error);
  }
}

/**
 * Get the current health status of automation services
 */
export function getAutomationHealth() {
  try {
    const jobStatus = CronScheduler.getJobStatus();
    const nextRunTimes = CronScheduler.getNextRunTimes();

    // Get stats for key jobs
    const dailyStatsEmailStats = CronScheduler.getCronJobStats('daily-stats-email');
    const branchStatsEmailStats = CronScheduler.getCronJobStats('branch-stats-email');

    return {
      status: 'healthy',
      jobs: jobStatus,
      nextRunTimes,
      statistics: {
        dailyStatsEmail: dailyStatsEmailStats,
        branchStatsEmail: branchStatsEmailStats
      },
      environment: process.env.NODE_ENV,
      automationEnabled: process.env.NODE_ENV === 'production' || process.env.ENABLE_AUTOMATION === 'true',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}


