import Reservation from '@/models/Reservation';
import { Types } from 'mongoose';
import { fromZonedTime } from 'date-fns-tz';
import { getSalesStatusCodes, getPresenceStatusCodes } from '@/lib/utils/reservation-status-utils';
import dbConnect from '@/lib/db';

export interface ReservationStatsFilters {
  branches?: string[];
  dateFrom?: string;
  dateTo?: string;
  agentOrSeller?: 'agent' | 'seller';
  userId?: string;
  timezone?: string; // Client timezone (e.g., 'America/Toronto', 'Europe/Paris')
}

export interface BranchSalesData {
  branchId: string;
  branchName: string;
  count: number;
  amount: number;
}

export interface BranchReservationData {
  branchId: string;
  branchName: string;
  count: number;
}

export interface SellerPerformanceData {
  sellerId: string;
  sellerName: string;
  reservationsCount: number;
  salesCount: number;
  closingRate: number; // salesCount / reservationsCount
  // Comparison fields (optional)
  reservationsCountChange?: number;
  salesCountChange?: number;
  closingRateChange?: number;
}

export interface PapPerformanceData {
  papId: string;
  papName: string;
  reservationsCreatedCount: number;
  presenceReservationsCount: number;
  salesCount: number;
  presenceRate: number; // presenceReservationsCount / reservationsCreatedCount
  closingRate: number; // salesCount / reservationsCreatedCount
  // Comparison fields (optional)
  reservationsCreatedCountChange?: number;
  presenceReservationsCountChange?: number;
  salesCountChange?: number;
  presenceRateChange?: number;
  closingRateChange?: number;
}

export interface FunnelData {
  totalReservations: number;
  confirmedReservations: number;
  salesReservations: number;
  conversionRates: {
    reservationToConfirmed: number; // confirmedReservations / totalReservations
    confirmedToSales: number; // salesReservations / confirmedReservations
    reservationToSales: number; // salesReservations / totalReservations
  };
}

export interface MonthlyData {
  month: string; // YYYY-MM format
  count: number;
  amount?: number; // For sales data
}

export interface ReservationStats {
  totalCreated: number;
  totalSales: number;
  totalSalesAmount: number;
  presenceRate: number;
  presenceCount: number; // Keep for calculation purposes
  funnelData: FunnelData;
  salesByBranch: BranchSalesData[];
  reservationsCreatedByBranch: BranchReservationData[];
  sellerPerformance: SellerPerformanceData[];
  papPerformance: PapPerformanceData[];
  salesByMonth: MonthlyData[];
  reservationsCreatedByMonth: MonthlyData[];
}

export interface ComparisonResult {
  stats: ReservationStats;
  compareStats?: ReservationStats;
  comparisons?: {
    totalCreated: number;
    totalSales: number;
    totalSalesAmount: number;
    presenceRate: number;
    funnelData?: {
      totalReservations: number;
      confirmedReservations: number;
      salesReservations: number;
    };
    sellerPerformance?: SellerPerformanceData[];
    papPerformance?: PapPerformanceData[];
  };
  comparisonContext?: {
    filterDays: number;
    compareDays: number;
    filterBranches: number;
    compareBranches: number;
    adjustmentType: 'time' | 'branches' | 'both' | 'none';
    tooltip: string;
  };
}

export class ReservationStatsService {
  private static instance: ReservationStatsService;

  public static getInstance(): ReservationStatsService {
    if (!ReservationStatsService.instance) {
      ReservationStatsService.instance = new ReservationStatsService();
    }
    return ReservationStatsService.instance;
  }

  /**
   * Calculate reservation statistics based on filters
   */
  async calculateStats(filters: ReservationStatsFilters): Promise<ReservationStats> {
  await dbConnect();

    // Build match conditions for createdAt (used for total created)
    const createdAtMatchConditions: any = {
      isDeleted: { $ne: true }
    };

    // Build match conditions for visitDate (used for presence, sales, and branch stats)
    const visitDateMatchConditions: any = {
      isDeleted: { $ne: true }
    };

    // Branch filter - apply to all conditions
    if (filters.branches && filters.branches.length > 0) {
      createdAtMatchConditions['preferences.branchId'] = { $in: filters.branches };
      visitDateMatchConditions['preferences.branchId'] = { $in: filters.branches };
    }

    // Date range filter - apply to createdAt and visitDate conditions
    if (filters.dateFrom || filters.dateTo) {


      // For total created (use createdAt)
      const createdAtDateFilter: any = {};
      if (filters.dateFrom) {
        // Convert client date to UTC start of day
        createdAtDateFilter.$gte = this.convertClientDateToUTC(filters.dateFrom, filters.timezone, 'start');
      }
      if (filters.dateTo) {
        // Convert client date to UTC end of day
        createdAtDateFilter.$lte = this.convertClientDateToUTC(filters.dateTo, filters.timezone, 'end');
      }
      createdAtMatchConditions.createdAt = createdAtDateFilter;


      // For presence and sales (use visitDate with timezone-converted date range)
      // Since visitDate is stored as ISO strings, convert Date objects to strings for comparison
      if (filters.dateFrom || filters.dateTo) {
        const visitDateFilter: any = {};

        if (filters.dateFrom) {
          // Convert to Date object then to ISO string for string comparison
          const startDate = this.convertClientDateToUTC(filters.dateFrom, filters.timezone, 'start');
          visitDateFilter.$gte = startDate.toISOString();
        }

        if (filters.dateTo) {
          // Convert to Date object then to ISO string for string comparison
          const endDate = this.convertClientDateToUTC(filters.dateTo, filters.timezone, 'end');
          visitDateFilter.$lte = endDate.toISOString();
        }

        visitDateMatchConditions['preferences.visitDate'] = visitDateFilter;
      }

    }

    // Agent/Seller filter - apply to all conditions
    if (filters.agentOrSeller && filters.userId) {
      if (filters.agentOrSeller === 'agent') {
        createdAtMatchConditions.partnerId = new Types.ObjectId(filters.userId);
        visitDateMatchConditions.partnerId = new Types.ObjectId(filters.userId);
      } else if (filters.agentOrSeller === 'seller') {
        createdAtMatchConditions.assigned_user_id = new Types.ObjectId(filters.userId);
        visitDateMatchConditions.assigned_user_id = new Types.ObjectId(filters.userId);
      }
    }

    // Calculate all stats in parallel

    const [
      totalCreated,
      salesData,
      presenceCount,
      totalVisitDateReservations,
      funnelData,
      salesByBranch,
      reservationsCreatedByBranch,
      sellerPerformance,
      papPerformance,
      salesByMonth,
      reservationsCreatedByMonth
    ] = await Promise.all([
      this.getTotalCreated(createdAtMatchConditions),
      this.getSalesData(visitDateMatchConditions), // Reverted to use visitDate for sales cards
      this.getPresenceCount(visitDateMatchConditions),
      this.getTotalVisitDateReservations(visitDateMatchConditions),
      this.getFunnelData(visitDateMatchConditions), // Keep using visitDate for funnel
      this.getSalesByBranch(visitDateMatchConditions), // Keep using visitDate for branch stats
      this.getReservationsCreatedByBranch(createdAtMatchConditions), // This uses createdAt
      this.getSellerPerformance(visitDateMatchConditions), // Keep using visitDate
      this.getPapPerformance(createdAtMatchConditions, visitDateMatchConditions), // Keep using both createdAt and visitDate
      this.getSalesByMonth(filters), // Reverted to use visitDate
      this.getReservationsCreatedByMonth(filters) // Uses createdAt
    ]);



    // Calculate derived metrics - presence rate based on reservations that took place
    const presenceRate = totalVisitDateReservations > 0 ? presenceCount / totalVisitDateReservations : 0;

    return {
      totalCreated,
      totalSales: salesData.count,
      totalSalesAmount: salesData.amount,
      presenceRate: Math.round(presenceRate * 10000) / 10000, // 4 decimal places for percentage
      presenceCount,
      funnelData,
      salesByBranch,
      reservationsCreatedByBranch,
      sellerPerformance,
      papPerformance,
      salesByMonth,
      reservationsCreatedByMonth
    };
  }

  /**
   * Calculate stats with intelligent comparison
   */
  async calculateStatsWithComparison(
    filters: ReservationStatsFilters,
    compareFilters: ReservationStatsFilters
  ): Promise<ComparisonResult> {
    const [stats, compareStats] = await Promise.all([
      this.calculateStats(filters),
      this.calculateStats(compareFilters)
    ]);

    // Calculate adjustment factors for intelligent comparison
    const filterDays = this.calculateDaysDifference(filters.dateFrom, filters.dateTo);
    const compareDays = this.calculateDaysDifference(compareFilters.dateFrom, compareFilters.dateTo);
    const filterBranches = filters.branches?.length || 0;
    const compareBranches = compareFilters.branches?.length || 0;

    // Determine adjustment type and create tooltip
    let adjustmentType: 'time' | 'branches' | 'both' | 'none' = 'none';
    let tooltip = '';

    if (filterDays !== compareDays && (filterBranches !== compareBranches && filterBranches > 0 && compareBranches > 0)) {
      adjustmentType = 'both';
      tooltip = `Comparing ${filterDays} days vs ${compareDays} days, and ${filterBranches} branches vs ${compareBranches} branches. Compare data normalized to ${filterDays} days × ${filterBranches} branches.`;
    } else if (filterDays !== compareDays) {
      adjustmentType = 'time';
      tooltip = `Comparing ${filterDays} days vs ${compareDays} days. Compare data normalized to ${filterDays}-day period (daily average × ${filterDays}).`;
    } else if (filterBranches !== compareBranches && filterBranches > 0 && compareBranches > 0) {
      adjustmentType = 'branches';
      tooltip = `Comparing ${filterBranches} branches vs ${compareBranches} branches. Compare data normalized to ${filterBranches} branches.`;
    } else {
      tooltip = 'Direct comparison - same time period and branch scope.';
    }

    // Calculate adjusted compare stats for meaningful comparison
    const adjustedCompareStats = this.adjustCompareStats(compareStats, filterDays, compareDays, filterBranches, compareBranches);

    const comparisons = {
      totalCreated: this.calculatePercentageChange(stats.totalCreated, adjustedCompareStats.totalCreated),
      totalSales: this.calculatePercentageChange(stats.totalSales, adjustedCompareStats.totalSales),
      totalSalesAmount: this.calculatePercentageChange(stats.totalSalesAmount, adjustedCompareStats.totalSalesAmount),
      presenceRate: this.calculatePercentageChange(stats.presenceRate, adjustedCompareStats.presenceRate),
      funnelData: {
        totalReservations: this.calculatePercentageChange(stats.funnelData.totalReservations, adjustedCompareStats.funnelData.totalReservations),
        confirmedReservations: this.calculatePercentageChange(stats.funnelData.confirmedReservations, adjustedCompareStats.funnelData.confirmedReservations),
        salesReservations: this.calculatePercentageChange(stats.funnelData.salesReservations, adjustedCompareStats.funnelData.salesReservations)
      },
      sellerPerformance: this.calculateSellerPerformanceComparisons(stats.sellerPerformance, compareStats.sellerPerformance),
      papPerformance: this.calculatePapPerformanceComparisons(stats.papPerformance, compareStats.papPerformance)
    };

    return {
      stats,
      compareStats,
      comparisons,
      comparisonContext: {
        filterDays,
        compareDays,
        filterBranches,
        compareBranches,
        adjustmentType,
        tooltip
      }
    };
  }

  private async getTotalCreated(matchConditions: any): Promise<number> {
    return await Reservation.countDocuments(matchConditions);
  }

  private async getTotalVisitDateReservations(matchConditions: any): Promise<number> {
    return await Reservation.countDocuments(matchConditions);
  }



  private async getSalesData(matchConditions: any): Promise<{ count: number; amount: number }> {
    // This method uses visitDate-based match conditions for sales count and amount cards
    const salesStatusCodes = await getSalesStatusCodes();

    const pipeline = [
      { $match: matchConditions },
      {
        $match: {
          status: { $in: salesStatusCodes }
        }
      },
      {
        $group: {
          _id: null,
          count: { $sum: 1 },
          amount: { $sum: { $ifNull: ['$sellingAmount', 0] } }
        }
      }
    ];

    const result = await Reservation.aggregate(pipeline);
    const salesData = result.length > 0 ? { count: result[0].count, amount: result[0].amount } : { count: 0, amount: 0 };
    return salesData;
  }

  private async getPresenceCount(matchConditions: any): Promise<number> {
    const presenceStatusCodes = await getPresenceStatusCodes();
    return await Reservation.countDocuments({
      ...matchConditions,
      status: { $in: presenceStatusCodes }
    });
  }

  private async getFunnelData(matchConditions: any): Promise<FunnelData> {
    const [presenceStatusCodes, salesStatusCodes] = await Promise.all([
      getPresenceStatusCodes(),
      getSalesStatusCodes()
    ]);

    // Get total reservations (filtered by visitDate)
    const totalReservations = await Reservation.countDocuments(matchConditions);

    // Get confirmed reservations (presence status)
    const confirmedReservations = await Reservation.countDocuments({
      ...matchConditions,
      status: { $in: presenceStatusCodes }
    });

    // Get sales reservations (sales status)
    const salesReservations = await Reservation.countDocuments({
      ...matchConditions,
      status: { $in: salesStatusCodes }
    });

    // Calculate conversion rates
    const reservationToConfirmed = totalReservations > 0 ? confirmedReservations / totalReservations : 0;
    const confirmedToSales = confirmedReservations > 0 ? salesReservations / confirmedReservations : 0;
    const reservationToSales = totalReservations > 0 ? salesReservations / totalReservations : 0;

    return {
      totalReservations,
      confirmedReservations,
      salesReservations,
      conversionRates: {
        reservationToConfirmed: Math.round(reservationToConfirmed * 10000) / 10000, // 4 decimal places
        confirmedToSales: Math.round(confirmedToSales * 10000) / 10000, // 4 decimal places
        reservationToSales: Math.round(reservationToSales * 10000) / 10000 // 4 decimal places
      }
    };
  }

  private async getSalesByBranch(matchConditions: any): Promise<BranchSalesData[]> {
    const salesStatusCodes = await getSalesStatusCodes();

    const pipeline: any[] = [
      { $match: matchConditions },
      {
        $match: {
          status: { $in: salesStatusCodes },
          'preferences.branchId': { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          // Normalize branchId to string to ensure consistent grouping
          normalizedBranchId: { $toString: '$preferences.branchId' }
        }
      },
      {
        $group: {
          _id: '$normalizedBranchId',
          count: { $sum: 1 },
          amount: { $sum: { $ifNull: ['$sellingAmount', 0] } }
        }
      },
      {
        $lookup: {
          from: 'branches',
          let: { branchIdStr: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', { $toObjectId: '$$branchIdStr' }]
                }
              }
            }
          ],
          as: 'branch'
        }
      },
      {
        $unwind: {
          path: '$branch',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          branchId: { $toString: '$_id' },
          branchName: { $ifNull: ['$branch.name', 'Unknown Branch'] },
          count: 1,
          amount: 1
        }
      },
      { $sort: { branchName: 1 } }
    ];

    const result = await Reservation.aggregate(pipeline);
    return result.map((item: any) => ({
      branchId: item.branchId,
      branchName: item.branchName,
      count: item.count,
      amount: item.amount
    }));
  }

  private async getReservationsCreatedByBranch(matchConditions: any): Promise<BranchReservationData[]> {
    const pipeline: any[] = [
      { $match: matchConditions },
      {
        $match: {
          'preferences.branchId': { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          // Normalize branchId to string to ensure consistent grouping
          normalizedBranchId: { $toString: '$preferences.branchId' }
        }
      },
      {
        $group: {
          _id: '$normalizedBranchId',
          count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'branches',
          let: { branchIdStr: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', { $toObjectId: '$$branchIdStr' }]
                }
              }
            }
          ],
          as: 'branch'
        }
      },
      {
        $unwind: {
          path: '$branch',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          branchId: { $toString: '$_id' },
          branchName: { $ifNull: ['$branch.name', 'Unknown Branch'] },
          count: 1
        }
      },
      { $sort: { branchName: 1 } }
    ];

    const result = await Reservation.aggregate(pipeline);
    return result.map((item: any) => ({
      branchId: item.branchId,
      branchName: item.branchName,
      count: item.count
    }));
  }

  private async getSellerPerformance(matchConditions: any): Promise<SellerPerformanceData[]> {
    const salesStatusCodes = await getSalesStatusCodes();

    const pipeline: any[] = [
      { $match: matchConditions },
      {
        $match: {
          assigned_user_id: { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          // Normalize assigned_user_id to string to ensure consistent grouping
          normalizedUserId: { $toString: '$assigned_user_id' },
          // Add a field to identify sales based on status
          isSale: { $in: ['$status', salesStatusCodes] }
        }
      },
      {
        $group: {
          _id: '$normalizedUserId',
          reservationsCount: { $sum: 1 },
          salesCount: {
            $sum: {
              $cond: ['$isSale', 1, 0]
            }
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          let: { userIdStr: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', { $toObjectId: '$$userIdStr' }]
                }
              }
            }
          ],
          as: 'user'
        }
      },
      {
        $unwind: {
          path: '$user',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $addFields: {
          closingRate: {
            $cond: [
              { $gt: ['$reservationsCount', 0] },
              { $divide: ['$salesCount', '$reservationsCount'] },
              0
            ]
          }
        }
      },
      {
        $project: {
          sellerId: { $toString: '$_id' },
          sellerName: { $ifNull: ['$user.name', 'Unknown Seller'] },
          reservationsCount: 1,
          salesCount: 1,
          closingRate: 1
        }
      },
      { $sort: { salesCount: -1, closingRate: -1 } } // Sort by sales count desc, then closing rate desc
    ];

    const result = await Reservation.aggregate(pipeline);
    return result.map((item: any) => ({
      sellerId: item.sellerId,
      sellerName: item.sellerName,
      reservationsCount: item.reservationsCount,
      salesCount: item.salesCount,
      closingRate: Math.round(item.closingRate * 10000) / 10000 // 4 decimal places for percentage
    }));
  }

  private async getPapPerformance(createdAtMatchConditions: any, visitDateMatchConditions: any): Promise<PapPerformanceData[]> {
    const [presenceStatusCodes, salesStatusCodes] = await Promise.all([
      getPresenceStatusCodes(),
      getSalesStatusCodes()
    ]);

    // Get all reservations created by PAPs (using createdAt filter)
    const createdReservationsPipeline: any[] = [
      { $match: createdAtMatchConditions },
      {
        $match: {
          partnerId: { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          normalizedPartnerId: { $toString: '$partnerId' }
        }
      },
      {
        $group: {
          _id: '$normalizedPartnerId',
          reservationsCreatedCount: { $sum: 1 }
        }
      }
    ];

    // Get presence reservations by PAPs (using visitDate filter)
    const presenceReservationsPipeline: any[] = [
      { $match: visitDateMatchConditions },
      {
        $match: {
          partnerId: { $exists: true, $ne: null },
          status: { $in: presenceStatusCodes }
        }
      },
      {
        $addFields: {
          normalizedPartnerId: { $toString: '$partnerId' }
        }
      },
      {
        $group: {
          _id: '$normalizedPartnerId',
          presenceReservationsCount: { $sum: 1 }
        }
      }
    ];

    // Get sales by PAPs (using visitDate filter)
    const salesPipeline: any[] = [
      { $match: visitDateMatchConditions },
      {
        $match: {
          partnerId: { $exists: true, $ne: null },
          status: { $in: salesStatusCodes }
        }
      },
      {
        $addFields: {
          normalizedPartnerId: { $toString: '$partnerId' }
        }
      },
      {
        $group: {
          _id: '$normalizedPartnerId',
          salesCount: { $sum: 1 }
        }
      }
    ];

    // Execute all pipelines in parallel
    const [createdResults, presenceResults, salesResults] = await Promise.all([
      Reservation.aggregate(createdReservationsPipeline),
      Reservation.aggregate(presenceReservationsPipeline),
      Reservation.aggregate(salesPipeline)
    ]);

    // Create maps for easy lookup
    const createdMap = new Map(createdResults.map(item => [item._id, item.reservationsCreatedCount]));
    const presenceMap = new Map(presenceResults.map(item => [item._id, item.presenceReservationsCount]));
    const salesMap = new Map(salesResults.map(item => [item._id, item.salesCount]));

    // Get all unique PAP IDs
    const allPapIds = new Set([
      ...createdResults.map(item => item._id),
      ...presenceResults.map(item => item._id),
      ...salesResults.map(item => item._id)
    ]);

    // Get PAP user information
    const papUsersPipeline = [
      {
        $match: {
          _id: { $in: Array.from(allPapIds).map(id => Types.ObjectId.createFromHexString(id)) }
        }
      },
      {
        $project: {
          _id: 1,
          name: 1
        }
      }
    ];

    const User = (await import('@/models/User')).default;
    const papUsers = await User.aggregate(papUsersPipeline);
    const papUserMap = new Map(papUsers.map(user => [user._id.toString(), user.name]));

    // Combine all data
    const papPerformanceData: PapPerformanceData[] = Array.from(allPapIds).map(papId => {
      const reservationsCreatedCount = createdMap.get(papId) || 0;
      const presenceReservationsCount = presenceMap.get(papId) || 0;
      const salesCount = salesMap.get(papId) || 0;

      const presenceRate = reservationsCreatedCount > 0 ? presenceReservationsCount / reservationsCreatedCount : 0;
      const closingRate = reservationsCreatedCount > 0 ? salesCount / reservationsCreatedCount : 0;

      return {
        papId,
        papName: papUserMap.get(papId) || 'Unknown PAP',
        reservationsCreatedCount,
        presenceReservationsCount,
        salesCount,
        presenceRate: Math.round(presenceRate * 10000) / 10000, // 4 decimal places for percentage
        closingRate: Math.round(closingRate * 10000) / 10000 // 4 decimal places for percentage
      };
    });

    // Sort by reservations created count (descending)
    return papPerformanceData.sort((a, b) => b.reservationsCreatedCount - a.reservationsCreatedCount);
  }

  private calculateDaysDifference(dateFrom?: string, dateTo?: string): number {
    if (!dateFrom || !dateTo) {
      return 1; // Default to 1 day if no date range specified
    }

    const start = new Date(dateFrom);
    const end = new Date(dateTo);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 ? diffDays : 1;
  }

  /**
   * Adjust compare stats to match filter period/scope for meaningful comparison
   * Always normalize compare data to filter scope, not the other way around
   */
  private adjustCompareStats(
    compareStats: ReservationStats,
    filterDays: number,
    compareDays: number,
    filterBranches: number,
    compareBranches: number
  ): ReservationStats {
    // Calculate daily averages from compare stats
    const compareDailyAvgCreated = compareDays > 0 ? compareStats.totalCreated / compareDays : 0;
    const compareDailyAvgSales = compareDays > 0 ? compareStats.totalSales / compareDays : 0;
    const compareDailyAvgSalesAmount = compareDays > 0 ? compareStats.totalSalesAmount / compareDays : 0;
    const compareDailyAvgPresence = compareDays > 0 ? compareStats.presenceCount / compareDays : 0;

    // Calculate per-branch averages if needed
    let branchAdjustment = 1;
    if (filterBranches > 0 && compareBranches > 0 && filterBranches !== compareBranches) {
      branchAdjustment = filterBranches / compareBranches;
    }

    // Normalize compare stats to filter period and branch scope
    const adjustedTotalCreated = Math.round(compareDailyAvgCreated * filterDays * branchAdjustment);
    const adjustedTotalSales = Math.round(compareDailyAvgSales * filterDays * branchAdjustment);
    const adjustedTotalSalesAmount = Math.round(compareDailyAvgSalesAmount * filterDays * branchAdjustment);
    const adjustedPresenceCount = Math.round(compareDailyAvgPresence * filterDays * branchAdjustment);

    // Calculate adjusted derived metrics
    const adjustedPresenceRate = adjustedTotalCreated > 0 ? adjustedPresenceCount / adjustedTotalCreated : 0;

    return {
      totalCreated: adjustedTotalCreated,
      totalSales: adjustedTotalSales,
      totalSalesAmount: adjustedTotalSalesAmount,
      presenceRate: Math.round(adjustedPresenceRate * 10000) / 10000,
      presenceCount: adjustedPresenceCount,
      funnelData: {
        totalReservations: adjustedTotalCreated,
        confirmedReservations: adjustedPresenceCount,
        salesReservations: adjustedTotalSales,
        conversionRates: {
          reservationToConfirmed: adjustedTotalCreated > 0 ? adjustedPresenceCount / adjustedTotalCreated : 0,
          confirmedToSales: adjustedPresenceCount > 0 ? adjustedTotalSales / adjustedPresenceCount : 0,
          reservationToSales: adjustedTotalCreated > 0 ? adjustedTotalSales / adjustedTotalCreated : 0
        }
      },
      salesByBranch: [], // Branch-level normalization not needed for comparison calculations
      reservationsCreatedByBranch: [], // Branch-level normalization not needed for comparison calculations
      sellerPerformance: [], // Performance comparisons handled separately by user ID matching
      papPerformance: [], // Performance comparisons handled separately by user ID matching
      salesByMonth: compareStats.salesByMonth, // Use original compare data for monthly charts
      reservationsCreatedByMonth: compareStats.reservationsCreatedByMonth // Use original compare data for monthly charts
    };
  }

  private calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) {
      return current > 0 ? 100 : 0;
    }
    return ((current - previous) / previous) * 100;
  }

  private async getSalesByMonth(filters: ReservationStatsFilters): Promise<MonthlyData[]> {
    const salesStatusCodes = await getSalesStatusCodes();

    const matchConditions: any = {
      isDeleted: { $ne: true },
      status: { $in: salesStatusCodes }
    };

    // Branch filter
    if (filters.branches && filters.branches.length > 0) {
      matchConditions['preferences.branchId'] = { $in: filters.branches };
    }

    // Date range filter using visitDate
    if (filters.dateFrom || filters.dateTo) {
      const visitDateFilter: any = {};
      if (filters.dateFrom) {
        // Format as YYYY-MM-DD without timezone conversion
        visitDateFilter.$gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        // Format as YYYY-MM-DD without timezone conversion
        visitDateFilter.$lte = filters.dateTo;
      }
      matchConditions['preferences.visitDate'] = visitDateFilter;
    }

    // Agent/Seller filter
    if (filters.agentOrSeller === 'agent') {
      matchConditions.partnerId = { $exists: true, $ne: null };
    } else if (filters.agentOrSeller === 'seller') {
      matchConditions.assigned_user_id = { $exists: true, $ne: null };
    }

    // User filter
    if (filters.userId) {
      if (filters.agentOrSeller === 'agent') {
        matchConditions.partnerId = filters.userId;
      } else if (filters.agentOrSeller === 'seller') {
        matchConditions.assigned_user_id = filters.userId;
      }
    }

    const pipeline: any[] = [
      { $match: matchConditions },
      {
        $addFields: {
          // Extract year and month from visitDate string (YYYY-MM-DD format)
          visitYear: { $toInt: { $substr: ['$preferences.visitDate', 0, 4] } },
          visitMonth: { $toInt: { $substr: ['$preferences.visitDate', 5, 2] } }
        }
      },
      {
        $group: {
          _id: {
            year: '$visitYear',
            month: '$visitMonth'
          },
          count: { $sum: 1 },
          amount: { $sum: { $ifNull: ['$sellingAmount', 0] } }
        }
      },
      {
        $project: {
          month: {
            $concat: [
              { $toString: '$_id.year' },
              '-',
              {
                $cond: [
                  { $lt: ['$_id.month', 10] },
                  { $concat: ['0', { $toString: '$_id.month' }] },
                  { $toString: '$_id.month' }
                ]
              }
            ]
          },
          count: 1,
          amount: 1
        }
      },
      { $sort: { month: 1 } }
    ];

    const result = await Reservation.aggregate(pipeline);
    return result.map((item: any) => ({
      month: item.month,
      count: item.count,
      amount: item.amount
    }));
  }

  private async getReservationsCreatedByMonth(filters: ReservationStatsFilters): Promise<MonthlyData[]> {
    const matchConditions: any = {
      isDeleted: { $ne: true }
    };

    // Branch filter
    if (filters.branches && filters.branches.length > 0) {
      matchConditions['preferences.branchId'] = { $in: filters.branches };
    }

    // Date range filter using createdAt
    if (filters.dateFrom || filters.dateTo) {
      const dateFilter: any = {};
      if (filters.dateFrom) {
        dateFilter.$gte = this.convertClientDateToUTC(filters.dateFrom, filters.timezone, 'start');
      }
      if (filters.dateTo) {
        dateFilter.$lte = this.convertClientDateToUTC(filters.dateTo, filters.timezone, 'end');
      }
      matchConditions.createdAt = dateFilter;
    }

    // Agent/Seller filter
    if (filters.agentOrSeller === 'agent') {
      matchConditions.partnerId = { $exists: true, $ne: null };
    } else if (filters.agentOrSeller === 'seller') {
      matchConditions.assigned_user_id = { $exists: true, $ne: null };
    }

    // User filter
    if (filters.userId) {
      if (filters.agentOrSeller === 'agent') {
        matchConditions.partnerId = filters.userId;
      } else if (filters.agentOrSeller === 'seller') {
        matchConditions.assigned_user_id = filters.userId;
      }
    }

    const pipeline: any[] = [
      { $match: matchConditions },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          month: {
            $concat: [
              { $toString: '$_id.year' },
              '-',
              {
                $cond: [
                  { $lt: ['$_id.month', 10] },
                  { $concat: ['0', { $toString: '$_id.month' }] },
                  { $toString: '$_id.month' }
                ]
              }
            ]
          },
          count: 1
        }
      },
      { $sort: { month: 1 } }
    ];

    const result = await Reservation.aggregate(pipeline);
    return result.map((item: any) => ({
      month: item.month,
      count: item.count
    }));
  }

  /**
   * Convert client date to UTC Date object for database queries
   * @param dateString - Date string in YYYY-MM-DD format from client
   * @param timezone - Client timezone (e.g., 'America/Toronto')
   * @param boundary - 'start' for beginning of day, 'end' for end of day
   */
  private convertClientDateToUTC(dateString: string, timezone?: string, boundary: 'start' | 'end' = 'start'): Date {
    if (!timezone) {
      // Fallback to UTC if no timezone provided
      const date = new Date(dateString + (boundary === 'start' ? 'T00:00:00.000Z' : 'T23:59:59.999Z'));
      return date;
    }

    try {
      // Create the time string for start or end of day
      const timeString = boundary === 'start' ? '00:00:00.000' : '23:59:59.999';

      // Create a date string in client timezone
      const clientDateTimeString = `${dateString} ${timeString}`;

      // Convert client local time to UTC using date-fns-tz
      // This properly handles EST/EDT transitions
      const result = fromZonedTime(clientDateTimeString, timezone);
      return result;
    } catch (error) {
      console.error('Timezone conversion error:', error);
      // Fallback to UTC
      const date = new Date(dateString + (boundary === 'start' ? 'T00:00:00.000Z' : 'T23:59:59.999Z'));
      return date;
    }
  }



  /**
   * Calculate seller performance comparisons
   */
  private calculateSellerPerformanceComparisons(
    currentData: SellerPerformanceData[],
    compareData: SellerPerformanceData[]
  ): SellerPerformanceData[] {
    return currentData.map(current => {
      const compare = compareData.find(c => c.sellerId === current.sellerId);

      if (!compare) {
        return {
          ...current,
          reservationsCountChange: undefined,
          salesCountChange: undefined,
          closingRateChange: undefined
        };
      }

      return {
        ...current,
        reservationsCountChange: this.calculatePercentageChange(current.reservationsCount, compare.reservationsCount),
        salesCountChange: this.calculatePercentageChange(current.salesCount, compare.salesCount),
        closingRateChange: this.calculatePercentageChange(current.closingRate, compare.closingRate)
      };
    });
  }

  /**
   * Calculate PAP performance comparisons
   */
  private calculatePapPerformanceComparisons(
    currentData: PapPerformanceData[],
    compareData: PapPerformanceData[]
  ): PapPerformanceData[] {
    return currentData.map(current => {
      const compare = compareData.find(c => c.papId === current.papId);

      if (!compare) {
        return {
          ...current,
          reservationsCreatedCountChange: undefined,
          presenceReservationsCountChange: undefined,
          salesCountChange: undefined,
          presenceRateChange: undefined,
          closingRateChange: undefined
        };
      }

      return {
        ...current,
        reservationsCreatedCountChange: this.calculatePercentageChange(current.reservationsCreatedCount, compare.reservationsCreatedCount),
        presenceReservationsCountChange: this.calculatePercentageChange(current.presenceReservationsCount, compare.presenceReservationsCount),
        salesCountChange: this.calculatePercentageChange(current.salesCount, compare.salesCount),
        presenceRateChange: this.calculatePercentageChange(current.presenceRate, compare.presenceRate),
        closingRateChange: this.calculatePercentageChange(current.closingRate, compare.closingRate)
      };
    });
  }

  /**
   * Validate that compare settings don't have different agent types than filters
   */
  validateCompareSettings(filters: ReservationStatsFilters, compareFilters: ReservationStatsFilters): boolean {
    if (filters.agentOrSeller && compareFilters.agentOrSeller) {
      return filters.agentOrSeller === compareFilters.agentOrSeller;
    }
    return true; // Allow if either is not set
  }
}
