// Branch stats email template system
import {
  BranchStatsEmailData,
  BranchStatsTemplateOptions,
  BranchStatsResult,
  EmailTemplateVariables
} from '@/types/branch-stats-email';
import { generateBranchSection } from './branch-stats-template-sections';

/**
 * Generate branch stats email template
 */
export function getBranchStatsEmailTemplate(
  data: BranchStatsEmailData,
  options: BranchStatsTemplateOptions = {
    includeDetailedReservations: true,
    includeAllergiesBreakdown: true,
    includeServiceTypesBreakdown: true,
    compactMode: false
  }
): string {
  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Statistiques de succursale - ${data.targetDate}</title>
      <style>
        ${getBranchStatsEmailStyles()}
      </style>
    </head>
    <body>
      <div class="email-wrapper">
        ${getBranchStatsEmailHeader(data)}
        ${getBranchStatsEmailContent(data, options)}
        ${getBranchStatsEmailFooter(data.exportUrls)}
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate email subject line
 */
export function getBranchStatsEmailSubject(data: BranchStatsEmailData): string {
  const dateLogicText = data.dateLogic === 'current' 
    ? 'Aujourd\'hui' 
    : 'Demain';
    
  const branchCount = data.branchStats.length;
  const totalReservations = data.branchStats.reduce((sum, branch) => sum + branch.summary.totalReservations, 0);
  
  if (branchCount === 1) {
    return `📊 Stats ${data.branchStats[0].branchName} - ${dateLogicText} (${totalReservations} réservations)`;
  } else {
    return `📊 Stats ${branchCount} succursales - ${dateLogicText} (${totalReservations} réservations)`;
  }
}

/**
 * Process branch stats email template with variable substitution
 */
export function processBranchStatsEmailTemplate(template: string, data: BranchStatsEmailData): string {
  // For now, the template is already processed during generation
  // This function exists for consistency with the regular stats email templates
  // and can be extended in the future for additional processing
  return template;
}

/**
 * Process template variables
 */
export function processTemplateVariables(
  template: string,
  variables: EmailTemplateVariables
): string {
  let processed = template;

  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    processed = processed.replace(new RegExp(placeholder, 'g'), value || '');
  });

  return processed;
}

/**
 * Get default template variables
 */
export function getDefaultTemplateVariables(data: BranchStatsEmailData): EmailTemplateVariables {
  return {
    TARGET_DATE: formatDateForDisplay(data.targetDate),
    LOGO_PLACEHOLDER: '🏢', // Will be replaced with actual logo
    COMPANY_NAME: 'AMQ Partners',
    COMPANY_ADDRESS: '',
    UNSUBSCRIBE_URL: '/admin-dashboard/stats-email-settings',
    EXPORT_CSV_URL: data.exportUrls?.csv,
    EXPORT_PDF_URL: data.exportUrls?.pdf
  };
}

/**
 * Format date for display in French
 */
function formatDateForDisplay(dateString: string): string {
  // Parse the date string as local date to avoid timezone issues
  // dateString is in format YYYY-MM-DD
  const [year, month, day] = dateString.split('-').map(Number);
  const date = new Date(year, month - 1, day); // month is 0-indexed

  const options: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };

  return date.toLocaleDateString('fr-CA', options);
}

/**
 * Email header section
 */
function getBranchStatsEmailHeader(data: BranchStatsEmailData): string {
  const dateLogicText = data.dateLogic === 'current' 
    ? 'Statistiques du jour' 
    : 'Statistiques pour demain';
    
  const logicIndicator = data.dateLogic === 'current'
    ? '📅 Envoi matinal - Données du jour'
    : '🌅 Envoi après-midi - Données de demain';

  return `
    <div class="header">
      <h1>{{LOGO_PLACEHOLDER}} {{COMPANY_NAME}}</h1>
      <h1>Statistiques de Succursale</h1>
      <div class="date-info">
        <strong>${dateLogicText}</strong><br>
        {{TARGET_DATE}}
      </div>
      <div class="logic-indicator">
        ${logicIndicator}
      </div>
    </div>
  `;
}

/**
 * Email content section
 */
function getBranchStatsEmailContent(
  data: BranchStatsEmailData,
  options: BranchStatsTemplateOptions
): string {
  let content = '<div class="content">';
  
  // Add greeting
  content += `
    <div class="greeting">
      <p>Bonjour,</p>
      <p>Voici les statistiques détaillées pour les succursales sélectionnées pour la date du <strong>{{TARGET_DATE}}</strong>.</p>
    </div>
  `;
  
  // Add branch sections
  data.branchStats.forEach(branchStats => {
    content += generateBranchSection(branchStats, data.selectedStats, options);
  });
  
  content += '</div>';
  return content;
}

/**
 * Email footer section
 */
function getBranchStatsEmailFooter(exportUrls?: { csv: string; pdf: string }): string {
  let footer = `
    <div class="footer">
      <div class="footer-content">
        <div class="export-section">
          <h4>📥 Exporter les données</h4>
          <div class="export-buttons">
  `;
  
  if (exportUrls?.csv) {
    footer += `<a href="{{EXPORT_CSV_URL}}" class="export-btn csv-btn">📊 Télécharger CSV</a>`;
  }
  
  if (exportUrls?.pdf) {
    footer += `<a href="{{EXPORT_PDF_URL}}" class="export-btn pdf-btn">📄 Télécharger PDF</a>`;
  }
  
  footer += `
          </div>
        </div>
        <div class="company-info">
          <p><strong>{{COMPANY_NAME}}</strong></p>
          <p>{{COMPANY_ADDRESS}}</p>
          <p><a href="{{UNSUBSCRIBE_URL}}">Gérer les préférences d'email</a></p>
        </div>
      </div>
    </div>
  `;
  
  return footer;
}

/**
 * Email CSS styles
 */
function getBranchStatsEmailStyles(): string {
  return `
    /* Base styles */
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      color: #333;
    }

    .email-wrapper {
      max-width: 800px;
      margin: 0 auto;
      background-color: #ffffff;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    /* Header styles */
    .header {
      background: linear-gradient(135deg, #000000 0%, #**********%);
      color: white;
      padding: 20px;
      text-align: center;
    }

    .header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: bold;
    }

    .header .date-info {
      margin-top: 10px;
      font-size: 16px;
      opacity: 0.9;
    }

    .header .logic-indicator {
      background: rgba(255,255,255,0.2);
      padding: 5px 15px;
      border-radius: 20px;
      display: inline-block;
      margin-top: 10px;
      font-size: 14px;
    }

    /* Content styles */
    .content {
      padding: 20px;
    }

    .greeting {
      margin-bottom: 30px;
      font-size: 16px;
      line-height: 1.5;
    }

    .branch-section {
      margin-bottom: 40px;
      border: 1px solid #e5e5e5;
      border-radius: 8px;
      overflow: hidden;
    }

    .branch-header {
      background-color: #f8f9fa;
      padding: 15px 20px;
      border-bottom: 1px solid #e5e5e5;
    }

    .branch-name {
      font-size: 20px;
      font-weight: bold;
      color: #dc2626;
      margin: 0;
    }

    .branch-summary {
      display: flex;
      gap: 20px;
      margin-top: 10px;
      flex-wrap: wrap;
    }

    .summary-item {
      background: white;
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 14px;
    }

    .summary-item .number {
      font-weight: bold;
      color: #dc2626;
    }

    /* Statistics sections */
    .stats-section {
      padding: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 20px;
    }

    .stat-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 15px;
    }

    .stat-card h4 {
      margin: 0 0 10px 0;
      color: #495057;
      font-size: 16px;
      border-bottom: 2px solid #dc2626;
      padding-bottom: 5px;
    }

    /* Demographics styles */
    .demographics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 15px;
    }

    .demo-item {
      background: white;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #dee2e6;
    }

    .demo-breakdown {
      margin-top: 5px;
      color: #6c757d;
    }

    .demo-total {
      background: #e7f3ff;
      border: 1px solid #b3d9ff;
      border-radius: 4px;
      padding: 10px;
      text-align: center;
      color: #0056b3;
    }

    /* Allergies styles */
    .allergies-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .allergies-list li {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 8px 12px;
      margin-bottom: 5px;
      font-weight: 500;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .allergy-count {
      background: #dc2626;
      color: white;
      border-radius: 12px;
      padding: 2px 8px;
      font-size: 12px;
    }

    /* Service types styles */
    .service-types-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
    }

    .service-category h5 {
      margin: 0 0 10px 0;
      color: #495057;
      font-size: 14px;
      font-weight: 600;
    }

    .service-type-item {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 8px 10px;
      margin-bottom: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .service-type-name {
      font-weight: 500;
      font-size: 13px;
    }

    .service-type-count {
      background: #28a745;
      color: white;
      border-radius: 12px;
      padding: 2px 6px;
      font-size: 11px;
      font-weight: bold;
    }

    /* Reservations table */
    .reservations-section {
      margin-top: 20px;
    }

    .reservations-section h4 {
      margin: 0 0 15px 0;
      color: #495057;
      font-size: 16px;
      border-bottom: 2px solid #dc2626;
      padding-bottom: 5px;
    }

    .reservations-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .reservations-table th,
    .reservations-table td {
      border: 1px solid #dee2e6;
      padding: 8px 10px;
      text-align: left;
      font-size: 13px;
    }

    .reservations-table th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #495057;
    }

    .reservations-table tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    .allergies-cell,
    .services-cell {
      max-width: 150px;
      word-wrap: break-word;
      font-size: 12px;
    }

    .no-data {
      color: #6c757d;
      font-style: italic;
      text-align: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 4px;
      margin: 0;
    }

    /* Footer styles */
    .footer {
      background-color: #f8f9fa;
      border-top: 1px solid #e5e5e5;
      padding: 20px;
    }

    .footer-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
    }

    .export-section h4 {
      margin: 0 0 15px 0;
      color: #495057;
    }

    .export-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .export-btn {
      display: inline-block;
      padding: 10px 20px;
      border-radius: 6px;
      text-decoration: none;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.2s;
    }

    .csv-btn {
      background-color: #28a745;
      color: white;
    }

    .csv-btn:hover {
      background-color: #218838;
    }

    .pdf-btn {
      background-color: #dc3545;
      color: white;
    }

    .pdf-btn:hover {
      background-color: #c82333;
    }

    .company-info {
      text-align: right;
      font-size: 14px;
      color: #6c757d;
    }

    .company-info p {
      margin: 5px 0;
    }

    .company-info a {
      color: #dc2626;
      text-decoration: none;
    }

    .company-info a:hover {
      text-decoration: underline;
    }

    /* Mobile responsive */
    @media (max-width: 600px) {
      .email-wrapper {
        margin: 0;
        box-shadow: none;
      }

      .header {
        padding: 15px;
      }

      .header h1 {
        font-size: 20px;
      }

      .content {
        padding: 15px;
      }

      .branch-summary {
        flex-direction: column;
        gap: 10px;
      }

      .stats-grid {
        grid-template-columns: 1fr;
      }

      .demographics-grid {
        grid-template-columns: 1fr;
      }

      .service-types-grid {
        grid-template-columns: 1fr;
      }

      .reservations-table {
        font-size: 11px;
      }

      .reservations-table th,
      .reservations-table td {
        padding: 6px 4px;
      }

      .allergies-cell,
      .services-cell {
        max-width: 100px;
        font-size: 10px;
      }

      .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .company-info {
        text-align: left;
      }

      .export-buttons {
        flex-direction: column;
      }

      .export-btn {
        text-align: center;
      }
    }

    /* Print styles */
    @media print {
      .email-wrapper {
        box-shadow: none;
        max-width: none;
      }

      .header {
        background: #dc2626 !important;
        -webkit-print-color-adjust: exact;
      }

      .export-section {
        display: none;
      }
    }
  `;
}

export type { BranchStatsEmailData };
