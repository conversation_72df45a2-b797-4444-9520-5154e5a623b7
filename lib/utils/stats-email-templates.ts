export interface StatsEmailData {
  date: string;
  totalSales: {
    amount: number;
    count: number;
  };
  totalReservationsScheduled: number; // Reservations scheduled for this date
  totalReservationsCreated: number;   // Reservations created on this date
  totalPresence: number;
  salesByBranch: Array<{
    branchName: string;
    amount: number;
    count: number;
  }>;
  reservationsByBranch: Array<{
    branchName: string;
    count: number;
  }>;
  reservationsCreatedByBranch: Array<{
    branchName: string;
    count: number;
  }>;
  presenceByBranch: Array<{
    branchName: string;
    count: number;
  }>;
  topSellers: Array<{
    name: string;
    amount: number;
    count: number;
  }>;
  topPaps: Array<{
    name: string;
    count: number;
  }>;
  selectedStats: string[];
  exportUrls: {
    csv: string;
    pdf: string;
  };
}

/**
 * Main function to generate the complete daily stats email template
 */
export function getDailyStatsEmailTemplate(data: StatsEmailData): string {
  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Statistiques quotidiennes - ${data.date}</title>
      <style>
        ${getEmailStyles()}
      </style>
    </head>
    <body>
      <div class="email-wrapper">
        ${getEmailHeader()}
        ${getEmailContent(data)}
        ${getEmailFooter(data.exportUrls)}
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate the email header with logo and title
 */
function getEmailHeader(): string {
  return `
    <div class="header">
      <div class="logo-container">
        {{LOGO_PLACEHOLDER}}
      </div>
      <div class="header-content">
        <h1 class="title">Statistiques Quotidiennes</h1>
        <div class="subtitle">Rapport automatique - {{DATE}}</div>
      </div>
    </div>
  `;
}

/**
 * Generate the email header for weekly reports
 */
function getWeeklyEmailHeader(): string {
  return `
    <div class="header">
      <div class="logo-container">
        {{LOGO_PLACEHOLDER}}
      </div>
      <div class="header-content">
        <h1 class="title">Statistiques Hebdomadaires</h1>
        <div class="subtitle">Rapport automatique - {{START_DATE}} au {{END_DATE}}</div>
      </div>
    </div>
  `;
}

/**
 * Generate the main email content with statistics sections
 */
function getEmailContent(data: StatsEmailData): string {
  let content = '<div class="content">';
  
  // Add greeting
  content += `
    <div class="greeting">
      <p>Bonjour,</p>
      <p>Voici le résumé des statistiques pour la journée du <strong>{{DATE}}</strong>.</p>
    </div>
  `;
  
  // Add statistics sections based on selectedStats
  if (data.selectedStats.includes('totalSales')) {
    content += getTotalSalesSection(data.totalSales);
  }

  if (data.selectedStats.includes('totalReservationsScheduled')) {
    content += getTotalReservationsScheduledSection(data.totalReservationsScheduled);
  }

  if (data.selectedStats.includes('totalReservationsCreated')) {
    content += getTotalReservationsCreatedSection(data.totalReservationsCreated);
  }

  if (data.selectedStats.includes('totalPresence')) {
    content += getTotalPresenceSection(data.totalPresence);
  }
  
  if (data.selectedStats.includes('salesByBranch')) {
    content += getSalesByBranchSection(data.salesByBranch);
  }
  
  if (data.selectedStats.includes('reservationsByBranch')) {
    content += getReservationsByBranchSection(data.reservationsByBranch);
  }

  if (data.selectedStats.includes('reservationsCreatedByBranch')) {
    content += getReservationsCreatedByBranchSection(data.reservationsCreatedByBranch);
  }

  if (data.selectedStats.includes('presenceByBranch')) {
    content += getPresenceByBranchSection(data.presenceByBranch);
  }
  
  if (data.selectedStats.includes('topSellers')) {
    content += getTopSellersSection(data.topSellers);
  }
  
  if (data.selectedStats.includes('topPaps')) {
    content += getTopPapsSection(data.topPaps);
  }
  
  content += '</div>';
  return content;
}

/**
 * Generate the email footer with export buttons
 */
function getEmailFooter(exportUrls: { csv: string; pdf: string }): string {
  console.log('getEmailFooter called with exportUrls:', exportUrls);

  // Ensure we have valid URLs or provide fallbacks
  const csvUrl = exportUrls.csv && exportUrls.csv !== '#' && !exportUrls.csv.startsWith('#') ? exportUrls.csv : '#csv-not-available';
  const pdfUrl = exportUrls.pdf && exportUrls.pdf !== '#' && !exportUrls.pdf.startsWith('#') ? exportUrls.pdf : '#pdf-not-available';

  console.log('Using URLs - CSV:', csvUrl, 'PDF:', pdfUrl);

  // Check if exports are available
  const csvAvailable = !csvUrl.startsWith('#');
  const pdfAvailable = !pdfUrl.startsWith('#');

  return `
    <div class="export-section">
      <div class="export-title">Télécharger les données</div>
      <div class="export-description">
        Téléchargez ces statistiques dans le format de votre choix
      </div>
      <table class="export-buttons">
        <tr>
          <td>
            ${csvAvailable ?
              `<a href="${csvUrl}" class="export-button csv-button" target="_blank">
                📊 Télécharger CSV
              </a>` :
              `<span class="export-button export-button-disabled" title="Export CSV temporairement indisponible">
                📊 CSV indisponible
              </span>`
            }
          </td>
          <td>
            ${pdfAvailable ?
              `<a href="${pdfUrl}" class="export-button pdf-button" target="_blank">
                📄 Télécharger PDF
              </a>` :
              `<span class="export-button export-button-disabled" title="Export PDF temporairement indisponible">
                📄 PDF indisponible
              </span>`
            }
          </td>
        </tr>
      </table>
      <div class="export-note">
        <small>Les liens de téléchargement expirent après 24 heures</small>
      </div>
    </div>
    <div class="footer">
      <div class="footer-text">
        Ce rapport a été généré automatiquement par le système AMQ Partners.
      </div>
      <div class="company-info">
        {{COMPANY_NAME}} - {{COMPANY_ADDRESS}}
      </div>
    </div>
  `;
}

// Individual section generators will be added in the next part
function getTotalSalesSection(totalSales: { amount: number; count: number }): string {
  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">💰</div>
        Ventes Totales
      </div>
      <div class="stats-card">
        <div class="stats-value">${formatCurrency(totalSales.amount)}</div>
        <div class="stats-label">${totalSales.count} transaction${totalSales.count > 1 ? 's' : ''}</div>
      </div>
    </div>
  `;
}

function getTotalReservationsScheduledSection(totalReservationsScheduled: number): string {
  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">📅</div>
        Réservations Programmées
      </div>
      <div class="stats-card">
        <div class="stats-value">${totalReservationsScheduled}</div>
        <div class="stats-label">réservation${totalReservationsScheduled > 1 ? 's' : ''} programmée${totalReservationsScheduled > 1 ? 's' : ''}</div>
      </div>
    </div>
  `;
}

function getTotalReservationsCreatedSection(totalReservationsCreated: number): string {
  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">✨</div>
        Réservations Créées
      </div>
      <div class="stats-card">
        <div class="stats-value">${totalReservationsCreated}</div>
        <div class="stats-label">nouvelle${totalReservationsCreated > 1 ? 's' : ''} réservation${totalReservationsCreated > 1 ? 's' : ''}</div>
      </div>
    </div>
  `;
}

function getTotalPresenceSection(totalPresence: number): string {
  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">✅</div>
        Présences Totales
      </div>
      <div class="stats-card">
        <div class="stats-value">${totalPresence}</div>
        <div class="stats-label">client${totalPresence > 1 ? 's' : ''} présent${totalPresence > 1 ? 's' : ''}</div>
      </div>
    </div>
  `;
}

function getSalesByBranchSection(salesByBranch: Array<{ branchName: string; amount: number; count: number }>): string {
  if (salesByBranch.length === 0) {
    return `
      <div class="stats-section">
        <div class="section-title">
          <div class="section-icon">🏪</div>
          Ventes par Succursale
        </div>
        <div class="stats-card">
          <p>Aucune vente enregistrée</p>
        </div>
      </div>
    `;
  }

  const tableRows = salesByBranch.map(branch => `
    <tr>
      <td>${branch.branchName}</td>
      <td>${formatCurrency(branch.amount)}</td>
      <td>${branch.count}</td>
    </tr>
  `).join('');

  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">🏪</div>
        Ventes par Succursale
      </div>
      <table class="stats-table">
        <thead>
          <tr>
            <th>Succursale</th>
            <th>Montant</th>
            <th>Transactions</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    </div>
  `;
}

function getReservationsByBranchSection(reservationsByBranch: Array<{ branchName: string; count: number }>): string {
  if (reservationsByBranch.length === 0) {
    return `
      <div class="stats-section">
        <div class="section-title">
          <div class="section-icon">📋</div>
          Réservations par Succursale
        </div>
        <div class="stats-card">
          <p>Aucune réservation créée</p>
        </div>
      </div>
    `;
  }

  const tableRows = reservationsByBranch.map(branch => `
    <tr>
      <td>${branch.branchName}</td>
      <td>${branch.count}</td>
    </tr>
  `).join('');

  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">📋</div>
        Réservations par Succursale
      </div>
      <table class="stats-table">
        <thead>
          <tr>
            <th>Succursale</th>
            <th>Réservations</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    </div>
  `;
}

function getReservationsCreatedByBranchSection(reservationsCreatedByBranch: Array<{ branchName: string; count: number }>): string {
  if (!reservationsCreatedByBranch || reservationsCreatedByBranch.length === 0) {
    return `
      <div class="stats-section">
        <div class="section-title">
          <div class="section-icon">✨</div>
          Réservations Créées par Succursale
        </div>
        <div class="stats-card">
          <p>Aucune réservation créée</p>
        </div>
      </div>
    `;
  }

  const tableRows = reservationsCreatedByBranch.map(branch => `
    <tr>
      <td>${branch.branchName}</td>
      <td>${branch.count}</td>
    </tr>
  `).join('');

  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">✨</div>
        Réservations Créées par Succursale
      </div>
      <table class="stats-table">
        <thead>
          <tr>
            <th>Succursale</th>
            <th>Réservations Créées</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    </div>
  `;
}

function getPresenceByBranchSection(presenceByBranch: Array<{ branchName: string; count: number }>): string {
  if (presenceByBranch.length === 0) {
    return `
      <div class="stats-section">
        <div class="section-title">
          <div class="section-icon">📍</div>
          Présences par Succursale
        </div>
        <div class="stats-card">
          <p>Aucune présence enregistrée</p>
        </div>
      </div>
    `;
  }

  const tableRows = presenceByBranch.map(branch => `
    <tr>
      <td>${branch.branchName}</td>
      <td>${branch.count}</td>
    </tr>
  `).join('');

  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">📍</div>
        Présences par Succursale
      </div>
      <table class="stats-table">
        <thead>
          <tr>
            <th>Succursale</th>
            <th>Présences</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    </div>
  `;
}

function getTopSellersSection(topSellers: Array<{ name: string; amount: number; count: number }>): string {
  if (topSellers.length === 0) {
    return `
      <div class="stats-section">
        <div class="section-title">
          <div class="section-icon">🏆</div>
          Top 5 Vendeurs
        </div>
        <div class="stats-card">
          <p>Aucune vente enregistrée</p>
        </div>
      </div>
    `;
  }

  const tableRows = topSellers.map((seller, index) => `
    <tr>
      <td>${index + 1}</td>
      <td>${seller.name}</td>
      <td>${formatCurrency(seller.amount)}</td>
      <td>${seller.count}</td>
    </tr>
  `).join('');

  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">🏆</div>
        Top 5 Vendeurs
      </div>
      <table class="stats-table">
        <thead>
          <tr>
            <th>Rang</th>
            <th>Vendeur</th>
            <th>Montant</th>
            <th>Ventes</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    </div>
  `;
}

function getTopPapsSection(topPaps: Array<{ name: string; count: number }>): string {
  if (topPaps.length === 0) {
    return `
      <div class="stats-section">
        <div class="section-title">
          <div class="section-icon">⭐</div>
          Top 5 PAPs
        </div>
        <div class="stats-card">
          <p>Aucune activité PAP enregistrée</p>
        </div>
      </div>
    `;
  }

  const tableRows = topPaps.map((pap, index) => `
    <tr>
      <td>${index + 1}</td>
      <td>${pap.name}</td>
      <td>${pap.count}</td>
    </tr>
  `).join('');

  return `
    <div class="stats-section">
      <div class="section-title">
        <div class="section-icon">⭐</div>
        Top 5 PAPs
      </div>
      <table class="stats-table">
        <thead>
          <tr>
            <th>Rang</th>
            <th>PAP</th>
            <th>Activités</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    </div>
  `;
}

/**
 * Generate the CSS styles for the email template
 */
function getEmailStyles(): string {
  return `
    /* Reset and base styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333333;
      background-color: #f5f5f5;
    }

    .email-wrapper {
      max-width: 800px;
      margin: 0 auto;
      background-color: #ffffff;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Header styles */
    .header {
      background: linear-gradient(135deg, #000000 0%, #**********%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .logo-container {
      margin-bottom: 20px;
    }

    .company-logo {
      max-height: 60px;
      width: auto;
    }

    .title {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 8px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .subtitle {
      font-size: 16px;
      opacity: 0.9;
    }

    /* Content styles */
    .content {
      padding: 40px 30px;
    }

    .greeting {
      margin-bottom: 30px;
      font-size: 16px;
    }

    .stats-section {
      margin-bottom: 35px;
      border-left: 4px solid #dc2626;
      padding-left: 20px;
    }

    .section-title {
      font-size: 20px;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
    }

    .section-icon {
      width: 24px;
      height: 24px;
      margin-right: 10px;
      background-color: #dc2626;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }

    /* Statistics cards */
    .stats-card {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .stats-value {
      font-size: 32px;
      font-weight: bold;
      color: #dc2626;
      margin-bottom: 5px;
    }

    .stats-label {
      font-size: 14px;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    /* Tables */
    .stats-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }

    .stats-table th {
      background-color: #1f2937;
      color: white;
      padding: 12px;
      text-align: left;
      font-weight: 600;
    }

    .stats-table td {
      padding: 12px;
      border-bottom: 1px solid #e5e7eb;
    }

    .stats-table tr:nth-child(even) {
      background-color: #f9fafb;
    }

    .stats-table tr:hover {
      background-color: #f3f4f6;
    }

    /* Export buttons */
    .export-section {
      background-color: #f8f9fa;
      border-top: 2px solid #dc2626;
      padding: 30px;
      text-align: center;
    }

    .export-title {
      font-size: 18px;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 10px;
    }

    .export-description {
      font-size: 14px;
      color: #6b7280;
      margin-bottom: 20px;
    }

    .export-note {
      margin-top: 15px;
      color: #6b7280;
      font-size: 12px;
    }

    .export-buttons {
      margin: 0 auto;
      border-spacing: 20px 0;
      border-collapse: separate;
    }

    .export-buttons td {
      padding: 0;
      text-align: center;
    }

    .export-button {
      display: inline-block;
      padding: 12px 24px;
      background-color: #dc2626;
      color: white !important;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 600;
      font-size: 14px;
      border: none;
      cursor: pointer;
      min-width: 150px;
    }

    .export-button:hover {
      background-color: #991b1b;
      color: white !important;
      text-decoration: none;
    }

    .csv-button {
      background-color: #059669;
    }

    .csv-button:hover {
      background-color: #047857;
    }

    .pdf-button {
      background-color: #dc2626;
    }

    .pdf-button:hover {
      background-color: #991b1b;
    }

    .export-button-disabled {
      background-color: #9ca3af !important;
      color: #6b7280 !important;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .export-button-disabled:hover {
      background-color: #9ca3af !important;
      color: #6b7280 !important;
    }

    /* Footer */
    .footer {
      background-color: #1f2937 !important;
      color: #d1d5db !important;
      padding: 20px 30px;
      text-align: center;
      font-size: 14px;
      width: 100%;
      box-sizing: border-box;
    }

    .footer-text {
      margin-bottom: 10px;
      color: #d1d5db !important;
    }

    .company-info {
      font-size: 12px;
      opacity: 0.8;
      color: #d1d5db !important;
    }

    /* Responsive design */
    @media only screen and (max-width: 600px) {
      .email-wrapper {
        margin: 0;
        box-shadow: none;
      }

      .header, .content, .export-section, .footer {
        padding: 20px 15px;
      }

      .title {
        font-size: 24px;
      }

      .stats-value {
        font-size: 28px;
      }

      .export-buttons {
        border-spacing: 10px 10px;
      }

      .export-buttons td {
        display: block;
        text-align: center;
        padding: 5px 0;
      }

      .export-button {
        display: inline-block;
        width: 90%;
        max-width: 200px;
        margin: 0 auto;
      }

      .stats-table {
        font-size: 14px;
      }

      .stats-table th,
      .stats-table td {
        padding: 8px;
      }
    }
  `;
}

/**
 * Get the logo HTML for stats email templates
 */
export function getStatsEmailLogoHtml(logoUrl?: string): string {
  return logoUrl
    ? `<img src="${logoUrl}" alt="AMQ Partners" class="company-logo" />`
    : '<div class="company-name" style="font-size: 24px; font-weight: bold;">AMQ Partners</div>';
}

/**
 * Get the logo URL for stats email templates
 */
export function getStatsEmailLogoUrl(): string {
  try {
    const domain = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    return `${domain}/logo-white.png`;
  } catch (error) {
    console.error('Error generating logo URL:', error);
    return '';
  }
}

/**
 * Process the email template with variable substitution
 */
export function processStatsEmailTemplate(template: string, data: StatsEmailData): string {
  const logoUrl = getStatsEmailLogoUrl();
  const logoHtml = getStatsEmailLogoHtml(logoUrl);

  return template
    .replace(/{{LOGO_PLACEHOLDER}}/g, logoHtml)
    .replace(/{{DATE}}/g, formatDate(data.date))
    .replace(/{{COMPANY_NAME}}/g, 'AMQ Partners')
    .replace(/{{COMPANY_ADDRESS}}/g, '200 rue Principale, local 8, St-Sauveur, QC J0R 1R0');
}

/**
 * Format date for display in French
 */
function formatDate(dateString: string): string {
  try {
    // Parse the date string as local date to avoid timezone shifts
    const [year, month, day] = dateString.split('-').map(Number);
    const date = new Date(year, month - 1, day); // month is 0-indexed
    return date.toLocaleDateString('fr-CA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
}

// Utility function for currency formatting
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('fr-CA', {
    style: 'currency',
    currency: 'CAD'
  }).format(amount);
}

/**
 * Generate weekly stats email template
 */
export function getWeeklyStatsEmailTemplate(data: StatsEmailData): string {
  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rapport hebdomadaire - ${data.date}</title>
      <style>
        ${getEmailStyles()}
      </style>
    </head>
    <body>
      <div class="email-wrapper">
        ${getWeeklyEmailHeader()}
        ${getWeeklyEmailContent(data)}
        ${getEmailFooter(data.exportUrls)}
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate weekly email content with date range
 */
function getWeeklyEmailContent(data: StatsEmailData): string {
  let content = '<div class="content">';

  // Add weekly greeting with date range
  const [startDate, endDate] = data.date.split(' to ');
  const startFormatted = formatDateForDisplay(startDate);
  const endFormatted = formatDateForDisplay(endDate);

  content += `
    <div class="greeting">
      <p>Bonjour,</p>
      <p>Voici le résumé des statistiques pour la semaine du <strong>${startFormatted}</strong> au <strong>${endFormatted}</strong>.</p>
    </div>
  `;

  // Reuse existing statistics sections
  if (data.selectedStats.includes('totalSales')) {
    content += getTotalSalesSection(data.totalSales);
  }

  if (data.selectedStats.includes('totalReservationsScheduled')) {
    content += getTotalReservationsScheduledSection(data.totalReservationsScheduled);
  }

  if (data.selectedStats.includes('totalReservationsCreated')) {
    content += getTotalReservationsCreatedSection(data.totalReservationsCreated);
  }

  if (data.selectedStats.includes('totalPresence')) {
    content += getTotalPresenceSection(data.totalPresence);
  }

  if (data.selectedStats.includes('salesByBranch')) {
    content += getSalesByBranchSection(data.salesByBranch);
  }

  if (data.selectedStats.includes('reservationsByBranch')) {
    content += getReservationsByBranchSection(data.reservationsByBranch);
  }

  if (data.selectedStats.includes('reservationsCreatedByBranch')) {
    content += getReservationsCreatedByBranchSection(data.reservationsCreatedByBranch);
  }

  if (data.selectedStats.includes('presenceByBranch')) {
    content += getPresenceByBranchSection(data.presenceByBranch);
  }

  if (data.selectedStats.includes('topSellers')) {
    content += getTopSellersSection(data.topSellers);
  }

  if (data.selectedStats.includes('topPaps')) {
    content += getTopPapsSection(data.topPaps);
  }

  content += '</div>';
  return content;
}

/**
 * Format date for display in French
 */
function formatDateForDisplay(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-CA', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Process weekly email template with variable substitution
 */
export function processWeeklyStatsEmailTemplate(template: string, data: StatsEmailData): string {
  const [startDate, endDate] = data.date.split(' to ');
  const startFormatted = formatDateForDisplay(startDate);
  const endFormatted = formatDateForDisplay(endDate);

  return template
    .replace(/{{DATE}}/g, `${startFormatted} au ${endFormatted}`)
    .replace(/{{START_DATE}}/g, startFormatted)
    .replace(/{{END_DATE}}/g, endFormatted);
}
