import DashboardAccessToken from '@/models/DashboardAccessToken';
import dbConnect from '@/lib/db';

export interface TokenValidationResult {
  isValid: boolean;
  token?: any;
  error?: string;
}

/**
 * Validates a dashboard access token
 * @param tokenString - The token string to validate
 * @returns Promise<TokenValidationResult>
 */
export async function validateDashboardToken(tokenString: string): Promise<TokenValidationResult> {
  try {
  await dbConnect();
    
    // Find the token
    const token = await DashboardAccessToken.findOne({ 
      token: tokenString,
      isActive: true 
    });
    
    if (!token) {
      return {
        isValid: false,
        error: 'Invalid or inactive token'
      };
    }
    
    // Check if token has expired
    if (token.expiresAt && new Date() > token.expiresAt) {
      return {
        isValid: false,
        error: 'Token has expired'
      };
    }
    
    // Update last used timestamp
    token.lastUsedAt = new Date();
    await token.save();
    
    return {
      isValid: true,
      token
    };
    
  } catch (error: any) {
    console.error('Error validating dashboard token:', error);
    return {
      isValid: false,
      error: 'Token validation failed'
    };
  }
}

/**
 * Extracts token from request headers or query parameters
 * @param req - NextRequest object
 * @returns string | null
 */
export function extractTokenFromRequest(req: any): string | null {
  // Check Authorization header first
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Check query parameter
  const url = new URL(req.url);
  const tokenParam = url.searchParams.get('token');
  if (tokenParam) {
    return tokenParam;
  }

  // Check custom header
  const tokenHeader = req.headers.get('x-dashboard-token');
  if (tokenHeader) {
    return tokenHeader;
  }



  return null;
}
