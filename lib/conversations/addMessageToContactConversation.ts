import { Contact } from '@/app/models/Contact';
import { TwilioMessage } from '@/types/twilio';
import dbConnect from '@/lib/db';
/**
 * Adds a message to a contact's conversation. If the contact does not exist, creates it (and a Twilio conversation) using webhook logic.
 * @param phone - The phone number (E.164 or 10 digits, normalized internally)
 * @param message - The message body
 * @param author - The sender (from)
 * @param direction - Message direction (default: 'outbound-api')
 * @param sid - Optional message SID (if available from Twilio)
 */
export async function addMessageToContactConversation({
  phone,
  message,
  author,
  direction = 'outbound-api',
  sid,
  auto = false,
}: {
  phone: string;
  message: string;
  author: {name: string, id: string};
  direction?: 'inbound' | 'outbound-api';
  sid: string;
  auto?: boolean;
}): Promise<void> {
  if (!phone) return;
  // Normalize phone to 10 digits (remove non-digits, leading 1)
  const normalizedPhone = phone.replace(/\D/g, '').replace(/^1/, '');
  let contact = await Contact.findOne({ phone: normalizedPhone });

  // If contact does not exist, create it (and a Twilio conversation)
  if (!contact) {
    try {
      const db= (await dbConnect()).connection;
      // Try to find a reservation for this phone
      let reservationId: string | null = null;
      let branchId: string | null = null;
      let customerName: string | null = null;
      try {
        // Use the same findReservationByPhone and getReservationDetails as webhook
        const { default: mongoose } = await import('mongoose');
        const { ObjectId } = mongoose.Types;
        const findReservationByPhone = async (db: any, phoneToFind: string): Promise<string | null> => {
          const reservations = await db.collection('reservations').aggregate([
            { $addFields: {
                normalizedPhone: {
                  $let: {
                    vars: {
                      chars: {
                        $map: {
                          input: { $range: [0, { $strLenCP: "$customerInfo.phone" }] },
                          as: "i",
                          in: { $substrCP: ["$customerInfo.phone", "$$i", 1] }
                        }
                      }
                    },
                    in: {
                      $let: {
                        vars: {
                          digitsOnly: {
                            $reduce: {
                              input: "$$chars",
                              initialValue: "",
                              in: {
                                $cond: [
                                  { $in: ["$$this", ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]] },
                                  { $concat: ["$$value", "$$this"] },
                                  "$$value"
                                ]
                              }
                            }
                          }
                        },
                        in: {
                          $cond: [
                            { $eq: [{ $substr: ["$$digitsOnly", 0, 1] }, "1"] },
                            { $substr: ["$$digitsOnly", 1, { $subtract: [{ $strLenCP: "$$digitsOnly" }, 1] }] },
                            "$$digitsOnly"
                          ]
                        }
                      }
                    }
                  }
                }
              }
            },
            { $match: { normalizedPhone: phoneToFind } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
            { $project: { _id: 1 } }
          ]).toArray();
          if (reservations.length > 0) return reservations[0]._id.toString();
          return null;
        };
        const getReservationDetails = async (db: any, reservationId: string): Promise<{ branchId: string | null, customerName: string | null }> => {
          const reservation = await db.collection('reservations').findOne(
            { _id: new ObjectId(reservationId) },
            { projection: { branchId: 1, "customerInfo.client1Name": 1 } }
          );
          let customerName = null;
          if (reservation?.customerInfo?.client1Name) {
            customerName = reservation.customerInfo.client1Name;
          }
          return {
            branchId: reservation?.branchId?.toString() || null,
            customerName: customerName
          };
        };
        reservationId = await findReservationByPhone(db, normalizedPhone);
        if (reservationId) {
          const details = await getReservationDetails(db, reservationId);
          branchId = details.branchId;
          customerName = details.customerName;
        }
      } catch (err) {
        // If reservation lookup fails, fallback to defaults
        branchId = null;
        customerName = null;
      }
      // Create the contact
      contact = await Contact.create({
        conversation: {
          sid: '', // Will update after Twilio conversation is created
          linkedReservationId: reservationId,
          linkedBranch: branchId,
          customerName: customerName,
        },
        phone: normalizedPhone,
        fullname: customerName || '',
      });
      // Create Twilio conversation and update contact
      try {
        const { createTwilioConversation } = await import('@/lib/conversations/createTwilioConversation');
        // Use E.164 for participant/proxy if possible
        let participantAddress = phone.replace(/\D/g, '');
        if (!participantAddress.startsWith('1')) participantAddress = '1' + participantAddress;
        participantAddress = '+' + participantAddress;
        // Use REQUESTS_NUMBER as proxy if available
        const proxyAddress = process.env.REQUESTS_NUMBER ? process.env.REQUESTS_NUMBER : '+10000000000';
        const conversation = await createTwilioConversation({
          friendlyName: `Contact ${normalizedPhone}`,
          contactId: contact._id.toString(),
          participants: [
            { address: participantAddress, proxyAddress },
          ],
        });
        contact.conversation.sid = conversation.sid;
        await contact.save();
      } catch (err) {
        console.error('Failed to create Twilio conversation or contact:', err);
      }
    } catch (err) {
      console.error('Failed to create contact:', err);
      return;
    }
  }
  // If contact exists but has no conversation or no conversation.sid, create Twilio conversation
  if (!contact.conversation || !contact.conversation.sid) {
    if (!contact.conversation) contact.conversation = { sid: '', messages: [] };
    try {
      const { createTwilioConversation } = await import('@/lib/conversations/createTwilioConversation');
      let participantAddress = phone.replace(/\D/g, '');
      if (!participantAddress.startsWith('1')) participantAddress = '1' + participantAddress;
      participantAddress = '+' + participantAddress;
      const proxyAddress = process.env.REQUESTS_NUMBER ? process.env.REQUESTS_NUMBER : '+10000000000';
      const conversation = await createTwilioConversation({
        friendlyName: `Contact ${normalizedPhone}`,
        contactId: contact._id.toString(),
        participants: [
          { address: participantAddress, proxyAddress },
        ],
      });
      contact.conversation.sid = conversation.sid;
      await contact.save();
    } catch (err) {
      console.error('Failed to create Twilio conversation for existing contact:', err);
    }
  }
  // Add the message to the contact's conversation
  if (!contact.conversation.messages) contact.conversation.messages = [];
  const msg: TwilioMessage = {
    sid: sid,
    body: message,
    author: author.name,
    dateCreated: new Date().toISOString(),
    direction,
    archivedAt: undefined,
    archivedBy: [],
  };
  contact.conversation.messages.push(msg);
  contact.lastMessage = auto ? contact.lastMessage : msg;
  if (auto) {
    if (!Array.isArray(contact.conversation.archivedBy)) {
      contact.conversation.archivedBy = [];
    }
    if (!contact.conversation.archivedBy.includes("67eab74ed78036c9f68c7686")) {
      contact.conversation.archivedBy.push("67eab74ed78036c9f68c7686");
    }
  } else {
    contact.conversation.archivedBy = [];

 

  }

  const notificationPayload = {
    type: 'new_outbound_message',
    data: {
      sid: contact.conversation.sid,
      author: author.id,
     },
  };
  await fetch(`${process.env.NEXT_PUBLIC_APP_URL?.endsWith('/')?process.env.NEXT_PUBLIC_APP_URL:process.env.NEXT_PUBLIC_APP_URL+'/'}api/socket/emit`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(notificationPayload),
  });
  await contact.save();
} 