import { initializeAutomation, shutdownAutomation } from './services/automation-init';

// Global initialization flag to prevent multiple initializations
let isInitialized = false;

export function initializeApp() {
  if (isInitialized) {
    console.log('Application already initialized, skipping...');
    return;
  }

  console.log('🚀 Initializing application services...');

  try {
    // Initialize automation services
    initializeAutomation();

    // Set up graceful shutdown handlers (only once)
    if (!process.listeners('SIGTERM').length) {
      setupShutdownHandlers();
    }

    isInitialized = true;
    console.log('✅ Application initialization completed');
  } catch (error) {
    console.error('❌ Application initialization failed:', error);
    throw error;
  }
}

function setupShutdownHandlers() {
  const gracefulShutdown = (signal: string) => {
    try {
      shutdownAutomation();
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // Nodemon restart

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    gracefulShutdown('uncaughtException');
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('unhandledRejection');
  });
}

// Auto-initialize if this is the main module or automation is enabled
// But NOT during build process
if (typeof window === 'undefined' && (process.env.NODE_ENV === 'production' || process.env.ENABLE_AUTOMATION === 'true')) {

  setTimeout(() => {
    initializeApp();
  }, 100);
}
