import dbConnect from './db';
import { sendSMS } from '@/lib/twilio';

// Default SMS templates
const DEFAULT_TEMPLATES = {
  'reservation_assignment': 'Bonjour {{sellerName}}, vous avez été affecté(e) à une réservation pour {{customerName}} le {{appointmentDate}}. Merci de vous connecter à l\'application pour plus de détails.',
  'thank_client': 'Bonjour {{customerName}}, nous vous remercions pour votre visite à {{branch.name}}. Nous espérons que vous avez apprécié l\'expérience et nous serions ravis de vous accueillir à nouveau prochainement!',
  'reservation_confirmation': 'Bonjour {{customerName}}, votre réservation pour le {{appointmentDate}} a été confirmée. Merci pour votre confiance.',
  'reservation_reminder': 'Rappel: vous avez un rendez-vous prévu le {{appointmentDate}}. En cas d\'empêchement, merci de nous contacter.'
};

// Helper function to replace template variables with actual values
export function processTemplate(template: string, data: Record<string, string>): string {
  console.log('template', template);
  console.log('data', data);
  return template.replace(/\{\{([\w.]+)\}\}/g, (match, variable) => {
    const value = data[variable];
    if (value && variable == 'preferences.visitDate') {
      // Check if value is a valid date string or timestamp
      const date = new Date(value);
      if (!isNaN(date.getTime()) && typeof value === 'string' && value.length >= 8) {
        // Format as "11 janv. 2025" (day number, short month, full year, in fr-CA)
        const day = date.getDate();
        const month = date.toLocaleString('fr-CA', { month: 'short' });
        const year = date.getFullYear();
        return `${day} ${month} ${year}`;
      }
      return value;
    }else if (value && variable == 'preferences.visitTime') {
      if (typeof value === 'string' && /^\d{2}:\d{2}-\d{2}:\d{2}$/.test(value)) {
        // Extract the start hour and format as "HHh"
        const startHour = value.split('-')[0].split(':')[0];
        return `${startHour}h`;
      }
      return value;
    } else if (value && variable === 'branch.name') {
      // Special handling for branch.name - split on '-' and take second part if available
      if (typeof value === 'string' && value.includes('-')) {
        try {
          const splitResult = value.split('-');
          return splitResult.length > 1 ? splitResult[1] : value;
        } catch (err) {
          // If split fails, keep original value
          return value;
        }
      }
      return value;
    }
    return data[variable] || match;
  });
}

// Get a template by ID, with fallback to default templates
export async function getTemplate(templateId: string): Promise<string> {
  try {
    const db= (await dbConnect()).connection;
    const templateDoc = await db.collection('sms_templates').findOne({ id: templateId });
    
    if (templateDoc && templateDoc.template) {
      return templateDoc.template;
    }
    
    // Fallback to default templates
    return DEFAULT_TEMPLATES[templateId as keyof typeof DEFAULT_TEMPLATES] || '';
  } catch (error) {
    console.error('Error fetching SMS template:', error);
    // Fallback to default template in case of error
    return DEFAULT_TEMPLATES[templateId as keyof typeof DEFAULT_TEMPLATES] || '';
  }
}

// Send an SMS using a template
export async function sendTemplatedSMS(
  from: string,
  phoneNumber: string, 
  templateId: string, 
  data: Record<string, string>
): Promise<boolean> {
  try {
    // Get the template
    const templateContent = await getTemplate(templateId);
    
    if (!templateContent) {
      console.error(`Template not found: ${templateId}`);
      return false;
    }
    
    // Process the template with the provided data
    const message = processTemplate(templateContent, data);
    
    // Send the SMS
     await sendSMS(from, phoneNumber, message,true);
    
  
    
    return true;
  } catch (error) {
    console.error('Error sending templated SMS:', error);
    return false;
  }
} 